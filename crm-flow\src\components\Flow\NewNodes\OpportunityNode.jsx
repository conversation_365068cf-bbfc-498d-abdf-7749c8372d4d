import { memo } from 'react';
import { Handle, Position } from '@xyflow/react';
import { useCurrency } from '../../../contexts/CurrencyContext';
import NodeMenu from './NodeMenu';

const OpportunityNode = ({ data, selected }) => {
  const { getCurrencySymbol, availableCurrencies } = useCurrency();

  // Get currency symbol based on the opportunity's currency or fall back to user's default
  const getOpportunityCurrencySymbol = () => {
    if (data.currency) {
      const currencyObj = availableCurrencies.find(c => c.code === data.currency);
      return currencyObj ? currencyObj.symbol : '$';
    }
    return getCurrencySymbol(); // Fall back to user's default currency
  };
  const statusColors = {
    'New': 'bg-primary',
    'Qualified': 'bg-info',
    'Proposal': 'bg-warning',
    'Negotiation': 'bg-danger',
    'Closed Won': 'bg-success',
    'Closed Lost': 'bg-secondary',
    'Archived': 'bg-dark'
  };

  const formatDate = (date) => {
    if (!date) return 'Unknown';
    return typeof date === 'object' && date.toDate
      ? date.toDate().toLocaleDateString()
      : new Date(date).toLocaleDateString();
  };

  // Handle node actions
  const handleEdit = () => {
    if (data.onEdit) {
      // Get the node ID (format: "opportunity-id")
      const nodeId = data.id;

      // Extract the actual ID from the node ID
      const parts = nodeId.split('-');
      const actualId = parts.slice(1).join('-'); // Join in case ID contains hyphens

      console.log(`Editing opportunity node: type=opportunity, id=${actualId}`);

      // Create a custom event with the correct format
      const event = new CustomEvent('editNode', {
        detail: { type: 'opportunity', id: actualId }
      });
      window.dispatchEvent(event);
    }
  };

  const handleDelete = () => {
    if (data.onDelete) data.onDelete(data.id);
  };

  const handleArchive = () => {
    if (data.onArchive) data.onArchive(data.id);
  };

  const handleRestore = () => {
    if (data.onRestore) data.onRestore(data.id);
  };

  return (
    <div className={`node-card node-opportunity ${data.archived ? 'node-archived' : ''}`}>
      <NodeMenu
        onEdit={handleEdit}
        onDelete={handleDelete}
        onArchive={handleArchive}
        isArchived={data.archived}
        onRestore={handleRestore}
        nodeType="opportunity"
      />
      <div className="node-header">
        <h6 className="node-title">Opportunity</h6>
        <span className={`node-badge ${statusColors[data.status] || 'bg-primary'}`}>
          {data.status}
        </span>
      </div>
      <div className="node-body">
        <h5 className="node-name">{data.name}</h5>
        <div className="opportunity-value">
          <span className="opportunity-currency">{getOpportunityCurrencySymbol()}</span>
          <span className="opportunity-amount">{data.value ? data.value.toLocaleString() : '0'}</span>
          {data.currency && <span className="opportunity-currency-code">({data.currency})</span>}
        </div>
        <p className="node-detail">Company: {data.company || 'N/A'}</p>
        <p className="node-detail">Close: {data.closeDate ? new Date(data.closeDate).toLocaleDateString() : 'N/A'}</p>
        <p className="node-date">Created: {formatDate(data.createdAt)}</p>
      </div>
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#555' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: '#555' }}
      />
    </div>
  );
};

// Use React.memo to prevent unnecessary re-renders
export default memo(OpportunityNode);
