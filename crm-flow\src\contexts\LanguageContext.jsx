import { createContext, useState, useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { db } from '../services/firebase';
import { doc, updateDoc } from 'firebase/firestore';
import { useAuth } from './AuthContext';

// Create the language context
const LanguageContext = createContext();

// Available languages
export const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'es', name: '<PERSON>spa<PERSON><PERSON>' },
  { code: 'fr', name: 'Français' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>' },
  { code: 'pl', name: '<PERSON><PERSON>' }
];

// Language provider component
export const LanguageProvider = ({ children }) => {
  const { i18n } = useTranslation();
  const { currentUser } = useAuth();
  const [language, setLanguage] = useState(localStorage.getItem('i18nextLng') || 'en');

  // Change language function
  const changeLanguage = async (langCode) => {
    try {
      await i18n.changeLanguage(langCode);
      setLanguage(langCode);
      localStorage.setItem('i18nextLng', langCode);
      
      // If user is logged in, update their language preference in Firestore
      if (currentUser) {
        const userRef = doc(db, 'users', currentUser.uid);
        await updateDoc(userRef, {
          language: langCode,
          updatedAt: new Date()
        });
      }
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  // Sync with user's saved preference when they log in
  useEffect(() => {
    const syncUserLanguage = async () => {
      if (currentUser && currentUser.language && currentUser.language !== language) {
        await changeLanguage(currentUser.language);
      }
    };
    
    syncUserLanguage();
  }, [currentUser]);

  // Context value
  const value = {
    language,
    changeLanguage,
    languages: LANGUAGES
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

LanguageProvider.propTypes = {
  children: PropTypes.node.isRequired
};

// Custom hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;
