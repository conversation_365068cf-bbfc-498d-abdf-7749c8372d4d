import { initializeApp } from "firebase/app";
import { getA<PERSON>, GoogleAuthProvider } from "firebase/auth";
import {
  getFirestore,
  enableIndexedDbPersistence,
  CACHE_SIZE_UNLIMITED,
  initializeFirestore,
  persistentLocalCache,
  persistentMultipleTabManager,
  enableMultiTabIndexedDbPersistence
} from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { environment } from '../config/environment';

// Use Firebase configuration from environment settings
const firebaseConfig = environment.firebase;

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore with optimized settings based on environment
const db = initializeFirestore(app, {
  localCache: persistentLocalCache({
    tabManager: persistentMultipleTabManager(),
    cacheSizeBytes: environment.cache.maxCacheSize === 'unlimited' ?
      CACHE_SIZE_UNLIMITED : environment.cache.maxCacheSize
  })
});

// Enable offline persistence (with error handling)
const enableOfflinePersistence = async () => {
  // Skip if offline persistence is disabled in environment settings
  if (!environment.app.enableOfflinePersistence) {
    console.log('Offline persistence disabled in environment settings');
    return;
  }

  try {
    // Use multi-tab persistence if available
    try {
      await enableMultiTabIndexedDbPersistence(db);
      console.log('Multi-tab offline persistence enabled');
    } catch (e) {
      // Fall back to single-tab persistence
      await enableIndexedDbPersistence(db);
      console.log('Single-tab offline persistence enabled');
    }
  } catch (error) {
    if (error.code === 'failed-precondition') {
      // Multiple tabs open, persistence can only be enabled in one tab at a time
      console.warn('Multiple tabs open, persistence only enabled in one tab');
    } else if (error.code === 'unimplemented') {
      // The current browser does not support all of the features required for persistence
      console.warn('Offline persistence not supported in this browser');
    } else {
      console.error('Error enabling offline persistence:', error);
    }
  }
};

// Initialize Auth
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

// Initialize Storage
const storage = getStorage(app);

// Export the initialized services
export {
  app,
  auth,
  db,
  storage,
  googleProvider,
  enableOfflinePersistence
};

// Call enableOfflinePersistence immediately
enableOfflinePersistence();
