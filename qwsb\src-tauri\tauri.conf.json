{"$schema": "https://schema.tauri.app/config/2", "productName": "QuakeWorld Server Browser", "version": "0.1.0", "identifier": "com.qwsb.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1422", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "QuakeWorld Server Browser", "width": 1024, "height": 768, "minWidth": 800, "minHeight": 600, "resizable": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}