import { useState, useEffect, useRef } from 'react';
import { doc, updateDoc } from 'firebase/firestore';
import { addUserDocument } from '../../services/dataAccessService';
import { db } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';
import 'bootstrap/dist/css/bootstrap.min.css';

const ChecklistForm = ({ onClose, onSuccess, checklistData = null, isEditing = false }) => {
  const { currentUser, organization } = useAuth();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    items: []
  });
  const [newItem, setNewItem] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [editingItemId, setEditingItemId] = useState(null);
  const [editText, setEditText] = useState('');
  const editInputRef = useRef(null);

  // Initialize form with existing data if editing
  useEffect(() => {
    if (isEditing && checklistData) {
      setFormData({
        title: checklistData.title || '',
        description: checklistData.description || '',
        items: Array.isArray(checklistData.items) ? [...checklistData.items] : []
      });
    }
  }, [isEditing, checklistData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleAddItem = () => {
    if (!newItem.trim()) return;

    const newItemObj = {
      id: Date.now().toString(), // Simple unique ID
      text: newItem.trim(),
      completed: false,
      createdAt: new Date()
    };

    setFormData({
      ...formData,
      items: [...formData.items, newItemObj]
    });

    setNewItem('');
  };

  const handleEditItem = (itemId, currentText) => {
    setEditingItemId(itemId);
    setEditText(currentText);
    // Focus the input after it renders
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 50);
  };

  const handleSaveEdit = (itemId) => {
    if (!editText.trim()) {
      return handleCancelEdit();
    }

    setFormData({
      ...formData,
      items: formData.items.map(item => {
        if (item.id === itemId) {
          return { ...item, text: editText.trim() };
        }
        return item;
      })
    });

    // Reset editing state
    setEditingItemId(null);
    setEditText('');
  };

  const handleCancelEdit = () => {
    setEditingItemId(null);
    setEditText('');
  };

  const handleRemoveItem = (itemId) => {
    setFormData({
      ...formData,
      items: formData.items.filter(item => item.id !== itemId)
    });
  };

  const handleToggleItem = (itemId) => {
    setFormData({
      ...formData,
      items: formData.items.map(item => {
        if (item.id === itemId) {
          return { ...item, completed: !item.completed };
        }
        return item;
      })
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      // Validate form
      if (!formData.title.trim()) {
        throw new Error('Title is required');
      }

      // Prepare data for saving
      const checklistData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description.trim(),
        updatedAt: new Date()
      };

      let docId;

      if (isEditing && (checklistData.id || checklistData.id === '')) {
        // Update existing checklist
        // Extract the actual ID from the node ID (format: "checklist-id") if needed
        let checklistId = checklistData.id;

        // If the ID is in the format "checklist-id", extract the actual ID
        if (checklistId && checklistId.startsWith('checklist-')) {
          checklistId = checklistId.split('-')[1];
        }

        console.log('Updating checklist with ID:', checklistId);

        // Remove id from the data object before updating
        const { id, ...dataToUpdate } = checklistData;
        const checklistRef = doc(db, 'checklists', checklistId);
        await updateDoc(checklistRef, dataToUpdate);
        docId = checklistId;
      } else {
        // Create new checklist using the dataAccessService
        console.log('Creating new checklist with data:', checklistData);

        // Use the addUserDocument function to add the checklist with proper context
        const result = await addUserDocument('checklists', checklistData, currentUser.uid, organization?.id);

        if (result.success) {
          docId = result.id;
          console.log('Checklist created with ID:', docId);
        } else {
          throw new Error(result.error || 'Failed to create checklist');
        }
      }

      // Show success message
      setSuccess(true);

      // Reset form if not editing
      if (!isEditing) {
        setFormData({
          title: '',
          description: '',
          items: []
        });
      }

      // Call onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess(docId);
        }, 1500);
      }
    } catch (error) {
      console.error('Error saving checklist:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="card shadow border-0">
      <div className="card-header bg-info text-white">
        <h5 className="mb-0">{isEditing ? 'Edit Checklist' : 'Create New Checklist'}</h5>
      </div>
      <div className="card-body">
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success" role="alert">
            Checklist {isEditing ? 'updated' : 'created'} successfully!
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label htmlFor="title" className="form-label">Title *</label>
            <input
              type="text"
              className="form-control"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Enter checklist title"
              required
            />
          </div>

          <div className="mb-3">
            <label htmlFor="description" className="form-label">Description</label>
            <textarea
              className="form-control"
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter checklist description"
              rows="2"
            />
          </div>

          <div className="mb-4">
            <label className="form-label">Checklist Items</label>
            <div className="input-group mb-2">
              <input
                type="text"
                className="form-control"
                placeholder="Add new item"
                value={newItem}
                onChange={(e) => setNewItem(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddItem())}
              />
              <button
                type="button"
                className="btn btn-outline-primary"
                onClick={handleAddItem}
                disabled={!newItem.trim()}
              >
                <i className="bi bi-plus"></i> Add
              </button>
            </div>

            <div className="checklist-items border rounded p-2" style={{ maxHeight: '200px', overflowY: 'auto' }}>
              {formData.items.length > 0 ? (
                <ul className="list-group list-group-flush">
                  {formData.items.map((item, index) => (
                    <li key={item.id} className="list-group-item px-2 py-2 d-flex align-items-center">
                      {editingItemId === item.id ? (
                        <div className="input-group flex-grow-1">
                          <input
                            ref={editInputRef}
                            type="text"
                            className="form-control"
                            value={editText}
                            onChange={(e) => setEditText(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit(item.id)}
                          />
                          <button
                            type="button"
                            className="btn btn-outline-success"
                            onClick={() => handleSaveEdit(item.id)}
                          >
                            <i className="bi bi-check"></i>
                          </button>
                          <button
                            type="button"
                            className="btn btn-outline-secondary"
                            onClick={handleCancelEdit}
                          >
                            <i className="bi bi-x"></i>
                          </button>
                        </div>
                      ) : (
                        <>
                          <div className="form-check flex-grow-1 mb-0">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={item.completed}
                              onChange={() => handleToggleItem(item.id)}
                              id={`form-item-${item.id}`}
                            />
                            <label
                              className="form-check-label"
                              htmlFor={`form-item-${item.id}`}
                              style={{
                                textDecoration: item.completed ? 'line-through' : 'none',
                                color: item.completed ? '#6c757d' : 'inherit'
                              }}
                            >
                              {item.text}
                            </label>
                          </div>
                          <div className="btn-group btn-group-sm">
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-primary me-1"
                              onClick={() => handleEditItem(item.id, item.text)}
                              title="Edit item"
                            >
                              <i className="bi bi-pencil"></i>
                            </button>
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-danger"
                              onClick={() => handleRemoveItem(item.id)}
                              title="Delete item"
                            >
                              <i className="bi bi-trash"></i>
                            </button>
                          </div>
                        </>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-muted text-center my-3">No items added yet</p>
              )}
            </div>
          </div>

          <div className="d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Saving...
                </>
              ) : (
                isEditing ? 'Update Checklist' : 'Create Checklist'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChecklistForm;
