use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::fs::{self, File};
use std::io::{Read, Write};
use std::path::PathBuf;
use tauri::Manager;

use crate::master::MasterServer;
use crate::quakeworld::ServerInfo;

// Database connection wrapper
pub struct Database {
    app_handle: tauri::AppHandle,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FavoriteServer {
    pub id: Option<i64>,
    pub address: String,
    pub port: u16,
    pub name: Option<String>,
    pub notes: Option<String>,
    pub added_at: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AppSettings {
    pub auto_refresh: bool,
    pub refresh_interval: i64, // in seconds
    pub dark_mode: bool,
    pub show_empty_servers: bool,
    pub show_full_servers: bool,
    pub ping_timeout: i64, // in milliseconds
}

impl Default for AppSettings {
    fn default() -> Self {
        AppSettings {
            auto_refresh: false,
            refresh_interval: 300, // 5 minutes
            dark_mode: true,
            show_empty_servers: true,
            show_full_servers: true,
            ping_timeout: 3000, // 3 seconds
        }
    }
}

impl Database {
    // Initialize a new database connection
    pub fn new(app_handle: tauri::AppHandle) -> Result<Self> {
        Ok(Database { app_handle })
    }

    // Get the data directory for the app
    fn get_data_dir(&self) -> Result<PathBuf> {
        let app_dir = self.app_handle.path().app_data_dir()?;

        // Create the directory if it doesn't exist
        if !app_dir.exists() {
            fs::create_dir_all(&app_dir)?;
        }

        Ok(app_dir)
    }

    // Save servers to a file
    pub fn save_servers(&self, servers: &[ServerInfo]) -> Result<()> {
        let data_dir = self.get_data_dir()?;
        let servers_file = data_dir.join("servers.json");

        // Serialize the servers to JSON
        let json = serde_json::to_string_pretty(servers)?;

        // Write to file
        let mut file = File::create(servers_file)?;
        file.write_all(json.as_bytes())?;

        Ok(())
    }

    // Load servers from a file
    pub fn load_servers(&self) -> Result<Vec<ServerInfo>> {
        let data_dir = self.get_data_dir()?;
        let servers_file = data_dir.join("servers.json");

        // Check if the file exists
        if !servers_file.exists() {
            return Ok(Vec::new());
        }

        // Read the file
        let mut file = File::open(servers_file)?;
        let mut contents = String::new();
        file.read_to_string(&mut contents)?;

        // Deserialize the JSON
        let servers: Vec<ServerInfo> = serde_json::from_str(&contents)?;

        Ok(servers)
    }

    // Add default master servers if none exist
    pub fn add_default_master_servers(&self) -> Result<()> {
        // For now, we'll just return Ok
        Ok(())
    }

    // Get all master servers
    pub fn get_master_servers(&self) -> Result<Vec<MasterServer>> {
        // Return default master servers
        let mut servers = Vec::new();
        for server_str in crate::master::DEFAULT_MASTER_SERVERS.iter() {
            if let Some(server) = MasterServer::from_address_string(server_str) {
                servers.push(server);
            }
        }
        Ok(servers)
    }

    // Add a master server
    pub fn add_master_server(&self, _server: &MasterServer) -> Result<()> {
        // For now, we'll just return Ok
        Ok(())
    }

    // Update a master server
    pub fn update_master_server(&self, _server: &MasterServer) -> Result<()> {
        // For now, we'll just return Ok
        Ok(())
    }

    // Delete a master server
    pub fn delete_master_server(&self, _address: &str, _port: u16) -> Result<()> {
        // For now, we'll just return Ok
        Ok(())
    }

    // Add a favorite server
    pub fn add_favorite_server(&self, _server: &FavoriteServer) -> Result<i64> {
        // For now, we'll just return 0
        Ok(0)
    }

    // Get all favorite servers
    pub fn get_favorite_servers(&self) -> Result<Vec<FavoriteServer>> {
        // For now, we'll just return an empty vector
        Ok(Vec::new())
    }

    // Check if a server is a favorite
    pub fn is_favorite_server(&self, _address: &str, _port: u16) -> Result<bool> {
        // For now, we'll just return false
        Ok(false)
    }

    // Remove a favorite server
    pub fn remove_favorite_server(&self, _address: &str, _port: u16) -> Result<()> {
        // For now, we'll just return Ok
        Ok(())
    }

    // Get application settings
    pub fn get_settings(&self) -> Result<AppSettings> {
        // For now, we'll just return default settings
        Ok(AppSettings::default())
    }

    // Save application settings
    pub fn save_settings(&self, _settings: &AppSettings) -> Result<()> {
        // For now, we'll just return Ok
        Ok(())
    }
}
