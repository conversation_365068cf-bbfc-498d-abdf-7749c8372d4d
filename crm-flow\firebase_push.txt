# Push to Firebase

# Initialize Firebase (if not already done)
firebase login
firebase init
# Select Hosting, Firestore, and Storage
# Choose your project (janusz-8d5dc)
# Accept default options for most prompts

# Deploy to Firebase
npm run build
firebase deploy

# Build for Production

# Create a production build
npm run build

# Test the production build locally (optional)
npm install -g serve
serve -s dist

# Deploy the build
# To Firebase:
firebase deploy --only hosting
# To other hosting: Upload the contents of the `dist` folder to your web server