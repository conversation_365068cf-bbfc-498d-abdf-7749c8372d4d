import { memo } from 'react';

const NewArchivedPanel = ({ archivedOpportunities, onRestore, onClose }) => {
  const formatDate = (date) => {
    if (!date) return 'Unknown';
    return typeof date === 'object' && date.toDate
      ? date.toDate().toLocaleDateString()
      : new Date(date).toLocaleDateString();
  };

  return (
    <div className="archived-panel">
      <div className="archived-panel-header">
        <h5 className="archived-panel-title">Archived Opportunities</h5>
        <button className="archived-panel-close" onClick={onClose}>
          <i className="bi bi-x"></i>
        </button>
      </div>
      <div className="archived-panel-body">
        {archivedOpportunities.length > 0 ? (
          <ul className="archived-list">
            {archivedOpportunities.map((opportunity) => (
              <li key={opportunity.id} className="archived-item">
                <div className="archived-item-content">
                  <div className="archived-item-title">{opportunity.name}</div>
                  <div className="archived-item-details">
                    <span className="me-2">Value: ${opportunity.value?.toLocaleString() || '0'}</span>
                    <span className="me-2">Status: {opportunity.status}</span>
                    <span>Archived: {formatDate(opportunity.archivedAt)}</span>
                  </div>
                </div>
                <button
                  className="archived-item-restore"
                  onClick={() => onRestore(opportunity.id)}
                >
                  Restore
                </button>
              </li>
            ))}
          </ul>
        ) : (
          <div className="p-3 text-center text-muted">
            No archived opportunities found
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(NewArchivedPanel);
