{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../../src/common.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AAujBjC,oDAoGC;AAtpBD,oDAA4B;AAE5B,0DAA8B;AAE9B,MAAM,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC;AA8BrB;;;;GAIG;AACU,QAAA,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,eAAe,CAAC,CAAC;AAE1E,MAAa,WAA2C,SAAQ,KAAK;IA6E1D;IACA;IA7ET;;;;;;;;;;;;;;;;;;OAkBG;IACH,IAAI,CAAmB;IACvB;;;;;;OAMG;IACH,MAAM,CAAU;IAEhB;;;;;;;;;;;OAWG;IACH,KAAK,CAAiC;IAEtC;;;;;;;;OAQG;IACH,CAAC,2BAAmB,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;IAEpC;;;;;OAKG;IACH,MAAM,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAiB;QAC3C,IACE,QAAQ;YACR,OAAO,QAAQ,KAAK,QAAQ;YAC5B,2BAAmB,IAAI,QAAQ;YAC/B,QAAQ,CAAC,2BAAmB,CAAC,KAAK,GAAG,CAAC,OAAO,EAC7C,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,qBAAqB;QACrB,OAAO,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC5E,CAAC;IAED,YACE,OAAe,EACR,MAA6B,EAC7B,QAA4B,EACnC,KAAe;QAEf,KAAK,CAAC,OAAO,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC;QAJjB,WAAM,GAAN,MAAM,CAAuB;QAC7B,aAAQ,GAAR,QAAQ,CAAoB;QAKnC,IAAI,CAAC,KAAK,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAExD,+CAA+C;QAC/C,6CAA6C;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAA,gBAAM,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAA,gBAAM,EAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC;gBACH,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,aAAa,CAChC,IAAI,CAAC,MAAM,CAAC,YAAY;gBACxB,uDAAuD;gBACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,CAC1D,CAAC;YACJ,CAAC;YAAC,MAAM,CAAC;gBACP,qDAAqD;gBACrD,oEAAoE;gBACpE,0DAA0D;YAC5D,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACrC,CAAC;QAED,IAAI,KAAK,YAAY,YAAY,EAAE,CAAC;YAClC,oDAAoD;YACpD,6CAA6C;YAC7C,qEAAqE;YACrE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACzB,CAAC;aAAM,IACL,KAAK;YACL,OAAO,KAAK,KAAK,QAAQ;YACzB,MAAM,IAAI,KAAK;YACf,CAAC,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,EAClE,CAAC;YACD,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACH,MAAM,CAAC,2BAA2B,CAChC,GAA4B,EAC5B,mBAAmB,GAAG,oBAAoB;QAE1C,IAAI,OAAO,GAAG,mBAAmB,CAAC;QAElC,oCAAoC;QACpC,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;QACrB,CAAC;QAED,IACE,GAAG,CAAC,IAAI;YACR,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ;YAC5B,OAAO,IAAI,GAAG,CAAC,IAAI;YACnB,GAAG,CAAC,IAAI,CAAC,KAAK;YACd,CAAC,GAAG,CAAC,EAAE,EACP,CAAC;YACD,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,OAAO;oBACL,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK;oBACvB,IAAI,EAAE,GAAG,CAAC,MAAM;oBAChB,MAAM,EAAE,GAAG,CAAC,UAAU;iBACvB,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,mCAAmC;gBACnC,OAAO;oBACL,SAAS,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK;wBAC3B,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,QAAQ;wBACxC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;wBACxB,CAAC,CAAC,OAAO,CAAC;gBAEd,iCAAiC;gBACjC,MAAM,MAAM,GACV,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK;oBAC1B,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,QAAQ;oBACvC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;oBACvB,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC;gBAErB,+BAA+B;gBAC/B,MAAM,IAAI,GACR,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ;oBACjE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;oBACrB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;gBAEjB,IACE,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK;oBAC1B,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EACpC,CAAC;oBACD,MAAM,aAAa,GAAa,EAAE,CAAC;oBAEnC,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBACtC,IACE,OAAO,CAAC,KAAK,QAAQ;4BACrB,SAAS,IAAI,CAAC;4BACd,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,EAC7B,CAAC;4BACD,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;wBAChC,CAAC;oBACH,CAAC;oBAED,OAAO,MAAM,CAAC,MAAM,CAClB;wBACE,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO;wBAC5C,IAAI;wBACJ,MAAM;qBACP,EACD,GAAG,CAAC,IAAI,CAAC,KAAK,CACf,CAAC;gBACJ,CAAC;gBAED,OAAO,MAAM,CAAC,MAAM,CAClB;oBACE,OAAO;oBACP,IAAI;oBACJ,MAAM;iBACP,EACD,GAAG,CAAC,IAAI,CAAC,KAAK,CACf,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO;YACP,IAAI,EAAE,GAAG,CAAC,MAAM;YAChB,MAAM,EAAE,GAAG,CAAC,UAAU;SACvB,CAAC;IACJ,CAAC;CACF;AAhOD,kCAgOC;AA+QD,SAAS,aAAa,CACpB,YAAgC,EAChC,IAAwB;IAExB,QAAQ,YAAY,EAAE,CAAC;QACrB,KAAK,QAAQ;YACX,OAAO,IAAI,CAAC;QACd,KAAK,MAAM;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1C,KAAK,aAAa;YAChB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QACxD,KAAK,MAAM;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACjC;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,oBAAoB,CAGlC,IAAgC;IAChC,MAAM,MAAM,GACV,0EAA0E,CAAC;IAE7E,SAAS,aAAa,CAAC,OAAiB;QACtC,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;YACzB,iCAAiC;YACjC,gCAAgC;YAChC,sDAAsD;YACtD,IACE,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC7B,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC5B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;gBAEnB,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,YAAY,CAAkB,GAAM,EAAE,GAAY;QACzD,IACE,OAAO,GAAG,KAAK,QAAQ;YACvB,GAAG,KAAK,IAAI;YACZ,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,EAC5B,CAAC;YACD,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAW,CAAC;YAEhC,IACE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;gBACzB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;gBACxB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EACpB,CAAC;gBACA,GAAG,CAAC,GAAG,CAAQ,GAAG,MAAM,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS,YAAY,CAA0B,GAAa;QAC1D,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;aAAM,IACL,GAAG,YAAY,QAAQ;YACvB,GAAG,YAAY,eAAe;YAC9B,gDAAgD;YAChD,CAAC,SAAS,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,EAClC,CAAC;YACA,GAAkC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;gBACrD,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACnE,GAAkC,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,YAAY,IAAI,GAAG,EAAE,CAAC;gBACxB,GAAG,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;YAC7B,CAAC;YAED,IAAI,WAAW,IAAI,GAAG,EAAE,CAAC;gBACvB,GAAG,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;YAC5B,CAAC;YAED,IAAI,eAAe,IAAI,GAAG,EAAE,CAAC;gBAC3B,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE/B,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,oBAAoB,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAC,CAAC,CAAC;QACrD,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAErC,uDAAuD;QACvD,IAAK,IAAI,CAAC,QAA2B,CAAC,QAAQ,EAAE,CAAC;YAC/C,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACpC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}