import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy, deleteDoc, doc, updateDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import { useAuth } from '../contexts/AuthContext';
import { getUserData } from '../services/dataAccessService';
import Modal from '../components/UI/Modal';
import ConfirmDialog from '../components/Common/ConfirmDialog';
import TaskForm from '../components/Forms/TaskForm';

const TasksPage = () => {
  const { currentUser, organization } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentTask, setCurrentTask] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState(null);

  // Fetch tasks
  const fetchTasks = async () => {
    try {
      setLoading(true);
      console.log('Fetching tasks for user:', currentUser.uid);

      // Get organization ID if available
      const orgId = organization?.id || null;
      console.log('Organization ID:', orgId);

      // Use the getUserData function to get tasks
      const tasksList = await getUserData('tasks', currentUser.uid, orgId);
      console.log('Raw tasks list:', tasksList);

      // Sort tasks by due date
      const sortedTasks = tasksList.sort((a, b) => {
        const dateA = a.dueDate ? new Date(a.dueDate) : new Date(9999, 11, 31);
        const dateB = b.dueDate ? new Date(b.dueDate) : new Date(9999, 11, 31);
        return dateA - dateB;
      });

      console.log('Sorted tasks:', sortedTasks);
      setTasks(sortedTasks);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchTasks();
    }
  }, [currentUser, organization]);

  // Show confirmation dialog for task deletion
  const handleDeleteTask = (id) => {
    setTaskToDelete(id);
    setShowConfirmDialog(true);
  };

  // Confirm task deletion
  const confirmDeleteTask = async () => {
    try {
      if (!taskToDelete) return;

      await deleteDoc(doc(db, 'tasks', taskToDelete));

      // Refresh the tasks list
      fetchTasks();

      // Close the confirmation dialog
      setShowConfirmDialog(false);
      setTaskToDelete(null);
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  };

  // Cancel task deletion
  const cancelDeleteTask = () => {
    setShowConfirmDialog(false);
    setTaskToDelete(null);
  };

  // Handle task edit
  const handleEditTask = (task) => {
    setCurrentTask(task);
    setShowEditModal(true);
  };

  // Handle task status toggle
  const handleToggleStatus = async (task) => {
    try {
      const taskRef = doc(db, 'tasks', task.id);
      const newStatus = task.status === 'Completed' ? 'Not Started' : 'Completed';
      await updateDoc(taskRef, {
        status: newStatus,
        updatedAt: new Date()
      });
      // Refresh the tasks list
      fetchTasks();
    } catch (error) {
      console.error('Error updating task status:', error);
    }
  };

  // Handle sending task to flow view
  const handleSendToFlow = async (task) => {
    try {
      const taskRef = doc(db, 'tasks', task.id);

      // Update data to include organization context
      const updateData = {
        archived: false, // Make sure it's not archived
        updatedAt: new Date()
      };

      // If part of an organization, ensure the organizationId is set
      if (organization?.id) {
        updateData.organizationId = organization.id;
      }

      // Update the document
      await updateDoc(taskRef, updateData);

      // Refresh tasks list
      fetchTasks();
    } catch (error) {
      console.error('Error sending task to flow:', error);
    }
  };

  // Filter and search tasks
  const filteredTasks = tasks.filter(task => {
    // Apply status filter
    if (filter !== 'all' && task.status !== filter) {
      return false;
    }

    // Apply search term
    return (
      task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (task.description && task.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  });

  // Get priority badge color
  const getPriorityBadgeColor = (priority) => {
    switch (priority) {
      case 'High':
        return 'bg-danger';
      case 'Medium':
        return 'bg-warning';
      case 'Low':
        return 'bg-info';
      default:
        return 'bg-secondary';
    }
  };

  // Check if a task is overdue
  const isOverdue = (dueDate, status) => {
    return status !== 'Completed' && new Date(dueDate) < new Date();
  };

  return (
    <div style={{ height: '100%', width: '100%', overflow: 'auto', padding: '20px' }}>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h2>Tasks</h2>
          <button
            className="btn btn-warning"
            onClick={() => setShowAddModal(true)}
          >
            <i className="bi bi-plus-circle me-2"></i>
            Add Task
          </button>
        </div>

        {/* Filters and Search */}
        <div className="row mb-4">
          <div className="col-md-6">
            <div className="input-group">
              <span className="input-group-text">
                <i className="bi bi-search"></i>
              </span>
              <input
                type="text"
                className="form-control"
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="btn btn-outline-secondary"
                  type="button"
                  onClick={() => setSearchTerm('')}
                >
                  <i className="bi bi-x"></i>
                </button>
              )}
            </div>
          </div>
          <div className="col-md-6">
            <div className="btn-group float-end">
              <button
                className={`btn ${filter === 'all' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setFilter('all')}
              >
                All
              </button>
              <button
                className={`btn ${filter === 'Not Started' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setFilter('Not Started')}
              >
                Not Started
              </button>
              <button
                className={`btn ${filter === 'In Progress' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setFilter('In Progress')}
              >
                In Progress
              </button>
              <button
                className={`btn ${filter === 'Completed' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setFilter('Completed')}
              >
                Completed
              </button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="d-flex justify-content-center mt-5">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : (
          <>
            {filteredTasks.length === 0 ? (
              <div className="alert alert-info">
                {searchTerm || filter !== 'all' ? 'No tasks match your filters.' : 'No tasks found. Add your first task!'}
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-sm table-hover">
                  <thead className="table-light">
                    <tr>
                      <th style={{ width: '30px' }}></th>
                      <th>Title</th>
                      <th style={{ width: '100px' }}>Due Date</th>
                      <th style={{ width: '80px' }}>Priority</th>
                      <th style={{ width: '100px' }}>Status</th>
                      <th style={{ width: '120px' }}>Related To</th>
                      <th style={{ width: '140px' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTasks.map(task => (
                      <tr key={task.id} className={isOverdue(task.dueDate, task.status) ? 'table-danger' : ''}>
                        <td>
                          <div className="form-check">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={task.status === 'Completed'}
                              onChange={() => handleToggleStatus(task)}
                            />
                          </div>
                        </td>
                        <td>
                          <span className={task.status === 'Completed' ? 'text-decoration-line-through text-muted' : ''}>
                            {task.title}
                          </span>
                        </td>
                        <td>
                          <small className={isOverdue(task.dueDate, task.status) ? 'text-danger fw-bold' : ''}>
                            {task.dueDate ? new Date(task.dueDate).toLocaleDateString() : '-'}
                          </small>
                        </td>
                        <td>
                          <span className={`badge ${getPriorityBadgeColor(task.priority)}`}>
                            {task.priority}
                          </span>
                        </td>
                        <td>
                          <small>{task.status}</small>
                        </td>
                        <td>
                          <small>
                            {task.relatedTo && task.relatedTo.type && task.relatedTo.id ? (
                              <span>
                                {task.relatedTo.type.charAt(0).toUpperCase() + task.relatedTo.type.slice(1)}
                              </span>
                            ) : (
                              '-'
                            )}
                          </small>
                        </td>
                        <td>
                          <div className="btn-group btn-group-sm">
                            <button
                              className="btn btn-sm btn-outline-primary"
                              title="Edit"
                              onClick={() => handleEditTask(task)}
                            >
                              <i className="bi bi-pencil"></i>
                            </button>
                            <button
                              className="btn btn-sm btn-outline-success"
                              title="Send to Flow"
                              onClick={() => handleSendToFlow(task)}
                            >
                              <i className="bi bi-diagram-3"></i>
                            </button>
                            <button
                              className="btn btn-sm btn-outline-danger"
                              title="Delete"
                              onClick={() => handleDeleteTask(task.id)}
                            >
                              <i className="bi bi-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </>
        )}
      </div>

      {/* Add Task Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="lg"
      >
        <TaskForm
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            fetchTasks();
          }}
        />
      </Modal>

      {/* Edit Task Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="lg"
      >
        {currentTask && (
          <TaskForm
            onClose={() => setShowEditModal(false)}
            onSuccess={() => {
              setShowEditModal(false);
              fetchTasks();
            }}
            taskData={currentTask}
            isEditing={true}
          />
        )}
      </Modal>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        show={showConfirmDialog}
        title="Delete Task"
        message="Are you sure you want to delete this task? This action cannot be undone."
        onConfirm={confirmDeleteTask}
        onCancel={cancelDeleteTask}
        confirmText="Delete"
        cancelText="Cancel"
      />
    </div>
  );
};

export default TasksPage;
