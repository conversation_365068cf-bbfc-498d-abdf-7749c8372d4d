/**
 * Batch processing utilities for Firebase operations
 */
import { writeBatch, doc } from 'firebase/firestore';
import { db } from '../services/firebase';
import { cacheService } from '../services/cacheService';

// Maximum batch size for Firestore (500 is the limit)
const MAX_BATCH_SIZE = 450;

/**
 * Process an array of operations in batches
 * @param {Array} items - Array of items to process
 * @param {Function} processFn - Function to process each item
 * @param {number} batchSize - Maximum batch size
 * @returns {Promise<Array>} - Results of all operations
 */
export const processBatches = async (items, processFn, batchSize = MAX_BATCH_SIZE) => {
  if (!items || !Array.isArray(items) || items.length === 0) {
    return [];
  }
  
  const results = [];
  
  // Process items in batches
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await processFn(batch);
    results.push(...batchResults);
  }
  
  return results;
};

/**
 * Batch write operations to Firestore
 * @param {Array} operations - Array of operations, each with collection, id, and data
 * @param {string} userId - Current user ID for metadata
 * @returns {Promise<Object>} - Result of the batch operation
 */
export const batchWriteToFirestore = async (operations, userId) => {
  if (!operations || !Array.isArray(operations) || operations.length === 0) {
    return {
      success: false,
      error: 'No operations provided'
    };
  }
  
  try {
    // Track collections that need cache invalidation
    const collectionsToInvalidate = new Set();
    const documentsToInvalidate = [];
    
    // Process operations in batches
    const results = await processBatches(operations, async (batch) => {
      const firestoreBatch = writeBatch(db);
      const batchResults = [];
      
      for (const op of batch) {
        const { collection: collectionName, id, data, operation = 'update' } = op;
        
        if (!collectionName || !id) {
          batchResults.push({
            success: false,
            error: 'Missing collection or id',
            operation: op
          });
          continue;
        }
        
        // Add to collections to invalidate
        collectionsToInvalidate.add(collectionName);
        documentsToInvalidate.push({ collection: collectionName, id });
        
        // Get document reference
        const docRef = doc(db, collectionName, id);
        
        // Add metadata
        const updatedData = {
          ...data,
          updatedAt: new Date(),
          updatedBy: userId
        };
        
        // Add to batch based on operation type
        switch (operation) {
          case 'set':
            firestoreBatch.set(docRef, updatedData);
            break;
          case 'update':
            firestoreBatch.update(docRef, updatedData);
            break;
          case 'delete':
            firestoreBatch.delete(docRef);
            break;
          default:
            firestoreBatch.update(docRef, updatedData);
        }
        
        batchResults.push({
          success: true,
          collection: collectionName,
          id,
          operation
        });
      }
      
      // Commit the batch
      await firestoreBatch.commit();
      
      return batchResults;
    });
    
    // Invalidate caches
    collectionsToInvalidate.forEach(collection => {
      cacheService.invalidateCollection(collection);
    });
    
    documentsToInvalidate.forEach(doc => {
      cacheService.invalidateDocument(doc.collection, doc.id);
    });
    
    return {
      success: true,
      message: `Processed ${results.length} operations in batches`,
      results
    };
  } catch (error) {
    console.error('Error in batch write operation:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
