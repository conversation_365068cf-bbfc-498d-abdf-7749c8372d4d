import { useState, useEffect } from 'react';
import { collection, addDoc, getDocs, doc, updateDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';
import { useCurrency } from '../../contexts/CurrencyContext';
import 'bootstrap/dist/css/bootstrap.min.css';

const OpportunityForm = ({ onClose, onSuccess, opportunityData = null, isEditing = false }) => {
  const { currentUser, organization } = useAuth();
  const { getCurrencySymbol, availableCurrencies } = useCurrency();

  // Get currency symbol based on the form's selected currency
  const getFormCurrencySymbol = () => {
    const currencyObj = availableCurrencies.find(c => c.code === formData.currency);
    return currencyObj ? currencyObj.symbol : '$';
  };
  const [formData, setFormData] = useState({
    name: '',
    value: '',
    currency: 'USD', // Default currency
    status: 'New',
    closeDate: '',
    description: '',
    phone: '',
    email: '',
    address: '',
    archived: false,
    archivedAt: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Initialize form with opportunity data if editing
  useEffect(() => {
    if (isEditing && opportunityData) {
      // Format the date for the date input (YYYY-MM-DD)
      let formattedDate = '';
      if (opportunityData.closeDate) {
        const date = new Date(opportunityData.closeDate);
        formattedDate = date.toISOString().split('T')[0];
      }

      setFormData({
        name: opportunityData.name || '',
        value: opportunityData.value ? opportunityData.value.toString() : '',
        currency: opportunityData.currency || 'USD', // Use opportunity's currency or default to USD
        status: opportunityData.status || 'New',
        closeDate: formattedDate,
        description: opportunityData.description || '',
        phone: opportunityData.phone || '',
        email: opportunityData.email || '',
        address: opportunityData.address || '',
        archived: opportunityData.archived || false,
        archivedAt: opportunityData.archivedAt || null
      });
    }
  }, [isEditing, opportunityData]);



  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === 'value') {
      // Only allow numbers for value field
      const numericValue = value.replace(/[^0-9]/g, '');
      setFormData({
        ...formData,
        [name]: numericValue
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validate form
      if (!formData.name || !formData.value || !formData.closeDate) {
        throw new Error('Please fill in all required fields');
      }

      // Prepare opportunity data
      const updatedData = {
        ...formData,
        value: Number(formData.value),
        updatedAt: new Date()
      };

      // Add organization ID if available
      if (organization?.id) {
        updatedData.organizationId = organization.id;
      }

      let docId;

      if (isEditing && opportunityData) {
        // Update existing opportunity
        const opportunityId = opportunityData.id;
        // Use the updated form data for updating
        const opportunityRef = doc(db, 'opportunities', opportunityId);
        await updateDoc(opportunityRef, updatedData);
        docId = opportunityId;
      } else {
        // Create new opportunity
        updatedData.createdBy = currentUser.uid;
        updatedData.createdAt = new Date();

        // Add to Firestore
        const docRef = await addDoc(collection(db, 'opportunities'), updatedData);
        docId = docRef.id;
      }

      // Show success message
      setSuccess(true);

      if (!isEditing) {
        // Only reset form for new opportunities
        setFormData({
          name: '',
          value: '',
          currency: formData.currency, // Keep the selected currency
          status: 'New',
          closeDate: '',
          description: '',
          phone: '',
          email: '',
          address: ''
        });
      }

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(docId, updatedData);
      }

      // Close modal after 1.5 seconds if onClose is provided
      if (onClose) {
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('Error adding opportunity:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="card shadow border-0">
      <div className="card-header bg-primary text-white">
        <h5 className="mb-0">{isEditing ? 'Edit Opportunity' : 'Add New Opportunity'}</h5>
      </div>
      <div className="card-body">
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success" role="alert">
            Opportunity {isEditing ? 'updated' : 'created'} successfully!
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label htmlFor="name" className="form-label">Opportunity Name *</label>
            <input
              type="text"
              className="form-control"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>



          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="value" className="form-label">Value *</label>
              <div className="input-group">
                <span className="input-group-text">{getFormCurrencySymbol()}</span>
                <input
                  type="text"
                  className="form-control"
                  id="value"
                  name="value"
                  value={formData.value}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>

            <div className="col-md-6">
              <label htmlFor="currency" className="form-label">Currency *</label>
              <select
                className="form-select"
                id="currency"
                name="currency"
                value={formData.currency}
                onChange={handleChange}
                required
              >
                {availableCurrencies.map(curr => (
                  <option key={curr.code} value={curr.code}>
                    {curr.code} - {curr.name} ({curr.symbol})
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="status" className="form-label">Status *</label>
              <select
                className="form-select"
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                required
              >
                <option value="New">New</option>
                <option value="Qualified">Qualified</option>
                <option value="Proposal">Proposal</option>
                <option value="Negotiation">Negotiation</option>
                <option value="Closed Won">Closed Won</option>
                <option value="Closed Lost">Closed Lost</option>
                <option value="Archived">Archived</option>
              </select>
            </div>

            <div className="col-md-6">
              <label htmlFor="closeDate" className="form-label">Expected Close Date *</label>
              <input
                type="date"
                className="form-control"
                id="closeDate"
                name="closeDate"
                value={formData.closeDate}
                onChange={handleChange}
                required
              />
            </div>
          </div>



          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="phone" className="form-label">Phone</label>
              <input
                type="tel"
                className="form-control"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="email" className="form-label">Email</label>
              <input
                type="email"
                className="form-control"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="mb-3">
            <label htmlFor="address" className="form-label">Address</label>
            <input
              type="text"
              className="form-control"
              id="address"
              name="address"
              value={formData.address}
              onChange={handleChange}
            />
          </div>

          <div className="mb-3">
            <label htmlFor="description" className="form-label">Description</label>
            <textarea
              className="form-control"
              id="description"
              name="description"
              rows="3"
              value={formData.description}
              onChange={handleChange}
            ></textarea>
          </div>

          <div className="d-flex justify-content-end gap-2 mt-4">
            {onClose && (
              <button
                type="button"
                className="btn btn-outline-secondary"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Saving...
                </>
              ) : isEditing ? 'Update Opportunity' : 'Save Opportunity'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default OpportunityForm;
