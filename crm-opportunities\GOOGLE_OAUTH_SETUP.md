# Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for the Enhanced CRM Dashboard.

## Quick Start (Demo Mode)

The application works out of the box in **demo mode**. Simply:
1. Start the application (`npm run dev`)
2. Click "Sign in with Google" 
3. The app will automatically use demo credentials

## Setting Up Real Google OAuth (Optional)

If you want to use real Google authentication, follow these steps:

### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Identity Services API

### Step 2: Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in required information:
   - App name: "Enhanced CRM Dashboard"
   - User support email: Your email
   - Developer contact: Your email
4. Add scopes: `email`, `profile`, `openid`
5. Add test users (your email addresses)

### Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Add authorized JavaScript origins:
   - `http://localhost:5173`
   - `http://127.0.0.1:5173`
   - Your production domain (if applicable)
5. Copy the Client ID

### Step 4: Update Application

1. Open `src/components/GoogleAuth.jsx`
2. Replace `'YOUR_GOOGLE_CLIENT_ID'` with your actual Client ID:
   ```javascript
   client_id: 'your-actual-client-id-here.apps.googleusercontent.com',
   ```

### Step 5: Test Authentication

1. Restart the development server
2. Open the application
3. Click "Sign in with Google"
4. Complete the OAuth flow

## Troubleshooting

### Common Issues

**Error: "The request has been aborted"**
- This is normal in demo mode
- The app will automatically fall back to demo authentication

**Error: "Invalid client ID"**
- Check that your Client ID is correct
- Ensure the domain is added to authorized origins

**Error: "Access blocked"**
- Add your email to test users in OAuth consent screen
- Make sure the app is not in production mode without verification

### Demo Mode Features

When using demo mode, you get:
- Simulated user: "Demo User" (<EMAIL>)
- All CRM features work normally
- Data persists in localStorage
- No real Google account required

## Security Notes

- Never commit real Client IDs to public repositories
- Use environment variables for production deployments
- The demo mode is perfect for development and testing

## Production Deployment

For production deployment:
1. Set up proper environment variables
2. Add your production domain to authorized origins
3. Complete OAuth consent screen verification
4. Use HTTPS for your domain

## Support

If you encounter issues:
1. Check the browser console for errors
2. Verify your Google Cloud Console settings
3. Try demo mode first to ensure the app works
4. Refer to [Google Identity Services documentation](https://developers.google.com/identity/gsi/web)

The application is designed to work seamlessly in both demo and production modes!
