import { doc, updateDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { db } from './firebase';
import { getOrganization } from './organizationService';

// Collection names
const ORGANIZATIONS_COLLECTION = 'organizations';

// Subscription plans
export const SUBSCRIPTION_PLANS = {
  FREE: 'free',
  BASIC: 'basic',
  PROFESSIONAL: 'professional',
  ENTERPRISE: 'enterprise'
};

// Plan limits
export const PLAN_LIMITS = {
  [SUBSCRIPTION_PLANS.FREE]: {
    maxMembers: 5,
    maxContacts: 50,
    maxCompanies: 20,
    maxOpportunities: 20,
    maxTasks: 50,
    features: ['Basic CRM', 'Flow View', 'Team Collaboration']
  },
  [SUBSCRIPTION_PLANS.BASIC]: {
    maxMembers: 5,
    maxContacts: 500,
    maxCompanies: 100,
    maxOpportunities: 100,
    maxTasks: 500,
    features: ['Basic CRM', 'Flow View', 'Team Collaboration', 'Chat']
  },
  [SUBSCRIPTION_PLANS.PROFESSIONAL]: {
    maxMembers: 20,
    maxContacts: 2000,
    maxCompanies: 500,
    maxOpportunities: 500,
    maxTasks: 2000,
    features: ['Basic CRM', 'Flow View', 'Team Collaboration', 'Chat', 'Advanced Analytics', 'API Access']
  },
  [SUBSCRIPTION_PLANS.ENTERPRISE]: {
    maxMembers: 100,
    maxContacts: 10000,
    maxCompanies: 2000,
    maxOpportunities: 2000,
    maxTasks: 10000,
    features: ['Basic CRM', 'Flow View', 'Team Collaboration', 'Chat', 'Advanced Analytics', 'API Access', 'Priority Support', 'Custom Integrations']
  }
};

/**
 * Get the current subscription plan for an organization
 * @param {string} organizationId - The organization ID
 * @returns {Promise<Object>} - The subscription plan details
 */
export const getSubscriptionPlan = async (organizationId) => {
  try {
    const organization = await getOrganization(organizationId);

    if (!organization || !organization.subscription) {
      return {
        plan: SUBSCRIPTION_PLANS.FREE,
        status: 'inactive',
        limits: PLAN_LIMITS[SUBSCRIPTION_PLANS.FREE]
      };
    }

    const { plan = SUBSCRIPTION_PLANS.FREE, status = 'inactive' } = organization.subscription;

    return {
      plan,
      status,
      limits: PLAN_LIMITS[plan] || PLAN_LIMITS[SUBSCRIPTION_PLANS.FREE]
    };
  } catch (error) {
    console.error('Error getting subscription plan:', error);
    return {
      plan: SUBSCRIPTION_PLANS.FREE,
      status: 'error',
      limits: PLAN_LIMITS[SUBSCRIPTION_PLANS.FREE]
    };
  }
};

/**
 * Update the subscription plan for an organization
 * @param {string} organizationId - The organization ID
 * @param {string} plan - The new subscription plan
 * @param {Object} paymentDetails - Optional payment details
 * @returns {Promise<Object>} - The result of the operation
 */
export const updateSubscriptionPlan = async (organizationId, plan, paymentDetails = {}) => {
  try {
    // Validate the plan
    if (!Object.values(SUBSCRIPTION_PLANS).includes(plan)) {
      throw new Error('Invalid subscription plan');
    }

    const orgRef = doc(db, ORGANIZATIONS_COLLECTION, organizationId);
    const orgDoc = await getDoc(orgRef);

    if (!orgDoc.exists()) {
      throw new Error('Organization not found');
    }

    // Calculate end date (1 month from now for paid plans, null for free plan)
    const endDate = plan === SUBSCRIPTION_PLANS.FREE
      ? null
      : new Date(new Date().setMonth(new Date().getMonth() + 1));

    // Update the subscription
    await updateDoc(orgRef, {
      'subscription.plan': plan,
      'subscription.status': 'active',
      'subscription.startDate': serverTimestamp(),
      'subscription.endDate': endDate,
      'subscription.paymentDetails': paymentDetails,
      'subscription.updatedAt': serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'Subscription updated successfully',
      plan,
      limits: PLAN_LIMITS[plan]
    };
  } catch (error) {
    console.error('Error updating subscription plan:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Check if an organization has reached its plan limits
 * @param {string} organizationId - The organization ID
 * @param {string} resourceType - The type of resource to check (members, contacts, etc.)
 * @param {number} currentCount - The current count of resources
 * @returns {Promise<Object>} - The result of the check
 */
export const checkPlanLimits = async (organizationId, resourceType, currentCount) => {
  try {
    const { plan, limits } = await getSubscriptionPlan(organizationId);

    // Map resource type to the corresponding limit property
    const limitProperty = {
      'members': 'maxMembers',
      'contacts': 'maxContacts',
      'companies': 'maxCompanies',
      'opportunities': 'maxOpportunities',
      'tasks': 'maxTasks'
    }[resourceType];

    if (!limitProperty || !limits[limitProperty]) {
      return {
        success: false,
        error: 'Invalid resource type'
      };
    }

    const limit = limits[limitProperty];
    const withinLimits = currentCount < limit;

    return {
      success: true,
      withinLimits,
      limit,
      currentCount,
      remaining: limit - currentCount,
      plan
    };
  } catch (error) {
    console.error('Error checking plan limits:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
