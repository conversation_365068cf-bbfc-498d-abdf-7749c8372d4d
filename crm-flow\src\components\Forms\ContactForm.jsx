import { useState, useEffect } from 'react';
import { collection, addDoc, getDocs, doc, updateDoc } from 'firebase/firestore';
import { addUserDocument, getUserData } from '../../services/dataAccessService';
import { db } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';
import 'bootstrap/dist/css/bootstrap.min.css';

const ContactForm = ({ onClose, onSuccess, contactData = null, isEditing = false }) => {
  const { currentUser, organization } = useAuth();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    title: '',
    email: '',
    phone: '',
    company: '',
    notes: ''
  });
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Initialize form with contact data if editing
  useEffect(() => {
    if (isEditing && contactData) {
      setFormData({
        id: contactData.id,
        firstName: contactData.firstName || '',
        lastName: contactData.lastName || '',
        title: contactData.title || '',
        email: contactData.email || '',
        phone: contactData.phone || '',
        company: contactData.company || '',
        notes: contactData.notes || ''
      });
    }
  }, [isEditing, contactData]);

  // Fetch companies for dropdown
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        // Get organization ID if available
        const orgId = organization?.id || null;

        // Use the getUserData function to get companies
        const companiesData = await getUserData('companies', currentUser.uid, orgId);
        console.log('Companies for dropdown:', companiesData);

        setCompanies(companiesData);
      } catch (error) {
        console.error('Error fetching companies:', error);
        setError('Failed to load companies. Please try again.');
      }
    };

    if (currentUser) {
      fetchCompanies();
    }
  }, [currentUser, organization]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validate form
      if (!formData.firstName || !formData.lastName || !formData.email || !formData.company) {
        throw new Error('Please fill in all required fields');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Prepare contact data
      const contactData = {
        ...formData,
        updatedAt: new Date()
      };

      // Add organization ID if available
      if (organization?.id) {
        contactData.organizationId = organization.id;
      }

      console.log('Contact data with organization context:', contactData);

      let docId;

      if (isEditing && contactData.id) {
        // Update existing contact
        const contactId = contactData.id;
        // Remove id from the data object before updating
        const { id, ...dataToUpdate } = contactData;
        const contactRef = doc(db, 'contacts', contactId);
        await updateDoc(contactRef, dataToUpdate);
        docId = contactId;
      } else {
        // Create new contact using the dataAccessService
        console.log('Creating new contact with data:', contactData);

        // Use the addUserDocument function to add the contact with proper context
        const result = await addUserDocument('contacts', contactData, currentUser.uid, organization?.id);

        if (result.success) {
          docId = result.id;
          console.log('Contact created with ID:', docId);
        } else {
          throw new Error(result.error || 'Failed to create contact');
        }
      }

      // Show success message
      setSuccess(true);

      if (!isEditing) {
        // Only reset form for new contacts
        setFormData({
          firstName: '',
          lastName: '',
          title: '',
          email: '',
          phone: '',
          company: '',
          notes: ''
        });
      }

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(docId, contactData);
      }

      // Close modal after 1.5 seconds if onClose is provided
      if (onClose) {
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('Error adding contact:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="card shadow border-0">
      <div className="card-header bg-success text-white">
        <h5 className="mb-0">{isEditing ? 'Edit Contact' : 'Add New Contact'}</h5>
      </div>
      <div className="card-body">
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success" role="alert">
            Contact {isEditing ? 'updated' : 'created'} successfully!
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="firstName" className="form-label">First Name *</label>
              <input
                type="text"
                className="form-control"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                required
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="lastName" className="form-label">Last Name *</label>
              <input
                type="text"
                className="form-control"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className="mb-3">
            <label htmlFor="title" className="form-label">Job Title</label>
            <input
              type="text"
              className="form-control"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
            />
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="email" className="form-label">Email *</label>
              <input
                type="email"
                className="form-control"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>

            <div className="col-md-6">
              <label htmlFor="phone" className="form-label">Phone</label>
              <input
                type="tel"
                className="form-control"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="mb-3">
            <label htmlFor="company" className="form-label">Company *</label>
            <input
              type="text"
              className="form-control"
              id="company"
              name="company"
              value={formData.company || ''}
              onChange={handleChange}
              placeholder="Enter company name"
              required
            />
          </div>

          <div className="mb-3">
            <label htmlFor="notes" className="form-label">Notes</label>
            <textarea
              className="form-control"
              id="notes"
              name="notes"
              rows="3"
              value={formData.notes}
              onChange={handleChange}
            ></textarea>
          </div>

          <div className="d-flex justify-content-end gap-2 mt-4">
            {onClose && (
              <button
                type="button"
                className="btn btn-outline-secondary"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              className="btn btn-success"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Saving...
                </>
              ) : isEditing ? 'Update Contact' : 'Save Contact'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContactForm;
