{"$schema": "https://schema.tauri.app/config/2", "productName": "mixer", "version": "0.1.0", "identifier": "com.mixer.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Volume Mixer", "width": 250, "height": 100, "resizable": false, "decorations": false, "transparent": false, "center": true, "fullscreen": false, "maximized": false, "visible": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}