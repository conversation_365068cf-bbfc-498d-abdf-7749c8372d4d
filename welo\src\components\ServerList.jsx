import { useState } from 'react'
import {
  Box,
  Typography,
  Chip,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
  Circle as CircleIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material'
import ServerDetails from './ServerDetails'
import { queryServer, deleteServer, updateServerName } from '../services/api'

const ServerList = ({ servers, loading, onServerUpdated }) => {
  const [selectedServer, setSelectedServer] = useState(null)
  const [queryingServers, setQueryingServers] = useState(new Set())
  const [editingServer, setEditingServer] = useState(null)
  const [editName, setEditName] = useState('')

  const handleQueryServer = async (serverId) => {
    try {
      setQueryingServers(prev => new Set([...prev, serverId]))
      await queryServer(serverId)
      onServerUpdated()
    } catch (error) {
      console.error('Failed to query server:', error)
    } finally {
      setQueryingServers(prev => {
        const newSet = new Set(prev)
        newSet.delete(serverId)
        return newSet
      })
    }
  }

  const handleDeleteServer = async (serverId, serverName) => {
    if (!window.confirm(`Are you sure you want to delete "${serverName}"?`)) {
      return
    }

    try {
      await deleteServer(serverId)
      onServerUpdated()
    } catch (error) {
      console.error('Failed to delete server:', error)
      alert('Failed to delete server: ' + error.message)
    }
  }

  const handleEditServer = (server) => {
    setEditingServer(server.id)
    setEditName(server.name)
  }

  const handleSaveEdit = async () => {
    if (!editName.trim()) {
      alert('Server name cannot be empty')
      return
    }

    try {
      await updateServerName(editingServer, editName.trim())
      setEditingServer(null)
      setEditName('')
      onServerUpdated()
    } catch (error) {
      console.error('Failed to update server name:', error)
      alert('Failed to update server name: ' + error.message)
    }
  }

  const handleCancelEdit = () => {
    setEditingServer(null)
    setEditName('')
  }

  const formatLastChecked = (dateString) => {
    if (!dateString) return 'Never'
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / 60000)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return `${Math.floor(diffMins / 1440)}d ago`
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>Loading servers...</Typography>
      </Box>
    )
  }

  if (servers.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>No servers added yet</Typography>
        <Typography variant="body2" color="text.secondary">
          Click "Add Server" to get started!
        </Typography>
      </Paper>
    )
  }

  const onlineCount = servers.filter(s => s.is_online).length
  const offlineCount = servers.length - onlineCount

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h5" component="h2">
          Servers ({servers.length})
        </Typography>
        <Box display="flex" gap={1}>
          <Chip
            icon={<CircleIcon sx={{ color: '#4CAF50 !important' }} />}
            label={`Online: ${onlineCount}`}
            variant="outlined"
            size="small"
          />
          <Chip
            icon={<CircleIcon sx={{ color: '#f44336 !important' }} />}
            label={`Offline: ${offlineCount}`}
            variant="outlined"
            size="small"
          />
        </Box>
      </Box>

      <TableContainer component={Paper}>
        <Table size="small" stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>Status</TableCell>
              <TableCell>Server Name</TableCell>
              <TableCell>Address</TableCell>
              <TableCell align="center">Players</TableCell>
              <TableCell align="center">Ping</TableCell>
              <TableCell>Map</TableCell>
              <TableCell>Last Checked</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {servers.map((server) => (
              <TableRow
                key={server.id}
                hover
                sx={{
                  '&:last-child td, &:last-child th': { border: 0 },
                  cursor: 'pointer'
                }}
                onClick={() => setSelectedServer(server)}
              >
                <TableCell>
                  <CircleIcon
                    sx={{
                      color: server.is_online ? '#4CAF50' : '#f44336',
                      fontSize: 16
                    }}
                  />
                </TableCell>
                <TableCell>
                  {editingServer === server.id ? (
                    <Box display="flex" alignItems="center" gap={1}>
                      <TextField
                        size="small"
                        value={editName}
                        onChange={(e) => setEditName(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            handleSaveEdit()
                          } else if (e.key === 'Escape') {
                            handleCancelEdit()
                          }
                        }}
                        sx={{ minWidth: 150 }}
                      />
                      <IconButton size="small" onClick={handleSaveEdit} color="primary">
                        <SaveIcon fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={handleCancelEdit}>
                        <CancelIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  ) : (
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="body2" fontWeight="medium">
                        {server.name}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEditServer(server)
                        }}
                        sx={{ opacity: 0.6, '&:hover': { opacity: 1 } }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {server.address}:{server.port}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  {server.player_count !== null ? (
                    <Typography variant="body2">
                      {server.player_count}/{server.max_players || '?'}
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="text.secondary">-</Typography>
                  )}
                </TableCell>
                <TableCell align="center">
                  {server.ping ? (
                    <Typography variant="body2">{server.ping}ms</Typography>
                  ) : (
                    <Typography variant="body2" color="text.secondary">-</Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {server.map_name || '-'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {formatLastChecked(server.last_checked)}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Box display="flex" gap={0.5}>
                    <Tooltip title="Query Server">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleQueryServer(server.id)
                        }}
                        disabled={queryingServers.has(server.id)}
                      >
                        {queryingServers.has(server.id) ? (
                          <CircularProgress size={16} />
                        ) : (
                          <RefreshIcon fontSize="small" />
                        )}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Server Details">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation()
                          setSelectedServer(server)
                        }}
                      >
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Server">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteServer(server.id, server.name)
                        }}
                        color="error"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {selectedServer && (
        <ServerDetails
          server={selectedServer}
          onClose={() => setSelectedServer(null)}
          onServerUpdated={onServerUpdated}
        />
      )}
    </Box>
  )
}

export default ServerList
