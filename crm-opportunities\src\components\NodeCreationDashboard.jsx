import React, { useState } from 'react';
import { Plus, Settings, Eye, Edit, Trash2, X } from 'lucide-react';
import { nodeRegistry } from '../systems/NodeSystem';
import { 
  Target, Building, User, CheckSquare, Calendar, FileText 
} from 'lucide-react';

const iconMap = {
  Target, Building, User, CheckSquare, Calendar, FileText
};

const NodeCreationDashboard = ({ isOpen, onClose, onCreateNode }) => {
  const [activeTab, setActiveTab] = useState('create');
  const [selectedNodeType, setSelectedNodeType] = useState(null);
  const [showNodeTypeDetails, setShowNodeTypeDetails] = useState(false);

  if (!isOpen) return null;

  const nodeTypes = nodeRegistry.getAllNodeTypes();
  const categories = [...new Set(nodeTypes.map(type => type.category))];

  const handleQuickCreate = (nodeTypeName) => {
    const randomPosition = {
      x: Math.random() * 400 + 100,
      y: Math.random() * 300 + 100
    };
    onCreateNode(nodeTypeName, randomPosition);
  };

  const NodeTypeCard = ({ nodeType, showActions = true }) => {
    const IconComponent = iconMap[nodeType.icon] || Target;
    
    return (
      <div style={{
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        padding: '16px',
        background: 'white',
        transition: 'all 0.2s ease'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: '12px'
        }}>
          <div style={{
            background: nodeType.color,
            color: 'white',
            padding: '8px',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <IconComponent size={20} />
          </div>
          
          <div style={{ flex: 1 }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '8px'
            }}>
              <div>
                <h3 style={{
                  margin: 0,
                  fontSize: '16px',
                  fontWeight: 'bold',
                  color: '#1f2937'
                }}>
                  {nodeType.label}
                </h3>
                <div style={{
                  fontSize: '12px',
                  color: '#6b7280',
                  textTransform: 'capitalize',
                  marginTop: '2px'
                }}>
                  {nodeType.category}
                </div>
              </div>
              
              {showActions && (
                <div style={{ display: 'flex', gap: '4px' }}>
                  <button
                    onClick={() => {
                      setSelectedNodeType(nodeType);
                      setShowNodeTypeDetails(true);
                    }}
                    style={{
                      padding: '4px',
                      border: 'none',
                      background: '#f3f4f6',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    <Eye size={14} />
                  </button>
                  <button
                    onClick={() => handleQuickCreate(nodeType.name)}
                    style={{
                      padding: '4px',
                      border: 'none',
                      background: nodeType.color,
                      color: 'white',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    <Plus size={14} />
                  </button>
                </div>
              )}
            </div>
            
            <p style={{
              margin: '0 0 8px 0',
              fontSize: '14px',
              color: '#6b7280',
              lineHeight: '1.4'
            }}>
              {nodeType.description}
            </p>
            
            <div style={{
              fontSize: '12px',
              color: '#9ca3af'
            }}>
              {nodeType.properties.length} properties
            </div>
          </div>
        </div>
      </div>
    );
  };

  const NodeTypeDetails = ({ nodeType, onClose }) => (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1100
    }}>
      <div style={{
        background: 'white',
        borderRadius: '8px',
        padding: '24px',
        width: '90%',
        maxWidth: '600px',
        maxHeight: '80vh',
        overflow: 'auto'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <h2 style={{ margin: 0, fontSize: '20px', fontWeight: 'bold' }}>
            {nodeType.label} Details
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '4px'
            }}
          >
            <X size={20} />
          </button>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '8px' }}>
            Properties
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {nodeType.properties.map(prop => (
              <div key={prop.key} style={{
                padding: '12px',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                background: '#f9fafb'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '4px'
                }}>
                  <span style={{ fontWeight: '500', fontSize: '14px' }}>
                    {prop.label}
                  </span>
                  <div style={{
                    display: 'flex',
                    gap: '8px',
                    alignItems: 'center'
                  }}>
                    <span style={{
                      fontSize: '12px',
                      color: '#6b7280',
                      textTransform: 'capitalize'
                    }}>
                      {prop.type}
                    </span>
                    {prop.required && (
                      <span style={{
                        fontSize: '10px',
                        background: '#ef4444',
                        color: 'white',
                        padding: '2px 4px',
                        borderRadius: '2px'
                      }}>
                        Required
                      </span>
                    )}
                  </div>
                </div>
                <div style={{ fontSize: '12px', color: '#6b7280' }}>
                  Key: {prop.key}
                </div>
                {prop.defaultValue && (
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>
                    Default: {prop.defaultValue}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={() => {
            handleQuickCreate(nodeType.name);
            onClose();
          }}
          style={{
            width: '100%',
            padding: '12px',
            background: nodeType.color,
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500'
          }}
        >
          Create {nodeType.label}
        </button>
      </div>
    </div>
  );

  return (
    <>
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}>
        <div style={{
          background: 'white',
          borderRadius: '12px',
          width: '95%',
          maxWidth: '1000px',
          height: '90vh',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          {/* Header */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '20px 24px',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
              Node Creation Dashboard
            </h1>
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '4px'
              }}
            >
              <X size={24} />
            </button>
          </div>

          {/* Tabs */}
          <div style={{
            display: 'flex',
            borderBottom: '1px solid #e5e7eb',
            padding: '0 24px'
          }}>
            {[
              { id: 'create', label: 'Quick Create', icon: Plus },
              { id: 'manage', label: 'Manage Types', icon: Settings }
            ].map(tab => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '12px 16px',
                    border: 'none',
                    background: 'none',
                    cursor: 'pointer',
                    borderBottom: activeTab === tab.id ? '2px solid #3b82f6' : '2px solid transparent',
                    color: activeTab === tab.id ? '#3b82f6' : '#6b7280',
                    fontWeight: activeTab === tab.id ? '500' : '400'
                  }}
                >
                  <IconComponent size={16} />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Content */}
          <div style={{ flex: 1, overflow: 'auto', padding: '24px' }}>
            {activeTab === 'create' && (
              <div>
                <div style={{ marginBottom: '20px' }}>
                  <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>
                    Quick Create Nodes
                  </h2>
                  <p style={{ color: '#6b7280', margin: 0 }}>
                    Click the + button to quickly create a new node of any type.
                  </p>
                </div>

                {categories.map(category => (
                  <div key={category} style={{ marginBottom: '24px' }}>
                    <h3 style={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      marginBottom: '12px',
                      textTransform: 'capitalize',
                      color: '#1f2937'
                    }}>
                      {category}
                    </h3>
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                      gap: '12px'
                    }}>
                      {nodeTypes
                        .filter(type => type.category === category)
                        .map(nodeType => (
                          <NodeTypeCard key={nodeType.name} nodeType={nodeType} />
                        ))}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'manage' && (
              <div>
                <div style={{ marginBottom: '20px' }}>
                  <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>
                    Manage Node Types
                  </h2>
                  <p style={{ color: '#6b7280', margin: 0 }}>
                    View and manage all available node types in your system.
                  </p>
                </div>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
                  gap: '16px'
                }}>
                  {nodeTypes.map(nodeType => (
                    <NodeTypeCard key={nodeType.name} nodeType={nodeType} showActions={true} />
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Node Type Details Modal */}
      {showNodeTypeDetails && selectedNodeType && (
        <NodeTypeDetails
          nodeType={selectedNodeType}
          onClose={() => {
            setShowNodeTypeDetails(false);
            setSelectedNodeType(null);
          }}
        />
      )}
    </>
  );
};

export default NodeCreationDashboard;
