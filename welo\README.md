# QuakeWorld Server Browser (Tauri Desktop App)

A desktop QuakeWorld server browser built with <PERSON>ri, <PERSON><PERSON>, and Rust. This app allows you to add, monitor, and query QuakeWorld game servers with a native Windows binary.

## Features

- 🎮 Add and manage QuakeWorld servers
- 🔍 Real-time server status monitoring
- 📊 Player count and server information display
- 📈 Server history tracking
- 🗄️ SQLite database for persistent storage
- 🌐 Modern React frontend with Material-UI design
- 💻 Native Windows desktop application
- ⚡ Fast Rust backend with UDP QuakeWorld queries

## Prerequisites

- Node.js (v16 or higher)
- Rust (latest stable version)
- npm or yarn

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

## Running the Application

### Development Mode (Recommended)

Run the Tauri development server which will open the native desktop app:

```bash
npm run tauri:dev
```

This will:
- Start the Vite development server
- Compile the Rust backend
- Open the native desktop application window
- Enable hot-reload for both frontend and backend changes

### Building for Production

To create a Windows executable (.exe):

```bash
npm run tauri:build
```

The built executable will be located in `src-tauri/target/release/`

### Web Development Mode (Limited)

If you want to develop the frontend only in a browser:

```bash
npm run dev
```

Note: Tauri commands won't work in browser mode, so server functionality will be limited.

## Usage

1. **Add a Server**: Click the "Add Server" button and enter the server details
2. **Test Connection**: Use the "Test Connection" button to verify the server is reachable
3. **Monitor Servers**: The main dashboard shows all your servers with their current status
4. **Edit Server Names**: Click the edit icon next to any server name to rename it
5. **View Details**: Click on any server row to see detailed information, player lists, and history
6. **Refresh Status**: Use the "Refresh All" button to update all server statuses at once

## Technology Stack

- **Frontend**: React, Vite, Material-UI
- **Backend**: Rust with Tauri
- **Database**: SQLite with rusqlite
- **QuakeWorld Integration**: Custom Rust UDP implementation
- **Desktop Framework**: Tauri (creates native Windows executable)

## Tauri Commands (Internal API)

The Rust backend exposes these commands to the frontend:

- `get_servers` - Get all servers with latest status
- `add_server` - Add a new server
- `delete_server` - Delete a server
- `update_server_name` - Update server name
- `query_server` - Query a specific server
- `query_all_servers` - Query all servers
- `get_server_history` - Get server history
- `test_server` - Test server connection without saving

## Database Schema

The application uses SQLite with two main tables:
- `servers` - Stores server information
- `server_history` - Tracks server status over time

## Building for Distribution

The Tauri build process creates a native Windows executable that includes:
- The React frontend (bundled)
- The Rust backend
- SQLite database (created on first run)
- All dependencies

No additional runtime requirements needed on target machines.

## Development Notes

- The database file is stored in the user's app data directory
- Server queries use UDP sockets to communicate with QuakeWorld servers
- The app supports the standard QuakeWorld status protocol
- Hot-reload works for both frontend (React) and backend (Rust) changes

## Contributing

Feel free to submit issues and enhancement requests!
