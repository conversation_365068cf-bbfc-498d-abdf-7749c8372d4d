import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import 'bootstrap/dist/css/bootstrap.min.css';

const SettingsPage = () => {
  const { currentUser, userProfile, refreshUserProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  // Initialize form with user data
  useEffect(() => {
    // No form data to initialize
  }, [userProfile]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setSuccess('');
    setError('');

    try {
      if (!currentUser) {
        throw new Error('You must be logged in to update settings');
      }

      // Update user profile in Firestore if needed
      const userRef = doc(db, 'users', currentUser.uid);
      await updateDoc(userRef, {
        updatedAt: new Date()
      });

      // Refresh user profile
      await refreshUserProfile();
      setSuccess('Settings updated successfully!');
    } catch (error) {
      console.error('Error updating settings:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-4">
      <div className="row justify-content-center">
        <div className="col-md-8">
          <div className="card shadow-sm">
            <div className="card-header bg-primary text-white">
              <h4 className="mb-0">User Settings</h4>
            </div>
            <div className="card-body">
              {error && <div className="alert alert-danger">{error}</div>}
              {success && <div className="alert alert-success">{success}</div>}

              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <h5>Profile Information</h5>
                  <div className="row align-items-center mb-3">
                    <div className="col-auto">
                      {userProfile?.photoURL ? (
                        <img
                          src={userProfile.photoURL}
                          alt="Profile"
                          className="rounded-circle"
                          style={{ width: '80px', height: '80px', objectFit: 'cover' }}
                        />
                      ) : (
                        <div
                          className="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white"
                          style={{ width: '80px', height: '80px', fontSize: '2rem' }}
                        >
                          {userProfile?.name?.charAt(0) || userProfile?.email?.charAt(0) || '?'}
                        </div>
                      )}
                    </div>
                    <div className="col">
                      <p className="mb-1"><strong>Name:</strong> {userProfile?.name}</p>
                      <p className="mb-1"><strong>Email:</strong> {userProfile?.email}</p>
                      <p className="mb-0"><strong>Role:</strong> {userProfile?.role || 'User'}</p>
                    </div>
                  </div>
                </div>



                <div className="d-grid gap-2">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Saving...
                      </>
                    ) : 'Save Settings'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
