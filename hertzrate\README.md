# HertzRate - Monitor Refresh Rate Manager

A Rust application to manage refresh rates for all connected monitors on Windows with an intuitive GUI interface.

## Features

- 🖥️ **Data-Adaptive GUI** - Intelligent interface that adapts to your monitor setup and window size
- 📋 **List all connected monitors** with their current settings and available refresh rates
- ⚡ **Set refresh rate for specific monitor** through GUI dropdowns
- 📊 **Display current resolution and refresh rate** for each monitor
- ✅ **Error handling** with detailed feedback
- 🔄 **Auto-refresh** - GUI automatically updates monitor information every 5 seconds

## Installation

### Option 1: Windows Installer (Recommended)

Download the latest installer from the [Releases](https://github.com/your-username/hertzrate/releases) page:
- `HertzRate-Setup-v0.1.0.exe` - Complete Windows installer

**Features:**
- Easy installation with Windows installer
- Desktop shortcut and Start Menu integration
- Clean uninstall through Windows Programs & Features

### Option 2: Build from Source

**Prerequisites:**
- Windows 10/11
- Rust toolchain (install from [rustup.rs](https://rustup.rs/))

**Build steps:**
```bash
git clone <repository-url>
cd hertzrate
cargo build --release
```

**Create installer:**
```powershell
.\build_installer.ps1
```

**Executable:**
- `target/release/hertzrate.exe` - GUI application

### Quick Usage
```bash
# Launch GUI
.\target\release\hertzrate.exe
```

## Usage

Launch the GUI application:
```bash
.\target\release\hertzrate.exe
```

The GUI provides:
- **Data-Adaptive Design**: Automatically adjusts layout based on number of monitors and available space
- **Multiple Layout Modes**:
  - Grid layout for multiple monitors on wide screens
  - Compact layout for narrow windows
  - Minimal layout for many monitors in limited space
  - Standard layout for optimal viewing
- **Smart Window Sizing**: Initial size adapts to detected monitor count
- **Intelligent Status Display**: Shows refresh rate conflicts and monitor diversity
- **Monitor Cards**: Truncated text and optimized spacing based on content
- **Apply Buttons**: Apply changes to individual monitors
- **Real-time Feedback**: Status messages and auto-refresh every 5 seconds
- **Fully Resizable**: Minimum 400×300, maximum 1200×800 pixels



## Troubleshooting

### "No monitors found"
- Ensure your monitors are properly connected and recognized by Windows
- Check Windows Display Settings to verify monitors are detected

### "Refresh rate XHz is not available"
- Check the GUI for available refresh rates for each monitor
- Some monitors may not support certain refresh rates at their current resolution

### "Failed to change refresh rate"
- Try running as Administrator
- Ensure the monitor supports the requested refresh rate
- Check that no applications are preventing display changes

### Permission Issues
Some display changes may require administrator privileges. Try running the application as Administrator.

## Technical Details

This tool uses the Windows GDI API functions:
- `EnumDisplayDevices` - to enumerate connected monitors
- `EnumDisplaySettings` - to get current and available display modes
- `ChangeDisplaySettings` - to change the refresh rate

The tool preserves the current resolution and only changes the refresh rate.

## License

MIT License - see LICENSE file for details.
