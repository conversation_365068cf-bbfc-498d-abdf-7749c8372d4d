/**
 * Session-based node position management service
 * This service stores node positions in memory and provides methods to save them to Firebase
 */

// In-memory storage for node positions
let sessionNodePositions = {};
let hasUnsavedChanges = false;

/**
 * Initialize session storage with positions from Firebase
 * @param {Object} positions - Node positions loaded from Firebase
 */
export const initializeSessionPositions = (positions) => {
  sessionNodePositions = positions ? { ...positions } : {};
  hasUnsavedChanges = false;
};

/**
 * Save node positions to session storage
 * @param {Array} nodes - The array of nodes with positions
 * @returns {boolean} - Whether the operation was successful
 */
export const saveNodePositionsToSession = (nodes) => {
  try {
    if (!nodes || nodes.length === 0) return false;

    // Extract positions from nodes
    nodes.forEach(node => {
      if (node && node.id && node.position) {
        sessionNodePositions[node.id] = {
          x: node.position.x,
          y: node.position.y
        };
      }
    });

    hasUnsavedChanges = true;
    return true;
  } catch (error) {
    console.error('Error saving node positions to session:', error);
    return false;
  }
};

/**
 * Update a single node's position in session storage
 * @param {string} nodeId - The node ID
 * @param {Object} position - The new position {x, y}
 * @returns {boolean} - Whether the operation was successful
 */
export const updateNodePositionInSession = (nodeId, position) => {
  try {
    if (!nodeId || !position) return false;

    sessionNodePositions[nodeId] = {
      x: position.x,
      y: position.y
    };

    hasUnsavedChanges = true;
    return true;
  } catch (error) {
    console.error('Error updating node position in session:', error);
    return false;
  }
};

/**
 * Get all node positions from session storage
 * @returns {Object} - The node positions
 */
export const getSessionNodePositions = () => {
  return { ...sessionNodePositions };
};

/**
 * Check if there are unsaved changes
 * @returns {boolean} - Whether there are unsaved changes
 */
export const hasUnsavedNodeChanges = () => {
  return hasUnsavedChanges;
};

/**
 * Reset the unsaved changes flag
 */
export const resetUnsavedChangesFlag = () => {
  hasUnsavedChanges = false;
};

/**
 * Clear session storage
 */
export const clearSessionPositions = () => {
  sessionNodePositions = {};
  hasUnsavedChanges = false;
};
