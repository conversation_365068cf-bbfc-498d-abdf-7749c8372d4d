import { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Paper,
  Text,
  Group,
  Button,
  Switch,
  NumberInput,
  TextInput,
  Box,
  Tabs,
  Table,
  ActionIcon,
  Divider,
  Card
} from '@mantine/core';
import { useForm } from '@mantine/form';
import {
  IconArrowLeft,
  IconDeviceFloppy,
  IconPlus,
  IconTrash,
  IconRefresh,
  IconSettings,
  IconServer,
  IconWorld
} from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';
import { notifications } from '@mantine/notifications';

function Settings({
  settings,
  saveSettings,
  masterServers,
  addMasterServer,
  updateMasterServer,
  deleteMasterServer,
  loadMasterServers
}) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  // Form for general settings
  const settingsForm = useForm({
    initialValues: {
      auto_refresh: settings?.auto_refresh || false,
      refresh_interval: settings?.refresh_interval || 300,
      dark_mode: settings?.dark_mode || true,
      show_empty_servers: settings?.show_empty_servers || true,
      show_full_servers: settings?.show_full_servers || true,
      ping_timeout: settings?.ping_timeout || 3000,
    },
  });

  // Form for adding a new master server
  const masterServerForm = useForm({
    initialValues: {
      address: '',
      port: 27000,
    },
    validate: {
      address: (value) => (value.trim().length === 0 ? 'Address is required' : null),
      port: (value) => (value < 1 || value > 65535 ? 'Port must be between 1 and 65535' : null),
    },
  });

  // Update form when settings change
  useEffect(() => {
    if (settings) {
      settingsForm.setValues({
        auto_refresh: settings.auto_refresh,
        refresh_interval: settings.refresh_interval,
        dark_mode: settings.dark_mode,
        show_empty_servers: settings.show_empty_servers,
        show_full_servers: settings.show_full_servers,
        ping_timeout: settings.ping_timeout,
      });
    }
  }, [settings]);

  // Handle saving settings
  const handleSaveSettings = async (values) => {
    try {
      setLoading(true);
      await saveSettings(values);
      notifications.show({
        title: 'Settings Saved',
        message: 'Your settings have been saved successfully.',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: `Failed to save settings: ${error.message || error}`,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a master server
  const handleAddMasterServer = async (values) => {
    try {
      setLoading(true);
      await addMasterServer(values.address, values.port);
      masterServerForm.reset();
      await loadMasterServers();
      notifications.show({
        title: 'Master Server Added',
        message: 'The master server has been added successfully.',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: `Failed to add master server: ${error.message || error}`,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle toggling a master server
  const handleToggleMasterServer = async (address, port, enabled) => {
    try {
      setLoading(true);
      await updateMasterServer(address, port, !enabled);
      await loadMasterServers();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: `Failed to update master server: ${error.message || error}`,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle deleting a master server
  const handleDeleteMasterServer = async (address, port) => {
    try {
      setLoading(true);
      await deleteMasterServer(address, port);
      await loadMasterServers();
      notifications.show({
        title: 'Master Server Deleted',
        message: 'The master server has been deleted successfully.',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: `Failed to delete master server: ${error.message || error}`,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container size="xl" py="md">
      <Group mb="md">
        <Button leftSection={<IconArrowLeft size={16} />} onClick={() => navigate('/')}>
          Back to Server List
        </Button>
      </Group>

      <Title order={2} mb="md">Settings</Title>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List mb="md">
          <Tabs.Tab value="general" leftSection={<IconSettings size={16} />}>
            General
          </Tabs.Tab>
          <Tabs.Tab value="master-servers" leftSection={<IconWorld size={16} />}>
            Master Servers
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="general">
          <Card withBorder p="md">
            <form onSubmit={settingsForm.onSubmit(handleSaveSettings)}>
              <Title order={3} mb="md">General Settings</Title>

              <Switch
                label="Auto-refresh servers"
                description="Automatically refresh server list at regular intervals"
                checked={settingsForm.values.auto_refresh}
                onChange={(event) => settingsForm.setFieldValue('auto_refresh', event.currentTarget.checked)}
                mb="md"
              />

              <NumberInput
                label="Refresh interval (seconds)"
                description="How often to refresh the server list when auto-refresh is enabled"
                disabled={!settingsForm.values.auto_refresh}
                min={30}
                max={3600}
                {...settingsForm.getInputProps('refresh_interval')}
                mb="md"
              />

              <Divider my="md" />

              <Switch
                label="Dark mode"
                description="Use dark theme for the application"
                checked={settingsForm.values.dark_mode}
                onChange={(event) => settingsForm.setFieldValue('dark_mode', event.currentTarget.checked)}
                mb="md"
              />

              <Divider my="md" />

              <Switch
                label="Show empty servers"
                description="Display servers with no players"
                checked={settingsForm.values.show_empty_servers}
                onChange={(event) => settingsForm.setFieldValue('show_empty_servers', event.currentTarget.checked)}
                mb="md"
              />

              <Switch
                label="Show full servers"
                description="Display servers that are at maximum capacity"
                checked={settingsForm.values.show_full_servers}
                onChange={(event) => settingsForm.setFieldValue('show_full_servers', event.currentTarget.checked)}
                mb="md"
              />

              <Divider my="md" />

              <NumberInput
                label="Ping timeout (milliseconds)"
                description="Maximum time to wait for server responses"
                min={500}
                max={10000}
                step={100}
                {...settingsForm.getInputProps('ping_timeout')}
                mb="md"
              />

              <Group justify="flex-end" mt="md">
                <Button
                  type="submit"
                  leftSection={<IconDeviceFloppy size={16} />}
                  loading={loading}
                >
                  Save Settings
                </Button>
              </Group>
            </form>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="master-servers">
          <Card withBorder p="md" mb="md">
            <Title order={3} mb="md">Add Master Server</Title>
            <form onSubmit={masterServerForm.onSubmit(handleAddMasterServer)}>
              <Group grow>
                <TextInput
                  label="Address"
                  placeholder="Enter master server address"
                  required
                  {...masterServerForm.getInputProps('address')}
                />

                <NumberInput
                  label="Port"
                  placeholder="Enter port number"
                  required
                  min={1}
                  max={65535}
                  {...masterServerForm.getInputProps('port')}
                />
              </Group>

              <Group justify="flex-end" mt="md">
                <Button
                  type="submit"
                  leftSection={<IconPlus size={16} />}
                  loading={loading}
                >
                  Add Master Server
                </Button>
              </Group>
            </form>
          </Card>

          <Card withBorder p="md">
            <Title order={3} mb="md">Master Servers</Title>

            {masterServers.length === 0 ? (
              <Text ta="center">No master servers configured.</Text>
            ) : (
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Address</Table.Th>
                    <Table.Th>Port</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th style={{ textAlign: 'right' }}>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {masterServers.map((server) => (
                    <Table.Tr key={`${server.address}:${server.port}`}>
                      <Table.Td>{server.address}</Table.Td>
                      <Table.Td>{server.port}</Table.Td>
                      <Table.Td>
                        <Switch
                          checked={server.enabled}
                          onChange={() => handleToggleMasterServer(
                            server.address,
                            server.port,
                            server.enabled
                          )}
                        />
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="flex-end">
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            onClick={() => handleDeleteMasterServer(server.address, server.port)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          </Card>
        </Tabs.Panel>
      </Tabs>
    </Container>
  );
}

export default Settings;
