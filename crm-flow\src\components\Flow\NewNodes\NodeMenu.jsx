import { memo } from 'react';

const NodeMenu = ({ onEdit, onDelete, onArchive, isArchived, onRestore, nodeType }) => {
  return (
    <div className="node-menu">
      {!isArchived ? (
        <>
          <button
            className="node-menu-btn edit-btn"
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            title="Edit"
          >
            <i className="bi bi-pencil"></i>
          </button>
          {nodeType === 'opportunity' && (
            <button
              className="node-menu-btn archive-btn"
              onClick={(e) => {
                e.stopPropagation();
                onArchive();
              }}
              title="Archive"
            >
              <i className="bi bi-archive"></i>
            </button>
          )}
          <button
            className="node-menu-btn delete-btn"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            title="Delete"
          >
            <i className="bi bi-trash"></i>
          </button>
        </>
      ) : (
        <button
          className="node-menu-btn restore-btn"
          onClick={(e) => {
            e.stopPropagation();
            onRestore();
          }}
          title="Restore"
        >
          <i className="bi bi-arrow-counterclockwise"></i>
        </button>
      )}
    </div>
  );
};

export default memo(NodeMenu);
