# CRM Flow

A modern CRM application with a visual flow interface for managing business relationships, opportunities, and tasks.

## Features

- **Authentication**: Secure user authentication with Firebase Auth
- **Organizations**: Create and manage multiple organizations
- **Flow View**: Visual representation of business relationships
- **Contacts Management**: Track and manage contacts
- **Companies Management**: Organize company information
- **Opportunities Tracking**: Monitor sales opportunities
- **Task Management**: Assign and track tasks
- **Checklists**: Create and manage checklists for processes
- **Multilingual Support**: Available in multiple languages

## Technologies Used

- React
- Firebase (Authentication, Firestore, Hosting)
- React Flow
- Bootstrap
- i18next for internationalization

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Firebase account

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/marffinn/crm-flow.git
   cd crm-flow
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```
npm run build
```

### Deployment

The application is deployed using Firebase Hosting:

```
firebase deploy --only hosting
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Icons from Bootstrap Icons
- Avatar images from [source]
