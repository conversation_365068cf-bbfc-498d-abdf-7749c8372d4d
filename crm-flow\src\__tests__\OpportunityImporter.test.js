import { parseInputText, parseMarkdownTable, parseJSON, isJSON } from '../utils/csvParser';

describe('CSV Parser', () => {
  test('parses markdown table correctly', () => {
    const markdownTable = `
    |------------------|----------------------|--------------------------------|---------------------------------------------|
    | Name            | Contact Info         | Location                      | Issue                                       |
    |------------------|----------------------|--------------------------------|---------------------------------------------|
    | Iwona           | +48884672464         | Not specified                 | Samsung TV repair                           |
    | Patryk          | +48794750448         | Gorzów Wielkopolski           | TV repair, 41–55 inches                    |
    `;

    const result = parseMarkdownTable(markdownTable);

    expect(result).toHaveLength(2);
    expect(result[0].name).toBe('Iwona');
    expect(result[0].contact_info).toBe('+48884672464');
    expect(result[0].location).toBe('Not specified');
    expect(result[0].issue).toBe('Samsung TV repair');

    expect(result[1].name).toBe('Patryk');
    expect(result[1].contact_info).toBe('+48794750448');
    expect(result[1].location).toBe('Gorzów Wielkopolski');
    expect(result[1].issue).toBe('TV repair, 41–55 inches');
  });

  test('parses the full leads table correctly', () => {
    const fullTable = `
    |------------------|----------------------|--------------------------------|---------------------------------------------|
    | Name            | Contact Info         | Location                      | Issue                                       |
    |------------------|----------------------|--------------------------------|---------------------------------------------|
    | Iwona           | +48884672464         | Not specified                 | Samsung TV repair                           |
    | Patryk          | +48794750448         | Gorzów Wielkopolski           | TV repair, 41–55 inches                    |
    | Grzegorz        | +48535313570         | Not specified                 | Samsung TV repair, damaged screen           |
    | Maciej          | +48693373322         | Not specified                 | TV repair, 55 inches                       |
    | Bogumiła        | +48508419210         | Słupsk                        | Panasonic TV repair, TV not turning on      |
    | Jacek           | Not provided         | Not specified                 | LG TV repair, TV not turning on            |
    | Jerzy           | +48792912510         | Olecko, Warmińsko-mazurskie   | Philips TV repair                          |
    | Michał          | +48662444029         | Not specified                 | Samsung TV repair                          |
    | Urszula         | +48512872552         | Częstochowa, Śląskie          | TV repair, 41–55 inches                    |
    | Marek           | +48602673376         | Rypin, Kujawsko-pomorskie     | Thomson TV repair, damaged ports/cables     |
    | Aga             | +48500721017         | Brwinów, Mazowieckie          | TV repair, 30–40 inches                    |
    | Patrycja        | Not provided         | Not specified                 | Samsung TV repair                          |
    | Daniel          | Not provided         | Not specified                 | TV repair, 41–55 inches                    |
    | Bernadeta       | +48793907206         | Not specified                 | TV repair, 41–55 inches                    |
    | Olga            | +48572317195         | Not specified                 | Samsung TV repair, sound issues             |
    | Adam            | <EMAIL>  | Not specified                 | TV repair, 41–55 inches                    |
    | Irena           | +48660772022         | Kliniska Wielkie, Zachodniopomorskie | Sony TV repair                     |
    | Julia           | +48511045736         | Not specified                 | TV repair, 41–55 inches                    |
    | Agnieszka       | +48601190753         | Gliwice, Śląskie              | Philips TV repair                          |
    | Robert          | +48601855169         | Łódź, Łódzkie                 | TV repair, 41–55 inches                    |
    | Piotr           | +48515944978         | Lubartów, Lubelskie           | TV repair, 41–55 inches                    |
    | Stanisław       | +48509719824         | Sosnowiec, Śląskie            | Samsung TV repair                          |
    | Paweł           | +48660688023         | Not specified                 | Panasonic TV repair, TV not turning on      |
    | Jerzy           | +48502584422         | Gorlice, Małopolskie          | Samsung TV repair, color issues             |
    | Goska           | Not provided         | Not specified                 | TV repair, 41–55 inches                    |
    |------------------|----------------------|--------------------------------|---------------------------------------------|
    `;

    const result = parseInputText(fullTable);

    expect(result).toHaveLength(25);

    // Check first entry
    expect(result[0].name).toBe('Iwona');
    expect(result[0].contact_info).toBe('+48884672464');
    expect(result[0].location).toBe('Not specified');
    expect(result[0].issue).toBe('Samsung TV repair');

    // Check last entry
    expect(result[24].name).toBe('Goska');
    expect(result[24].contact_info).toBe('Not provided');
    expect(result[24].location).toBe('Not specified');
    expect(result[24].issue).toBe('TV repair, 41–55 inches');

    // Check an entry with email
    const adamEntry = result.find(item => item.name === 'Adam');
    expect(adamEntry.contact_info).toBe('<EMAIL>');

    // Check an entry with a complex location
    const irenaEntry = result.find(item => item.name === 'Irena');
    expect(irenaEntry.location).toBe('Kliniska Wielkie, Zachodniopomorskie');
  });

  test('detects JSON format correctly', () => {
    const jsonData = `[{"Name": "John", "Contact Info": "123"}]`;
    const csvData = `Name,Contact Info\nJohn,123`;
    const tableData = `| Name | Contact Info |\n|------|-------------|\n| John | 123 |`;

    expect(isJSON(jsonData)).toBe(true);
    expect(isJSON(csvData)).toBe(false);
    expect(isJSON(tableData)).toBe(false);
  });

  test('parses JSON data correctly', () => {
    const jsonData = `[
      {
        "Name": "Iwona",
        "Contact Info": "+48884672464",
        "Location": "Not specified",
        "Issue": "Samsung TV repair"
      },
      {
        "Name": "Patryk",
        "Contact Info": "+48794750448",
        "Location": "Gorzów Wielkopolski",
        "Issue": "TV repair, 41–55 inches"
      }
    ]`;

    const result = parseJSON(jsonData);

    expect(result).toHaveLength(2);
    expect(result[0].name).toBe('Iwona');
    expect(result[0].contact_info).toBe('+48884672464');
    expect(result[0].location).toBe('Not specified');
    expect(result[0].issue).toBe('Samsung TV repair');

    expect(result[1].name).toBe('Patryk');
    expect(result[1].contact_info).toBe('+48794750448');
    expect(result[1].location).toBe('Gorzów Wielkopolski');
    expect(result[1].issue).toBe('TV repair, 41–55 inches');
  });

  test('parseInputText automatically detects and parses JSON', () => {
    const jsonData = `[
      {
        "Name": "Iwona",
        "Contact Info": "+48884672464",
        "Location": "Not specified",
        "Issue": "Samsung TV repair"
      }
    ]`;

    const result = parseInputText(jsonData);

    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('Iwona');
    expect(result[0].contact_info).toBe('+48884672464');
    expect(result[0].location).toBe('Not specified');
    expect(result[0].issue).toBe('Samsung TV repair');
  });
});
