use rusqlite::{Connection, Result, params};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::path::Path;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Server {
    pub id: i64,
    pub name: String,
    pub address: String,
    pub port: i32,
    pub added_date: String,
    pub last_checked: Option<String>,
    pub is_online: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerWithStatus {
    pub id: i64,
    pub name: String,
    pub address: String,
    pub port: i32,
    pub added_date: String,
    pub last_checked: Option<String>,
    pub is_online: bool,
    pub ping: Option<i32>,
    pub player_count: Option<i32>,
    pub max_players: Option<i32>,
    pub map_name: Option<String>,
    pub game_mode: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerHistory {
    pub id: i64,
    pub server_id: i64,
    pub checked_date: String,
    pub is_online: bool,
    pub ping: Option<i32>,
    pub player_count: Option<i32>,
    pub max_players: Option<i32>,
    pub map_name: Option<String>,
    pub game_mode: Option<String>,
    pub server_info: Option<String>,
}

pub struct Database {
    conn: Connection,
}

impl Database {
    pub fn new<P: AsRef<Path>>(path: P) -> Result<Self> {
        let conn = Connection::open(path)?;
        
        // Enable foreign keys
        conn.execute("PRAGMA foreign_keys = ON", [])?;
        
        let db = Database { conn };
        db.create_tables()?;
        Ok(db)
    }

    fn create_tables(&self) -> Result<()> {
        // Create servers table
        self.conn.execute(
            "CREATE TABLE IF NOT EXISTS servers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                address TEXT NOT NULL,
                port INTEGER NOT NULL,
                added_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_checked DATETIME,
                is_online BOOLEAN DEFAULT 0,
                UNIQUE(address, port)
            )",
            [],
        )?;

        // Create server history table
        self.conn.execute(
            "CREATE TABLE IF NOT EXISTS server_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                server_id INTEGER NOT NULL,
                checked_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_online BOOLEAN NOT NULL,
                ping INTEGER,
                player_count INTEGER,
                max_players INTEGER,
                map_name TEXT,
                game_mode TEXT,
                server_info TEXT,
                FOREIGN KEY (server_id) REFERENCES servers (id) ON DELETE CASCADE
            )",
            [],
        )?;

        Ok(())
    }

    pub fn add_server(&self, name: &str, address: &str, port: i32) -> Result<i64> {
        let mut stmt = self.conn.prepare(
            "INSERT INTO servers (name, address, port) VALUES (?1, ?2, ?3)"
        )?;
        
        stmt.execute(params![name, address, port])?;
        Ok(self.conn.last_insert_rowid())
    }

    pub fn get_all_servers(&self) -> Result<Vec<Server>> {
        let mut stmt = self.conn.prepare(
            "SELECT id, name, address, port, added_date, last_checked, is_online 
             FROM servers ORDER BY added_date DESC"
        )?;

        let server_iter = stmt.query_map([], |row| {
            Ok(Server {
                id: row.get(0)?,
                name: row.get(1)?,
                address: row.get(2)?,
                port: row.get(3)?,
                added_date: row.get(4)?,
                last_checked: row.get(5)?,
                is_online: row.get(6)?,
            })
        })?;

        let mut servers = Vec::new();
        for server in server_iter {
            servers.push(server?);
        }
        Ok(servers)
    }

    pub fn get_servers_with_status(&self) -> Result<Vec<ServerWithStatus>> {
        let mut stmt = self.conn.prepare(
            "SELECT 
                s.id, s.name, s.address, s.port, s.added_date, s.last_checked, s.is_online,
                h.ping, h.player_count, h.max_players, h.map_name, h.game_mode
             FROM servers s
             LEFT JOIN (
                SELECT 
                    h1.server_id, h1.ping, h1.player_count, h1.max_players, 
                    h1.map_name, h1.game_mode
                FROM server_history h1
                INNER JOIN (
                    SELECT server_id, MAX(checked_date) as latest_check
                    FROM server_history
                    GROUP BY server_id
                ) h2 ON h1.server_id = h2.server_id AND h1.checked_date = h2.latest_check
             ) h ON s.id = h.server_id
             ORDER BY s.added_date DESC"
        )?;

        let server_iter = stmt.query_map([], |row| {
            Ok(ServerWithStatus {
                id: row.get(0)?,
                name: row.get(1)?,
                address: row.get(2)?,
                port: row.get(3)?,
                added_date: row.get(4)?,
                last_checked: row.get(5)?,
                is_online: row.get(6)?,
                ping: row.get(7)?,
                player_count: row.get(8)?,
                max_players: row.get(9)?,
                map_name: row.get(10)?,
                game_mode: row.get(11)?,
            })
        })?;

        let mut servers = Vec::new();
        for server in server_iter {
            servers.push(server?);
        }
        Ok(servers)
    }

    pub fn get_server_by_id(&self, id: i64) -> Result<Option<Server>> {
        let mut stmt = self.conn.prepare(
            "SELECT id, name, address, port, added_date, last_checked, is_online 
             FROM servers WHERE id = ?1"
        )?;

        let mut rows = stmt.query_map([id], |row| {
            Ok(Server {
                id: row.get(0)?,
                name: row.get(1)?,
                address: row.get(2)?,
                port: row.get(3)?,
                added_date: row.get(4)?,
                last_checked: row.get(5)?,
                is_online: row.get(6)?,
            })
        })?;

        match rows.next() {
            Some(server) => Ok(Some(server?)),
            None => Ok(None),
        }
    }

    pub fn update_server_name(&self, id: i64, name: &str) -> Result<bool> {
        let rows_affected = self.conn.execute(
            "UPDATE servers SET name = ?1 WHERE id = ?2",
            params![name, id],
        )?;
        Ok(rows_affected > 0)
    }

    pub fn update_server_status(&self, id: i64, is_online: bool) -> Result<()> {
        self.conn.execute(
            "UPDATE servers SET last_checked = CURRENT_TIMESTAMP, is_online = ?1 WHERE id = ?2",
            params![is_online, id],
        )?;
        Ok(())
    }

    pub fn delete_server(&self, id: i64) -> Result<bool> {
        let rows_affected = self.conn.execute("DELETE FROM servers WHERE id = ?1", [id])?;
        Ok(rows_affected > 0)
    }

    pub fn add_server_history(
        &self,
        server_id: i64,
        is_online: bool,
        ping: Option<i32>,
        player_count: Option<i32>,
        max_players: Option<i32>,
        map_name: Option<&str>,
        game_mode: Option<&str>,
        server_info: Option<&str>,
    ) -> Result<()> {
        self.conn.execute(
            "INSERT INTO server_history 
             (server_id, is_online, ping, player_count, max_players, map_name, game_mode, server_info)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)",
            params![
                server_id,
                is_online,
                ping,
                player_count,
                max_players,
                map_name,
                game_mode,
                server_info
            ],
        )?;
        Ok(())
    }

    pub fn get_server_history(&self, server_id: i64, limit: i32) -> Result<Vec<ServerHistory>> {
        let mut stmt = self.conn.prepare(
            "SELECT id, server_id, checked_date, is_online, ping, player_count, 
                    max_players, map_name, game_mode, server_info
             FROM server_history 
             WHERE server_id = ?1 
             ORDER BY checked_date DESC 
             LIMIT ?2"
        )?;

        let history_iter = stmt.query_map(params![server_id, limit], |row| {
            Ok(ServerHistory {
                id: row.get(0)?,
                server_id: row.get(1)?,
                checked_date: row.get(2)?,
                is_online: row.get(3)?,
                ping: row.get(4)?,
                player_count: row.get(5)?,
                max_players: row.get(6)?,
                map_name: row.get(7)?,
                game_mode: row.get(8)?,
                server_info: row.get(9)?,
            })
        })?;

        let mut history = Vec::new();
        for entry in history_iter {
            history.push(entry?);
        }
        Ok(history)
    }
}
