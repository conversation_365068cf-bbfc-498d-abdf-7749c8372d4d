import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  getConnectedEdges,
  useReactFlow,
  Panel
} from '@xyflow/react';
import NodeTypeModal from './NodeTypeModal';
import CustomEdge from './CustomEdge';
import OrganizationInfo from './OrganizationInfo';
import SearchBar from './SearchBar';
import '@xyflow/react/dist/style.css';
import './NewFlowStyles.css';

// Firebase imports
import { db } from '../../services/firebase';
import {
  collection,
  doc,
  getDocs,
  getDoc,
  updateDoc,
  addDoc,
  query,
  where,
  orderBy
} from 'firebase/firestore';

// Custom components
import NewControlPanel from './NewControlPanel';
import NewArchivedPanel from './NewArchivedPanel';
import NewNotification from './NewNotification';
import NewConfirmDialog from './NewConfirmDialog';

// Node components
import OpportunityNode from './NewNodes/OpportunityNode';
import ContactNode from './NewNodes/ContactNode';
import CompanyNode from './NewNodes/CompanyNode';
import TaskNode from './NewNodes/TaskNode';
import ChecklistNode from './NewNodes/ChecklistNode';
import NoteNode from './NoteNode';
import PictureNode from './NewNodes/PictureNode';

// Import services
import { saveNodePositions, getNodePositions } from '../../services/nodePositionService';
import { getNodePositionsFromSession, saveNodePositionsToSession } from '../../services/sessionStorageService';

// Define node types
const nodeTypes = {
  opportunity: OpportunityNode,
  contact: ContactNode,
  company: CompanyNode,
  task: TaskNode,
  checklist: ChecklistNode,
  note: NoteNode,
  picture: PictureNode
};



// Define edge types
const edgeTypes = {
  custom: CustomEdge
};

const NewCRMFlow = ({ organization, user }) => {
  // Flow state
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // UI state
  const [showArchived, setShowArchived] = useState(false);
  const [showArchivedPanel, setShowArchivedPanel] = useState(false);
  const [archivedOpportunities, setArchivedOpportunities] = useState([]);
  const [notification, setNotification] = useState(null);

  // Node type modal state
  const [showNodeTypeModal, setShowNodeTypeModal] = useState(false);
  const [nodeTypeModalPosition, setNodeTypeModalPosition] = useState({ x: 0, y: 0 });
  const [pendingConnection, setPendingConnection] = useState(null);

  // Confirmation dialog state
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmDialogInfo, setConfirmDialogInfo] = useState({ title: '', message: '' });
  const [nodeToDelete, setNodeToDelete] = useState(null);

  // Sync state
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [hasRemoteChanges, setHasRemoteChanges] = useState(false);
  const [lastSync, setLastSync] = useState(Date.now());



  // Refs for performance optimization
  const nodesRef = useRef([]);
  const edgesRef = useRef([]);
  const reactFlowInstance = useReactFlow();

  // Keep refs in sync with state for performance
  useEffect(() => {
    nodesRef.current = nodes;
  }, [nodes]);

  useEffect(() => {
    edgesRef.current = edges;
  }, [edges]);

  // Load data on component mount
  useEffect(() => {
    if (organization?.id) {
      loadFlowData();
      loadArchivedOpportunities();
    }
  }, [organization?.id]);

  // Check for remote changes periodically
  useEffect(() => {
    if (!organization?.id) return;

    const interval = setInterval(() => {
      checkRemoteChanges();
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [organization?.id, lastSync]);

  // Listen for node update events
  useEffect(() => {
    const handleNodeUpdated = async (event) => {
      const { type, id, data } = event.detail;
      console.log(`Node updated event received: ${type}-${id}`);

      if (!type || !id) return;

      try {
        // If we don't have the updated data from the event, fetch it from Firestore
        let nodeData = data;

        if (!nodeData) {
          // Determine the collection based on node type
          const collectionMap = {
            'opportunity': 'opportunities',
            'contact': 'contacts',
            'company': 'companies',
            'task': 'tasks',
            'checklist': 'checklists',
            'note': 'notes',
            'picture': 'pictureNodes'
          };

          const collectionName = collectionMap[type];
          if (!collectionName) {
            console.error(`Unknown node type: ${type}`);
            return;
          }

          // Fetch the updated node data
          const docRef = doc(db, collectionName, id);
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            nodeData = docSnap.data();
          } else {
            console.error(`Node not found: ${type}-${id}`);
            return;
          }
        }

        // Update the node in the state
        const nodeId = `${type}-${id}`;

        setNodes(nds =>
          nds.map(node => {
            if (node.id === nodeId) {
              // Preserve the node's position and handlers
              return {
                ...node,
                data: {
                  ...node.data,
                  ...nodeData,
                  // Preserve the handlers
                  onEdit: node.data.onEdit,
                  onDelete: node.data.onDelete,
                  onArchive: node.data.onArchive,
                  onRestore: node.data.onRestore,
                  onContentChange: node.data.onContentChange,
                  onTaskDue: node.data.onTaskDue
                }
              };
            }
            return node;
          })
        );

        console.log(`Node ${nodeId} updated in flow view`);

        // Show notification
        setNotification({
          message: `${type.charAt(0).toUpperCase() + type.slice(1)} updated successfully`,
          type: 'success',
          duration: 3000
        });

      } catch (error) {
        console.error('Error updating node in flow view:', error);
        setNotification({
          message: 'Error updating node in flow view',
          type: 'error',
          duration: 3000
        });
      }
    };

    // Add event listener
    window.addEventListener('nodeUpdated', handleNodeUpdated);

    // Clean up
    return () => {
      window.removeEventListener('nodeUpdated', handleNodeUpdated);
    };
  }, []);

  // Load flow data (nodes and edges)
  const loadFlowData = useCallback(async () => {
    if (!organization?.id) return;

    try {
      // Load opportunities
      const opportunitiesQuery = query(
        collection(db, 'opportunities'),
        where('organizationId', '==', organization.id),
        where('archived', '==', false)
      );
      const opportunitiesSnapshot = await getDocs(opportunitiesQuery);

      // Load contacts
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('organizationId', '==', organization.id),
        where('archived', '==', false)
      );
      const contactsSnapshot = await getDocs(contactsQuery);

      // Load companies
      const companiesQuery = query(
        collection(db, 'companies'),
        where('organizationId', '==', organization.id),
        where('archived', '==', false)
      );
      const companiesSnapshot = await getDocs(companiesQuery);

      // Load tasks
      const tasksQuery = query(
        collection(db, 'tasks'),
        where('organizationId', '==', organization.id),
        where('archived', '==', false)
      );
      const tasksSnapshot = await getDocs(tasksQuery);

      // Load checklists
      const checklistsQuery = query(
        collection(db, 'checklists'),
        where('organizationId', '==', organization.id),
        where('archived', '==', false)
      );
      const checklistsSnapshot = await getDocs(checklistsQuery);

      // Load notes
      const notesQuery = query(
        collection(db, 'notes'),
        where('organizationId', '==', organization.id),
        where('archived', '==', false)
      );
      const notesSnapshot = await getDocs(notesQuery);

      // Load picture nodes
      const pictureNodesQuery = query(
        collection(db, 'pictureNodes'),
        where('organizationId', '==', organization.id),
        where('archived', '==', false)
      );
      const pictureNodesSnapshot = await getDocs(pictureNodesQuery);

      // Load relationships (edges)
      const relationshipsQuery = query(
        collection(db, 'relationships'),
        where('organizationId', '==', organization.id),
        where('archived', '==', false)
      );
      const relationshipsSnapshot = await getDocs(relationshipsQuery);

      // Create nodes from opportunities
      const opportunityNodes = opportunitiesSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: `opportunity-${doc.id}`,
          type: 'opportunity',
          position: { x: 0, y: 0 }, // Default position, will be updated
          data: {
            ...data,
            id: `opportunity-${doc.id}`,
            onEdit: handleEditNode,
            onDelete: handleDeleteNode,
            onArchive: handleDeleteNode
          }
        };
      });

      // Create nodes from contacts
      const contactNodes = contactsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: `contact-${doc.id}`,
          type: 'contact',
          position: { x: 0, y: 0 }, // Default position, will be updated
          data: {
            ...data,
            id: `contact-${doc.id}`,
            onEdit: handleEditNode,
            onDelete: handleDeleteNode
          }
        };
      });

      // Create nodes from companies
      const companyNodes = companiesSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: `company-${doc.id}`,
          type: 'company',
          position: { x: 0, y: 0 }, // Default position, will be updated
          data: {
            ...data,
            id: `company-${doc.id}`,
            onEdit: handleEditNode,
            onDelete: handleDeleteNode
          }
        };
      });

      // Create nodes from tasks
      const taskNodes = tasksSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: `task-${doc.id}`,
          type: 'task',
          position: { x: 0, y: 0 }, // Default position, will be updated
          data: {
            ...data,
            id: `task-${doc.id}`,
            onEdit: handleEditNode,
            onDelete: handleDeleteNode,
            onTaskDue: handleTaskDue
          }
        };
      });

      // Create nodes from checklists
      const checklistNodes = checklistsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: `checklist-${doc.id}`,
          type: 'checklist',
          position: { x: 0, y: 0 }, // Default position, will be updated
          data: {
            ...data,
            id: `checklist-${doc.id}`,
            onEdit: handleEditNode,
            onDelete: handleDeleteNode
          }
        };
      });

      // Create nodes from notes
      const noteNodes = notesSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: `note-${doc.id}`,
          type: 'note',
          position: { x: 0, y: 0 }, // Default position, will be updated
          data: {
            ...data,
            id: `note-${doc.id}`,
            onContentChange: handleNoteContentChange,
            onDelete: handleDeleteNode
          }
        };
      });

      // Create nodes from picture nodes
      const pictureNodes = pictureNodesSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: `picture-${doc.id}`,
          type: 'picture',
          position: { x: 0, y: 0 }, // Default position, will be updated
          data: {
            ...data,
            id: `picture-${doc.id}`,
            onDelete: handleDeleteNode
          }
        };
      });

      // Combine all nodes
      const allNodes = [
        ...opportunityNodes,
        ...contactNodes,
        ...companyNodes,
        ...taskNodes,
        ...checklistNodes,
        ...noteNodes,
        ...pictureNodes
      ];

      // Create edges from relationships
      const relationshipEdges = relationshipsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: `edge-${doc.id}`,
          source: data.sourceId,
          target: data.targetId,
          label: data.label || '',
          animated: data.animated || false,
          style: { stroke: '#b1b1b7', strokeWidth: 1.5 },
          type: 'custom',
          data: { onDelete: handleDeleteEdge }
        };
      });

      // Load node positions from session storage or Firebase
      const positions = await getNodePositions(organization.id);

      // Apply positions to nodes
      const nodesWithPositions = allNodes.map(node => {
        const savedPosition = positions[node.id];
        if (savedPosition) {
          return {
            ...node,
            position: savedPosition
          };
        }
        return node;
      });

      // Auto-layout nodes without positions
      const nodesWithoutPositions = nodesWithPositions.filter(node =>
        node.position.x === 0 && node.position.y === 0
      );

      if (nodesWithoutPositions.length > 0) {
        // Simple auto-layout: place nodes in a grid
        const gridSize = Math.ceil(Math.sqrt(nodesWithoutPositions.length));
        const spacing = 300;

        nodesWithoutPositions.forEach((node, index) => {
          const row = Math.floor(index / gridSize);
          const col = index % gridSize;
          node.position = {
            x: col * spacing,
            y: row * spacing
          };
        });
      }

      // Update state
      setNodes(nodesWithPositions);
      setEdges(relationshipEdges);

      // Fit view after loading
      setTimeout(() => {
        reactFlowInstance.fitView({ padding: 0.2 });
      }, 100);

      // Update last sync time
      setLastSync(Date.now());
      setHasUnsavedChanges(false);
      setHasRemoteChanges(false);

    } catch (error) {
      console.error('Error loading flow data:', error);
      setNotification({
        message: 'Error loading flow data',
        type: 'error',
        duration: 3000
      });
    }
  }, [organization?.id, reactFlowInstance]);

  // Load archived opportunities
  const loadArchivedOpportunities = useCallback(async () => {
    if (!organization?.id) return;

    try {
      const archivedQuery = query(
        collection(db, 'opportunities'),
        where('organizationId', '==', organization.id),
        where('archived', '==', true),
        orderBy('archivedAt', 'desc')
      );

      const snapshot = await getDocs(archivedQuery);
      const archivedDocs = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setArchivedOpportunities(archivedDocs);
    } catch (error) {
      console.error('Error loading archived opportunities:', error);
    }
  }, [organization?.id]);

  // Check for remote changes
  const checkRemoteChanges = useCallback(async () => {
    if (!organization?.id || !lastSync) return;

    try {
      // Query for node positions updated after last sync
      const q = query(
        collection(db, 'nodePositions'),
        where('organizationId', '==', organization.id),
        where('updatedAt', '>', new Date(lastSync))
      );

      const snapshot = await getDocs(q);

      // If there are changes, notify the user
      if (!snapshot.empty) {
        setHasRemoteChanges(true);
      }
    } catch (error) {
      console.error('Error checking for remote changes:', error);
    }
  }, [organization?.id, lastSync]);

  // Track double-click
  const lastClickTimeRef = useRef(0);
  const lastClickNodeRef = useRef(null);

  // Simple node click handler
  const onNodeClick = useCallback((_, node) => {
    // No special handling needed
  }, []);

  // Handle node edit
  const handleEditNode = useCallback((nodeId) => {
    // Extract the node type and ID
    const [nodeType, actualId] = nodeId.split('-');

    // Emit an event for the parent component to handle
    const event = new CustomEvent('editNode', {
      detail: { type: nodeType, id: actualId }
    });
    window.dispatchEvent(event);
  }, []);

  // Handle edge delete button click
  const handleDeleteEdge = useCallback(async (edgeId) => {
    try {
      // Find the edge to delete
      const edgeToDelete = edgesRef.current.find(edge => edge.id === edgeId);

      if (!edgeToDelete) {
        console.error('Edge not found:', edgeId);
        return;
      }

      // Archive the relationship in Firestore
      const relationshipId = edgeId.replace('edge-', '');

      // Skip temporary edges
      if (edgeId.startsWith('temp-')) {
        // Just remove from state for temporary edges
        setEdges(eds => eds.filter(e => e.id !== edgeId));
        return;
      }

      await updateDoc(doc(db, 'relationships', relationshipId), {
        archived: true,
        archivedAt: new Date()
      });

      // Remove the edge from the state
      setEdges(eds => eds.filter(e => e.id !== edgeId));

      // Mark changes as unsaved
      setHasUnsavedChanges(true);

      // Show notification
      setNotification({
        message: 'Connection deleted successfully',
        type: 'success',
        duration: 3000
      });

    } catch (error) {
      console.error('Error deleting edge:', error);
      setNotification({
        message: 'Error deleting connection',
        type: 'error',
        duration: 3000
      });
    }
  }, []);

  // Handle node deletion (archive for all node types)
  const handleDeleteNode = useCallback((nodeId) => {
    // Extract the node type from the ID
    const [nodeType] = nodeId.split('-');

    // Get a user-friendly node type name
    const nodeTypeName = nodeType.charAt(0).toUpperCase() + nodeType.slice(1);

    // Set the node to delete
    setNodeToDelete(nodeId);

    // For opportunity nodes, show special message about connected nodes
    if (nodeType === 'opportunity') {
      const title = 'Archive Opportunity';
      const message = 'Are you sure you want to archive this opportunity and all connected nodes? They will be removed from the flow but preserved in the database.';

      setConfirmDialogInfo({ title, message });
    } else {
      // For all other node types, show a standard confirmation
      const title = `Archive ${nodeTypeName}`;
      const message = `Are you sure you want to archive this ${nodeType}? It will be removed from the flow but preserved in the database.`;

      setConfirmDialogInfo({ title, message });
    }

    // Show the confirmation dialog
    setShowConfirmDialog(true);
  }, []);



  // Confirm node deletion (now archives instead of deleting)
  const confirmDeleteNode = useCallback(async () => {
    try {
      if (!nodeToDelete) return;

      // Extract the actual ID from the node ID (format: "type-id")
      const [nodeType, actualId] = nodeToDelete.split('-');

      // Archive the node in Firestore instead of deleting it
      if (actualId) {
        // Determine the collection based on node type
        const collectionMap = {
          'opportunity': 'opportunities',
          'contact': 'contacts',
          'company': 'companies',
          'task': 'tasks',
          'checklist': 'checklists',
          'note': 'notes',
          'picture': 'pictureNodes'
        };

        const collectionName = collectionMap[nodeType];

        if (!collectionName) {
          console.error(`Unknown node type: ${nodeType}`);
          return;
        }

        // Update the document to mark it as archived
        await updateDoc(doc(db, collectionName, actualId), {
          archived: true,
          archivedAt: new Date()
        });

        // Find connected edges to this node
        const connectedEdges = getConnectedEdges([{ id: nodeToDelete }], edgesRef.current);

        // Get all connected node IDs
        const connectedNodeIds = new Set();
        connectedEdges.forEach(edge => {
          if (edge.source !== nodeToDelete) {
            connectedNodeIds.add(edge.source);
          }
          if (edge.target !== nodeToDelete) {
            connectedNodeIds.add(edge.target);
          }
        });

        // Archive all connected nodes if this is an opportunity
        if (nodeType === 'opportunity') {
          // Archive each connected node
          for (const connectedNodeId of connectedNodeIds) {
            const [connectedType, connectedId] = connectedNodeId.split('-');
            const connectedCollectionName = collectionMap[connectedType];

            if (connectedCollectionName && connectedId) {
              // Archive the connected node
              await updateDoc(doc(db, connectedCollectionName, connectedId), {
                archived: true,
                archivedAt: new Date()
              });
            }
          }
        }
        // For non-opportunity nodes with connections, create connections between connected nodes
        else if (connectedNodeIds.size >= 2) {
          // Create new relationships between the connected nodes
          const connectedNodesArray = Array.from(connectedNodeIds);

          for (let i = 0; i < connectedNodesArray.length; i++) {
            for (let j = i + 1; j < connectedNodesArray.length; j++) {
              const source = connectedNodesArray[i];
              const target = connectedNodesArray[j];

              // Add a new relationship
              await addDoc(collection(db, 'relationships'), {
                sourceId: source,
                targetId: target,
                label: '',
                organizationId: organization.id,
                createdAt: new Date(),
                createdBy: user.uid,
                archived: false
              });

              // Add a new edge to the state
              const newEdgeId = `temp-${Date.now()}-${i}-${j}`;
              setEdges(eds => [
                ...eds,
                {
                  id: newEdgeId,
                  source,
                  target,
                  animated: true,
                  style: { stroke: '#b1b1b7', strokeWidth: 1.5 },
                  type: 'custom',
                  data: { onDelete: handleDeleteEdge }
                }
              ]);
            }
          }
        }

        // Archive relationships instead of deleting them
        for (const edge of connectedEdges) {
          const relationshipId = edge.id.replace('edge-', '');
          await updateDoc(doc(db, 'relationships', relationshipId), {
            archived: true,
            archivedAt: new Date()
          });
        }

        // Remove the node from the state
        setNodes(nds => nds.filter(node => node.id !== nodeToDelete));

        // If this is an opportunity, also remove all connected nodes
        if (nodeType === 'opportunity') {
          setNodes(nds => nds.filter(node => !connectedNodeIds.has(node.id)));
        }

        // Remove all connected edges from the state
        setEdges(eds => eds.filter(edge =>
          edge.source !== nodeToDelete && edge.target !== nodeToDelete
        ));

        // If this is an opportunity, also remove edges between connected nodes
        if (nodeType === 'opportunity') {
          setEdges(eds => eds.filter(edge =>
            !connectedNodeIds.has(edge.source) && !connectedNodeIds.has(edge.target)
          ));
        }

        // Mark changes as unsaved
        setHasUnsavedChanges(true);

        // Refresh archived opportunities list if needed
        if (nodeType === 'opportunity') {
          loadArchivedOpportunities();
        }

        // Show notification with count of archived nodes
        setNotification({
          message: nodeType === 'opportunity'
            ? `Opportunity and ${connectedNodeIds.size} connected nodes archived successfully`
            : `${nodeType.charAt(0).toUpperCase() + nodeType.slice(1)} archived successfully`,
          type: 'success',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('Error archiving node:', error);
      setNotification({
        message: 'Error archiving node',
        type: 'error',
        duration: 3000
      });
    } finally {
      // Close the confirmation dialog
      setShowConfirmDialog(false);
      setNodeToDelete(null);
    }
  }, [nodeToDelete, loadArchivedOpportunities, organization?.id, user?.uid]);

  // Restore an archived opportunity
  const handleRestoreOpportunity = useCallback(async (opportunityId) => {
    try {
      // Update the document to mark it as not archived
      await updateDoc(doc(db, 'opportunities', opportunityId), {
        archived: false,
        archivedAt: null
      });

      // Refresh the flow data and archived opportunities list
      loadFlowData();
      loadArchivedOpportunities();

      // Show notification
      setNotification({
        message: 'Opportunity restored successfully',
        type: 'success',
        duration: 3000
      });
    } catch (error) {
      console.error('Error restoring opportunity:', error);
      setNotification({
        message: 'Error restoring opportunity',
        type: 'error',
        duration: 3000
      });
    }
  }, [loadFlowData, loadArchivedOpportunities]);

  // Track connection start
  const onConnectStart = useCallback((_, { nodeId }) => {
    // Find the source node
    const sourceNode = nodesRef.current.find(node => node.id === nodeId);

    // Store the source node ID and type
    setPendingConnection({
      sourceId: nodeId,
      sourceType: sourceNode ? sourceNode.type : null
    });
  }, []);

  // Handle connection end
  const onConnectEnd = useCallback((event) => {
    // Only proceed if we have a pending connection
    if (!pendingConnection) return;

    // Get the mouse position
    const { clientX, clientY } = event;
    const { x, y } = reactFlowInstance.screenToFlowPosition({ x: clientX, y: clientY });

    // Check if the connection ended on a node
    const targetNode = nodesRef.current.find(node => {
      // Simple hit detection - check if the mouse is within the node's bounds
      const nodeWidth = 220; // Default node width
      const nodeHeight = 150; // Approximate node height
      return (
        x >= node.position.x &&
        x <= node.position.x + nodeWidth &&
        y >= node.position.y &&
        y <= node.position.y + nodeHeight
      );
    });

    if (targetNode) {
      // If dropped on a node, create a connection normally
      const params = {
        source: pendingConnection.sourceId,
        target: targetNode.id
      };
      handleCreateConnection(params);

      // Reset the pending connection after creating the connection
      setPendingConnection(null);
    } else {
      // If dropped on empty canvas, show the node type modal for all node types
      // We're not using the position anymore since the modal is centered
      // but we'll store the position for node creation
      setNodeTypeModalPosition({ x, y });
      setShowNodeTypeModal(true);
      // Don't reset pendingConnection here, we need it for the modal selection
    }
  }, [pendingConnection, reactFlowInstance]);

  // Handle node type selection from modal
  const handleNodeTypeSelect = useCallback(async (nodeType) => {
    if (!pendingConnection || !organization?.id || !user?.uid) return;

    try {
      // Store the source node ID before resetting pendingConnection
      const sourceNodeId = pendingConnection.sourceId;

      // Get the position from the modal position
      const position = reactFlowInstance.screenToFlowPosition(nodeTypeModalPosition);

      console.log('Creating new node of type:', nodeType, 'at position:', position);

      // Create a new node based on the selected type
      const newNodeId = await createNewNode(nodeType, position);

      if (newNodeId) {
        console.log('New node created with ID:', newNodeId);

        // Create a connection between the source node and the new node
        const params = {
          source: sourceNodeId,
          target: newNodeId
        };

        console.log('Creating connection:', params);
        await handleCreateConnection(params);

        // Show success notification
        setNotification({
          message: `New ${nodeType} node created successfully`,
          type: 'success',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('Error creating new node:', error);
      setNotification({
        message: 'Error creating new node: ' + error.message,
        type: 'error',
        duration: 3000
      });
    } finally {
      // Always reset the pending connection after handling the node type selection
      setPendingConnection(null);
    }
  }, [pendingConnection, organization?.id, user?.uid, nodeTypeModalPosition, reactFlowInstance]);

  // Create a new node
  const createNewNode = async (nodeType, position) => {
    try {
      console.log('Creating new node of type:', nodeType, 'at position:', position);

      // Validate inputs
      if (!nodeType) {
        throw new Error('Node type is required');
      }

      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number') {
        throw new Error('Valid position is required');
      }

      // Default data for each node type
      const nodeData = {
        opportunity: {
          name: 'New Opportunity',
          company: '',
          value: 0,
          status: 'New',
          closeDate: new Date().toISOString().split('T')[0],
          description: '',
          archived: false
        },
        contact: {
          firstName: 'New',
          lastName: 'Contact',
          email: '',
          phone: '',
          company: '',
          title: '',
          archived: false
        },
        company: {
          name: 'New Company',
          industry: '',
          size: '',
          website: '',
          archived: false
        },
        task: {
          title: 'New Task',
          description: '',
          dueDate: new Date().toISOString().split('T')[0],
          priority: 'Medium',
          status: 'Not Started',
          assignedTo: user.uid,
          archived: false
        },
        checklist: {
          title: 'New Checklist',
          description: '',
          items: [
            { id: '1', text: 'Item 1', completed: false }
          ],
          archived: false
        },
        note: {
          content: 'Double-click to edit this note',
          archived: false
        },
        picture: {
          title: 'Picture Collection',
          description: 'Drag and drop files here',
          files: [],
          archived: false
        },
        organization: {
          name: 'New Organization',
          description: '',
          industry: '',
          website: '',
          archived: false
        }
      };

      // Add common fields
      const commonData = {
        createdBy: user.uid,
        createdAt: new Date(),
        organizationId: organization.id
      };

      // Determine the collection based on node type
      const collectionMap = {
        'opportunity': 'opportunities',
        'contact': 'contacts',
        'company': 'companies',
        'task': 'tasks',
        'checklist': 'checklists',
        'note': 'notes',
        'picture': 'pictureNodes',
        'organization': 'organizations'
      };

      const collectionName = collectionMap[nodeType];

      if (!collectionName) {
        throw new Error(`Unknown node type: ${nodeType}`);
      }

      console.log('Adding node to Firestore collection:', collectionName);

      // Add the node to Firestore
      const docRef = await addDoc(collection(db, collectionName), {
        ...nodeData[nodeType],
        ...commonData
      });

      console.log('Node added to Firestore with ID:', docRef.id);

      // Create a new node for the flow
      const newNodeId = `${nodeType}-${docRef.id}`;

      // Prepare the node data
      const nodeDataWithHandlers = {
        ...nodeData[nodeType],
        ...commonData,
        id: newNodeId,
        onEdit: handleEditNode,
        onDelete: handleDeleteNode,
        onArchive: handleDeleteNode,
        onRestore: handleRestoreOpportunity
      };

      // Add specific handlers based on node type
      if (nodeType === 'note') {
        nodeDataWithHandlers.onContentChange = handleNoteContentChange;
      }

      const newNode = {
        id: newNodeId,
        type: nodeType,
        position,
        data: nodeDataWithHandlers
      };

      console.log('Adding node to React Flow state:', newNode);

      // Add the node to the state
      setNodes(nds => {
        const updatedNodes = [...nds, newNode];
        console.log('Updated nodes count:', updatedNodes.length);
        return updatedNodes;
      });

      // Mark changes as unsaved
      setHasUnsavedChanges(true);

      return newNodeId;
    } catch (error) {
      console.error('Error creating new node:', error);
      setNotification({
        message: 'Error creating new node: ' + error.message,
        type: 'error',
        duration: 3000
      });
      return null;
    }
  };

  // Handle edge creation
  const handleCreateConnection = async (params) => {
    try {
      console.log('Creating connection with params:', params);

      // Validate params
      if (!params.source || !params.target) {
        throw new Error('Invalid connection parameters: source and target are required');
      }

      // Create a new relationship in Firestore
      const newRelationship = {
        sourceId: params.source,
        targetId: params.target,
        label: '',
        organizationId: organization.id,
        createdAt: new Date(),
        createdBy: user.uid,
        archived: false
      };

      console.log('Adding relationship to Firestore:', newRelationship);

      // Add the relationship to Firestore
      const docRef = await addDoc(collection(db, 'relationships'), newRelationship);
      console.log('Relationship added with ID:', docRef.id);

      // Create a new edge with the relationship ID
      const newEdge = {
        ...params,
        id: `edge-${docRef.id}`,
        animated: false,
        label: '',
        style: { stroke: '#b1b1b7', strokeWidth: 1.5 },
        type: 'custom',
        data: { onDelete: handleDeleteEdge }
      };

      console.log('Adding edge to state:', newEdge);

      // Add the edge to the state
      setEdges(eds => {
        const updatedEdges = addEdge(newEdge, eds);
        console.log('Updated edges:', updatedEdges);
        return updatedEdges;
      });

      // Mark changes as unsaved
      setHasUnsavedChanges(true);

      return docRef.id;
    } catch (error) {
      console.error('Error creating relationship:', error);
      setNotification({
        message: 'Error creating relationship: ' + error.message,
        type: 'error',
        duration: 3000
      });
      throw error; // Re-throw the error so the caller can handle it
    }
  };

  // Handle edge creation from connect event
  const onConnect = useCallback(async (params) => {
    handleCreateConnection(params);
  }, [organization?.id, user?.uid]);

  // Handle node position changes
  const onNodeDragStop = useCallback((_, node) => {
    if (!organization?.id) return;

    // Save the node position to session storage
    const sessionPositions = getNodePositionsFromSession(organization.id) || {};
    sessionPositions[node.id] = node.position;

    // Save updated positions to session storage
    saveNodePositionsToSession(organization.id, sessionPositions);

    // Mark changes as unsaved
    setHasUnsavedChanges(true);
  }, [organization?.id]);

  // Handle node position changes
  const onNodesDelete = useCallback((_) => {
    // This is handled by our custom delete functions
    // Prevent default deletion behavior
    return false;
  }, []);

  // Handle edge deletion
  const onEdgesDelete = useCallback(async (edges) => {
    try {
      // Archive each relationship in Firestore
      for (const edge of edges) {
        const relationshipId = edge.id.replace('edge-', '');

        // Skip temporary edges
        if (edge.id.startsWith('temp-')) continue;

        await updateDoc(doc(db, 'relationships', relationshipId), {
          archived: true,
          archivedAt: new Date()
        });
      }

      // Mark changes as unsaved
      setHasUnsavedChanges(true);

    } catch (error) {
      console.error('Error deleting relationships:', error);
      setNotification({
        message: 'Error deleting relationships',
        type: 'error',
        duration: 3000
      });
    }
  }, []);



  // Save node positions to Firebase
  const handleSaveToFirebase = useCallback(async () => {
    if (!organization?.id || !user?.uid) return;

    try {
      // Get current node positions
      const positions = {};
      nodesRef.current.forEach(node => {
        positions[node.id] = node.position;
      });

      // Create nodes array with positions for the saveNodePositions function
      const nodesWithPositions = nodesRef.current.map(node => ({
        id: node.id,
        position: node.position
      }));

      // Save positions to Firebase
      const result = await saveNodePositions(user.uid, organization.id, nodesWithPositions);

      if (!result.success) {
        throw new Error(result.error || 'Failed to save node positions');
      }

      // Update last sync time
      setLastSync(Date.now());
      setHasUnsavedChanges(false);
      setHasRemoteChanges(false);

      // Show notification
      setNotification({
        message: 'Flow saved to Firebase successfully',
        type: 'success',
        duration: 3000
      });
    } catch (error) {
      console.error('Error saving to Firebase:', error);
      setNotification({
        message: 'Error saving to Firebase: ' + error.message,
        type: 'error',
        duration: 3000
      });
    }
  }, [organization?.id, user?.uid]);

  // Sync with Firebase
  const handleSyncWithFirebase = useCallback(async () => {
    if (!organization?.id || !user?.uid) return;

    try {
      // Load flow data from Firebase
      await loadFlowData();

      // Show notification
      setNotification({
        message: 'Flow synced with Firebase successfully',
        type: 'success',
        duration: 3000
      });
    } catch (error) {
      console.error('Error syncing with Firebase:', error);
      setNotification({
        message: 'Error syncing with Firebase',
        type: 'error',
        duration: 3000
      });
    }
  }, [organization?.id, user?.uid, loadFlowData]);

  // Toggle archived opportunities panel
  const handleToggleArchived = useCallback(() => {
    setShowArchived(!showArchived);
    setShowArchivedPanel(!showArchived);
  }, [showArchived]);

  // Close archived opportunities panel
  const handleCloseArchivedPanel = useCallback(() => {
    setShowArchivedPanel(false);
  }, []);

  // Clear notification
  const handleClearNotification = useCallback(() => {
    setNotification(null);
  }, []);



  // Handle node found from search
  const handleNodeFound = useCallback((nodeId) => {
    // Find the node
    const node = nodesRef.current.find(n => n.id === nodeId);

    if (node) {
      // Select the node
      const nextNodes = nodesRef.current.map(n => ({
        ...n,
        selected: n.id === nodeId
      }));

      setNodes(nextNodes);

      // Center view on the node with animation
      reactFlowInstance.setCenter(
        node.position.x + 110, // Add half the node width
        node.position.y + 75,  // Add half the node height
        { duration: 800, zoom: 1.5 }
      );

      // Show notification
      setNotification({
        message: `Found node: ${getNodeDisplayName(node)}`,
        type: 'success',
        duration: 2000
      });
    }
  }, [reactFlowInstance]);

  // Handle note content change
  const handleNoteContentChange = useCallback(async (nodeId, content) => {
    try {
      // Extract the actual ID from the node ID (format: "note-id")
      const [nodeType, actualId] = nodeId.split('-');

      if (nodeType !== 'note' || !actualId) {
        console.error('Invalid note node ID:', nodeId);
        return;
      }

      // Update the note in Firestore
      await updateDoc(doc(db, 'notes', actualId), {
        content: content,
        updatedAt: new Date()
      });

      // Update the node in the state
      setNodes(nds =>
        nds.map(node =>
          node.id === nodeId
            ? {
              ...node,
              data: {
                ...node.data,
                content: content
              }
            }
            : node
        )
      );

      // Mark changes as unsaved
      setHasUnsavedChanges(true);

    } catch (error) {
      console.error('Error updating note content:', error);
      setNotification({
        message: 'Error updating note content',
        type: 'error',
        duration: 3000
      });
    }
  }, []);



  // Handle task due notification
  const handleTaskDue = useCallback((nodeId) => {
    // Find the node
    const node = nodesRef.current.find(n => n.id === nodeId);

    if (node && reactFlowInstance) {
      // Center view on the node with animation
      reactFlowInstance.setCenter(
        node.position.x + 110, // Add half the node width
        node.position.y + 75,  // Add half the node height
        { duration: 1000, zoom: 1.5 }
      );

      // Show notification
      setNotification({
        message: `Task due: ${node.data.title}`,
        type: 'warning',
        duration: 5000
      });
    }
  }, [reactFlowInstance]);

  // Get a display name for a node
  const getNodeDisplayName = (node) => {
    if (!node || !node.data) return 'Unknown Node';

    switch (node.type) {
      case 'opportunity':
        return node.data.name || 'Unnamed Opportunity';
      case 'contact':
        return `${node.data.firstName || ''} ${node.data.lastName || ''}`.trim() || 'Unnamed Contact';
      case 'company':
        return node.data.name || 'Unnamed Company';
      case 'task':
        return node.data.title || 'Unnamed Task';
      case 'checklist':
        return node.data.title || 'Unnamed Checklist';
      case 'note':
        return 'Note';
      case 'picture':
        return 'Picture Collection';
      default:
        return 'Unknown Node';
    }
  };

  return (
    <div className="flow-container">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onConnectStart={onConnectStart}
        onConnectEnd={onConnectEnd}
        onNodeClick={onNodeClick}
        onNodeDragStop={onNodeDragStop}
        onNodesDelete={onNodesDelete}
        onEdgesDelete={onEdgesDelete}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        attributionPosition="bottom-left"

      >
        {/* Search Bar */}
        <SearchBar
          nodes={nodesRef.current}
          onNodeFound={handleNodeFound}
        />

        <Background
          variant="dots"
          gap={20}
          size={1.5}
          color="rgba(255, 255, 255, 0.5)"
        />

        <NewControlPanel
          onToggleArchived={handleToggleArchived}
          onSaveToFirebase={handleSaveToFirebase}
          onSyncWithFirebase={handleSyncWithFirebase}
          showArchived={showArchived}
          hasUnsavedChanges={hasUnsavedChanges}
          hasRemoteChanges={hasRemoteChanges}
        />

        {/* Organization Info */}
        <OrganizationInfo organization={organization} />
      </ReactFlow>

      {/* Archived Opportunities Panel */}
      {showArchivedPanel && (
        <NewArchivedPanel
          archivedOpportunities={archivedOpportunities}
          onRestore={handleRestoreOpportunity}
          onClose={handleCloseArchivedPanel}
        />
      )}

      {/* Notification */}
      {notification && (
        <NewNotification
          message={notification.message}
          type={notification.type}
          duration={notification.duration}
          onClose={handleClearNotification}
        />
      )}

      {/* Confirmation Dialog */}
      <NewConfirmDialog
        show={showConfirmDialog}
        title={confirmDialogInfo.title}
        message={confirmDialogInfo.message}
        onConfirm={confirmDeleteNode}
        onCancel={() => setShowConfirmDialog(false)}
      />

      {/* Node Type Modal */}
      <NodeTypeModal
        isOpen={showNodeTypeModal}
        onClose={() => setShowNodeTypeModal(false)}
        onSelectNodeType={handleNodeTypeSelect}
        position={nodeTypeModalPosition}
        sourceType={pendingConnection?.sourceType}
      />
    </div>
  );
};

export default NewCRMFlow;
