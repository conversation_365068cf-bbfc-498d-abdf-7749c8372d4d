.note-node {
  width: 220px;
  background-color: #fffde7;
  border: 1px solid #e6ee9c;
  border-radius: 8px;
  color: #424242;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  overflow: hidden;
}

.note-node.selected {
  box-shadow: 0 0 0 2px #ffd54f, 0 4px 10px rgba(0, 0, 0, 0.2);
  border-color: #ffd54f;
}

.note-header {
  background-color: #fff9c4;
  padding: 8px 12px;
  border-bottom: 1px solid #e6ee9c;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
}

.note-header i {
  color: #fbc02d;
  margin-right: 8px;
  font-size: 16px;
}

.note-title {
  flex: 1;
}

.note-edit-button {
  background: transparent;
  border: none;
  color: #9e9e9e;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.note-edit-button:hover {
  color: #424242;
  background-color: rgba(0, 0, 0, 0.05);
}

.note-content {
  padding: 12px;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}

.note-text {
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 14px;
  line-height: 1.5;
}

.note-placeholder {
  color: #9e9e9e;
  font-style: italic;
}

.note-textarea {
  width: 100%;
  min-height: 100px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  font-family: inherit;
  background-color: #fff;
  color: #424242;
}

.note-textarea:focus {
  outline: none;
  border-color: #ffd54f;
  box-shadow: 0 0 0 2px rgba(255, 213, 79, 0.3);
}

.note-handle {
  width: 8px;
  height: 8px;
  background-color: #ffd54f;
  border: 1px solid #fbc02d;
}

/* Scrollbar styling */
.note-content::-webkit-scrollbar {
  width: 6px;
}

.note-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.note-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.note-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
