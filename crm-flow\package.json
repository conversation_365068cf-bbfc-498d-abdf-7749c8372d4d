{"name": "crm-flow", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "node build-production.js", "lint": "eslint .", "preview": "vite preview", "serve": "vite preview", "start": "npm run dev", "start:prod": "node start-server.js", "analyze": "vite build --mode analyze", "clean": "rimraf dist node_modules", "clear-cache": "rimraf node_modules/.vite"}, "dependencies": {"@reactflow/node-resizer": "^2.2.14", "@xyflow/react": "^12.5.5", "bootstrap": "^5.3.5", "express": "^4.18.2", "firebase": "^11.6.0", "i18next": "^25.0.2", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.1", "react-router-dom": "^7.5.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "rimraf": "^5.0.5", "vite": "^6.2.0"}}