import React, { useState, useEffect } from 'react';
import OpportunityFlow from './OpportunityFlow';
import OpportunityForm from './OpportunityForm';
import OpportunityDetails from './OpportunityDetails';
import { opportunityService } from '../services/database';
import { BarChart3, DollarSign, TrendingUp, Users } from 'lucide-react';

const CRMDashboard = () => {
  const [selectedOpportunity, setSelectedOpportunity] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editingOpportunity, setEditingOpportunity] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [stats, setStats] = useState({
    totalOpportunities: 0,
    totalValue: 0,
    averageProbability: 0,
    stageDistribution: {}
  });

  // Load statistics
  const loadStats = () => {
    try {
      const opportunities = opportunityService.getAll();
      
      const totalOpportunities = opportunities.length;
      const totalValue = opportunities.reduce((sum, opp) => sum + (opp.value || 0), 0);
      const averageProbability = totalOpportunities > 0 
        ? opportunities.reduce((sum, opp) => sum + (opp.probability || 0), 0) / totalOpportunities 
        : 0;
      
      const stageDistribution = opportunities.reduce((acc, opp) => {
        acc[opp.stage] = (acc[opp.stage] || 0) + 1;
        return acc;
      }, {});

      setStats({
        totalOpportunities,
        totalValue,
        averageProbability: Math.round(averageProbability),
        stageDistribution
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  const handleNodeSelect = (opportunity) => {
    setSelectedOpportunity(opportunity);
    setShowDetails(true);
  };

  const handleAddOpportunity = () => {
    setEditingOpportunity(null);
    setShowForm(true);
  };

  const handleEditOpportunity = (opportunity) => {
    setEditingOpportunity(opportunity);
    setShowForm(true);
  };

  const handleFormSave = () => {
    setShowForm(false);
    setEditingOpportunity(null);
    loadStats();
    // Trigger flow refresh by closing and reopening details if needed
    if (showDetails) {
      setShowDetails(false);
      setTimeout(() => setShowDetails(true), 100);
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingOpportunity(null);
  };

  const handleDetailsClose = () => {
    setShowDetails(false);
    setSelectedOpportunity(null);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <header style={{
        background: 'white',
        borderBottom: '1px solid #e5e7eb',
        padding: '16px 24px'
      }}>
        <h1 style={{ 
          margin: 0, 
          fontSize: '28px', 
          fontWeight: 'bold',
          color: '#1f2937'
        }}>
          CRM Opportunities Dashboard
        </h1>
      </header>

      {/* Stats Bar */}
      <div style={{
        background: 'white',
        borderBottom: '1px solid #e5e7eb',
        padding: '16px 24px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px',
            background: '#f8fafc',
            borderRadius: '8px'
          }}>
            <BarChart3 size={24} style={{ color: '#3b82f6' }} />
            <div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>
                {stats.totalOpportunities}
              </div>
              <div style={{ fontSize: '14px', color: '#6b7280' }}>
                Total Opportunities
              </div>
            </div>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px',
            background: '#f0fdf4',
            borderRadius: '8px'
          }}>
            <DollarSign size={24} style={{ color: '#059669' }} />
            <div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>
                {formatCurrency(stats.totalValue)}
              </div>
              <div style={{ fontSize: '14px', color: '#6b7280' }}>
                Total Pipeline Value
              </div>
            </div>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px',
            background: '#fef3c7',
            borderRadius: '8px'
          }}>
            <TrendingUp size={24} style={{ color: '#d97706' }} />
            <div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>
                {stats.averageProbability}%
              </div>
              <div style={{ fontSize: '14px', color: '#6b7280' }}>
                Average Probability
              </div>
            </div>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px',
            background: '#fdf2f8',
            borderRadius: '8px'
          }}>
            <Users size={24} style={{ color: '#be185d' }} />
            <div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>
                {Object.keys(stats.stageDistribution).length}
              </div>
              <div style={{ fontSize: '14px', color: '#6b7280' }}>
                Active Stages
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, position: 'relative' }}>
        <OpportunityFlow
          onNodeSelect={handleNodeSelect}
          onAddOpportunity={handleAddOpportunity}
        />
      </div>

      {/* Modals */}
      {showForm && (
        <OpportunityForm
          opportunity={editingOpportunity}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
        />
      )}

      {showDetails && selectedOpportunity && (
        <OpportunityDetails
          opportunity={selectedOpportunity}
          onClose={handleDetailsClose}
          onEdit={handleEditOpportunity}
        />
      )}
    </div>
  );
};

export default CRMDashboard;
