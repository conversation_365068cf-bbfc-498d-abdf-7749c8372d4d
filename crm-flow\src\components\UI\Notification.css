.notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 90%;
  width: auto;
  animation: fadeIn 0.3s ease-in-out, fadeOut 0.5s ease-in-out 4.5s forwards;
}

.notification-content {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.notification-error {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  color: #b91c1c;
}

.notification-warning {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  color: #b45309;
}

.notification-info {
  background-color: #e0f2fe;
  border: 1px solid #3b82f6;
  color: #1d4ed8;
}

.notification-success {
  background-color: #dcfce7;
  border: 1px solid #10b981;
  color: #047857;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translate(-50%, 0);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -20px);
  }
}
