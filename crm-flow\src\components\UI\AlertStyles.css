/* Custom Alert Styling */
.alert {
  border-radius: 8px;
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  position: relative;
  font-weight: 500;
}

.alert-success {
  background-color: #ecfdf5;
  color: #065f46;
  border-left: 4px solid #10b981;
}

.alert-danger {
  background-color: #fef2f2;
  color: #b91c1c;
  border-left: 4px solid #ef4444;
}

.alert-warning {
  background-color: #fffbeb;
  color: #92400e;
  border-left: 4px solid #f59e0b;
}

.alert-info {
  background-color: #eff6ff;
  color: #1e40af;
  border-left: 4px solid #3b82f6;
}

.alert-dismissible {
  padding-right: 3rem;
}

.alert .btn-close {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 1rem;
  padding: 0.5rem;
  background-color: transparent;
  border-radius: 50%;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.alert .btn-close:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

/* Animation for alerts */
.alert.fade.show {
  animation: alertFadeIn 0.3s ease forwards;
}

@keyframes alertFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
