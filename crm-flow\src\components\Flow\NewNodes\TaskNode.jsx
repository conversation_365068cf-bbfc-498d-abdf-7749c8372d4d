import { memo, useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { formatUserNameCached } from '../../../services/userService';
import NodeMenu from './NodeMenu';
import audioPlayer from '../../../utils/audioPlayer';

const TaskNode = ({ id, data, selected }) => {
  const [assignedUserName, setAssignedUserName] = useState('Loading...');
  const [isDue, setIsDue] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const priorityColors = {
    'High': 'bg-danger',
    'Medium': 'bg-warning',
    'Low': 'bg-info'
  };

  const formatDate = (date) => {
    if (!date) return 'Unknown';
    return typeof date === 'object' && date.toDate
      ? date.toDate().toLocaleDateString()
      : new Date(date).toLocaleDateString();
  };

  // Get the assigned user name
  useEffect(() => {
    const getAssignedUserName = async () => {
      if (data.assignedTo) {
        const userName = await formatUserNameCached(data.assignedTo);
        setAssignedUserName(userName);
      } else {
        setAssignedUserName('Unassigned');
      }
    };

    getAssignedUserName();
  }, [data.assignedTo]);

  // Check if task is due and play sound
  useEffect(() => {
    console.log(`TaskNode useEffect running for task: ${data.title}, id: ${id}`);

    const checkIfTaskIsDue = () => {
      if (data.archived || !data.dueDate || data.status === 'Completed') {
        console.log(`Task is not due: archived=${data.archived}, status=${data.status}`);
        return false;
      }

      const dueDate = new Date(data.dueDate);
      const now = new Date();

      // Check if the due date has passed
      const isDue = dueDate <= now;
      console.log(`Task due date check: ${dueDate.toLocaleString()} <= ${now.toLocaleString()} = ${isDue}`);
      return isDue;
    };

    const isDueNow = checkIfTaskIsDue();
    console.log(`Setting isDue to ${isDueNow}, current isPlaying: ${isPlaying}`);
    setIsDue(isDueNow);

    // Play sound if task is due and not already playing
    if (isDueNow && !isPlaying) {
      console.log(`Task is due and not playing, starting sound for task: ${data.title}`);
      audioPlayer.playTaskDueSound();
      setIsPlaying(true);

      // Notify parent to zoom to this node
      if (data.onTaskDue) {
        console.log(`Notifying parent to zoom to task: ${id}`);
        data.onTaskDue(id);
      }
    } else if (!isDueNow && isPlaying) {
      // Stop sound if task is no longer due
      console.log(`Task is no longer due but sound is playing, stopping sound`);
      audioPlayer.stopTaskDueSound();
      setIsPlaying(false);
    }

    // Clean up when component unmounts
    return () => {
      if (isPlaying) {
        console.log(`Cleanup: stopping sound for task: ${data.title}`);
        audioPlayer.stopTaskDueSound();

        // We don't need to update state here as the component is unmounting
      }
    };
  }, [data.dueDate, data.status, data.archived, id, isPlaying, data.onTaskDue, data.title]);

  // Handle node click to stop sound
  const handleNodeClick = useCallback((e) => {
    // Make sure the event doesn't propagate to parent elements
    e.stopPropagation();

    console.log('Task node clicked, isPlaying:', isPlaying);

    if (isPlaying) {
      console.log('Stopping sound...');

      // Force stop the sound and update state
      try {
        // Stop the sound
        const stopped = audioPlayer.stopTaskDueSound();
        console.log('Stop result:', stopped);

        // Then update the state
        setIsPlaying(false);

        console.log('Sound stopped and state updated');
      } catch (error) {
        console.error('Error stopping sound:', error);
      }
    }
  }, [isPlaying]);

  // Handle node actions
  const handleEdit = () => {
    if (data.onEdit) data.onEdit(data.id);
  };

  const handleDelete = () => {
    if (data.onDelete) data.onDelete(data.id);
  };

  const handleArchive = () => {
    if (data.onArchive) data.onArchive(data.id);
  };

  const handleRestore = () => {
    if (data.onRestore) data.onRestore(data.id);
  };

  return (
    <div
      className={`node-card node-task ${data.archived ? 'node-archived' : ''} ${isDue ? 'node-due' : ''} ${isPlaying ? 'node-playing' : ''}`}
      onClick={handleNodeClick}
    >
      <NodeMenu
        onEdit={handleEdit}
        onDelete={handleDelete}
        onArchive={handleArchive}
        isArchived={data.archived}
        onRestore={handleRestore}
        nodeType="task"
      />
      <div className="node-header">
        <h6 className="node-title">Task</h6>
        <span className={`node-badge ${priorityColors[data.priority] || 'bg-secondary'}`}>
          {data.priority}
        </span>
        {isDue && (
          <span className="node-badge bg-danger ms-1">
            <i className="bi bi-alarm"></i> Due
          </span>
        )}
      </div>
      <div className="node-body">
        <h5 className="node-name">{data.title}</h5>
        <p className={`node-detail ${isDue ? 'text-danger fw-bold' : ''}`}>
          Due: {data.dueDate ? new Date(data.dueDate).toLocaleDateString() : 'Not set'}
        </p>
        <p className="node-detail">Assigned: {assignedUserName}</p>
        <p className="node-detail">Status: {data.status}</p>
        <p className="node-date">Created: {formatDate(data.createdAt)}</p>
      </div>
      {isPlaying && (
        <div className="node-alarm-indicator">
          <i className="bi bi-volume-up-fill"></i>
        </div>
      )}
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#555' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: '#555' }}
      />
    </div>
  );
};

export default memo(TaskNode);
