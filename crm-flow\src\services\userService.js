import { doc, getDoc, collection, query, where, getDocs, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from './firebase';

// Collection name
const USERS_COLLECTION = 'users';

/**
 * Get user by ID
 * @param {string} userId - The user ID
 * @returns {Promise<Object|null>} - The user data or null if not found
 */
export const getUserById = async (userId) => {
  try {
    if (!userId) return null;

    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      return {
        id: userDoc.id,
        ...userDoc.data()
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
};

/**
 * Get user by email
 * @param {string} email - The user's email
 * @returns {Promise<Object|null>} - The user data or null if not found
 */
export const getUserByEmail = async (email) => {
  try {
    if (!email) return null;

    const usersQuery = query(
      collection(db, USERS_COLLECTION),
      where('email', '==', email)
    );

    const snapshot = await getDocs(usersQuery);

    if (!snapshot.empty) {
      const userDoc = snapshot.docs[0];
      return {
        id: userDoc.id,
        ...userDoc.data()
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
};

/**
 * Format user ID to display name
 * @param {string} userId - The user ID
 * @returns {Promise<string>} - The formatted user name or 'Unknown User'
 */
export const formatUserName = async (userId) => {
  try {
    if (!userId) return 'Unassigned';

    const user = await getUserById(userId);

    if (user && user.name) {
      return user.name;
    } else if (user && user.email) {
      // If no name, use the part of email before @
      return user.email.split('@')[0];
    }

    return 'Unknown User';
  } catch (error) {
    console.error('Error formatting user name:', error);
    return 'Unknown User';
  }
};

/**
 * Cache for user names to avoid repeated database calls
 */
const userNameCache = new Map();

/**
 * Format user ID to display name with caching
 * @param {string} userId - The user ID
 * @returns {Promise<string>} - The formatted user name or 'Unknown User'
 */
export const formatUserNameCached = async (userId) => {
  try {
    if (!userId) return 'Unassigned';

    // Check cache first
    if (userNameCache.has(userId)) {
      return userNameCache.get(userId);
    }

    const formattedName = await formatUserName(userId);

    // Cache the result
    userNameCache.set(userId, formattedName);

    return formattedName;
  } catch (error) {
    console.error('Error formatting user name with cache:', error);
    return 'Unknown User';
  }
};

/**
 * Clear the user name cache
 */
export const clearUserNameCache = () => {
  userNameCache.clear();
};

/**
 * Update user profile
 * @param {string} userId - The user ID
 * @param {Object} userData - The user data to update
 * @returns {Promise<Object>} - The result of the operation
 */
export const updateUserProfile = async (userId, userData) => {
  try {
    console.log(`Updating user profile for ${userId} with data:`, userData);

    // Get the user to check if it exists
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    // Update user document
    await updateDoc(userRef, {
      ...userData,
      updatedAt: serverTimestamp()
    });

    // Clear the user name cache
    clearUserNameCache();

    return {
      success: true,
      message: 'Profile updated successfully'
    };
  } catch (error) {
    console.error('Error updating user profile:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
