import { useState } from 'react'
import { queryServer, deleteServer } from '../services/api'

const ServerCard = ({ server, onSelect, onServerUpdated }) => {
  const [querying, setQuerying] = useState(false)
  const [deleting, setDeleting] = useState(false)

  const handleQuery = async (e) => {
    e.stopPropagation() // Prevent card selection
    try {
      setQuerying(true)
      await queryServer(server.id)
      onServerUpdated() // Refresh the server list
    } catch (error) {
      console.error('Failed to query server:', error)
    } finally {
      setQuerying(false)
    }
  }

  const handleDelete = async (e) => {
    e.stopPropagation() // Prevent card selection
    if (!window.confirm(`Are you sure you want to delete "${server.name}"?`)) {
      return
    }
    
    try {
      setDeleting(true)
      await deleteServer(server.id)
      onServerUpdated() // Refresh the server list
    } catch (error) {
      console.error('Failed to delete server:', error)
      alert('Failed to delete server: ' + error.message)
    } finally {
      setDeleting(false)
    }
  }

  const formatLastChecked = (dateString) => {
    if (!dateString) return 'Never'
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return `${Math.floor(diffMins / 1440)}d ago`
  }

  const getStatusColor = () => {
    if (server.is_online) return '#4CAF50' // Green
    if (server.last_checked) return '#f44336' // Red
    return '#9E9E9E' // Gray for never checked
  }

  return (
    <div 
      className="server-card" 
      onClick={onSelect}
      style={{ borderLeft: `4px solid ${getStatusColor()}` }}
    >
      <div className="server-card-header">
        <h3 className="server-name">{server.name}</h3>
        <div className="server-actions">
          <button
            onClick={handleQuery}
            disabled={querying}
            className="btn btn-small btn-secondary"
            title="Query server"
          >
            {querying ? '⏳' : '🔄'}
          </button>
          <button
            onClick={handleDelete}
            disabled={deleting}
            className="btn btn-small btn-danger"
            title="Delete server"
          >
            {deleting ? '⏳' : '🗑️'}
          </button>
        </div>
      </div>

      <div className="server-info">
        <div className="server-address">
          <strong>{server.address}:{server.port}</strong>
        </div>
        
        <div className="server-status">
          <span className={`status-indicator ${server.is_online ? 'online' : 'offline'}`}>
            {server.is_online ? '🟢 Online' : '🔴 Offline'}
          </span>
          {server.ping && (
            <span className="ping">
              {server.ping}ms
            </span>
          )}
        </div>

        {server.is_online && (
          <div className="server-details">
            {server.player_count !== null && (
              <span className="players">
                👥 {server.player_count}/{server.max_players || '?'}
              </span>
            )}
            {server.map_name && (
              <span className="map">
                🗺️ {server.map_name}
              </span>
            )}
          </div>
        )}

        <div className="server-meta">
          <small>Last checked: {formatLastChecked(server.last_checked)}</small>
        </div>
      </div>
    </div>
  )
}

export default ServerCard
