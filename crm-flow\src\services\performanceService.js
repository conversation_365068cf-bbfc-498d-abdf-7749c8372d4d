/**
 * Performance Monitoring Service
 * 
 * This service provides utilities for monitoring and optimizing application performance.
 * It integrates with Firebase Performance Monitoring in production environments.
 */

import { environment } from '../config/environment';

// Performance metrics storage
const metrics = {
  // Store timing marks
  marks: new Map(),
  
  // Store measurements
  measures: new Map(),
  
  // Store counters
  counters: new Map()
};

/**
 * Start timing a specific operation
 * @param {string} name - The name of the operation to time
 */
export const startTiming = (name) => {
  if (!name) return;
  
  try {
    // Record the start time
    const startTime = performance.now();
    metrics.marks.set(`${name}_start`, startTime);
    
    // Log in development
    if (environment.isDevelopment) {
      console.log(`[Performance] Started timing: ${name}`);
    }
  } catch (error) {
    console.error('Error starting performance timing:', error);
  }
};

/**
 * End timing a specific operation and record the measurement
 * @param {string} name - The name of the operation to end timing
 * @returns {number|null} - The duration in milliseconds or null if timing failed
 */
export const endTiming = (name) => {
  if (!name) return null;
  
  try {
    // Get the start time
    const startTime = metrics.marks.get(`${name}_start`);
    if (!startTime) {
      console.warn(`No start time found for: ${name}`);
      return null;
    }
    
    // Calculate the duration
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Store the measurement
    metrics.measures.set(name, duration);
    
    // Log in development
    if (environment.isDevelopment) {
      console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
    }
    
    // Clean up the mark
    metrics.marks.delete(`${name}_start`);
    
    return duration;
  } catch (error) {
    console.error('Error ending performance timing:', error);
    return null;
  }
};

/**
 * Increment a counter
 * @param {string} name - The name of the counter
 * @param {number} value - The value to increment by (default: 1)
 */
export const incrementCounter = (name, value = 1) => {
  if (!name) return;
  
  try {
    // Get the current count
    const currentCount = metrics.counters.get(name) || 0;
    
    // Increment the counter
    metrics.counters.set(name, currentCount + value);
    
    // Log in development
    if (environment.isDevelopment && value !== 1) {
      console.log(`[Performance] Counter ${name}: ${currentCount} -> ${currentCount + value}`);
    }
  } catch (error) {
    console.error('Error incrementing counter:', error);
  }
};

/**
 * Get the value of a counter
 * @param {string} name - The name of the counter
 * @returns {number} - The counter value or 0 if not found
 */
export const getCounter = (name) => {
  if (!name) return 0;
  return metrics.counters.get(name) || 0;
};

/**
 * Reset a counter to zero
 * @param {string} name - The name of the counter
 */
export const resetCounter = (name) => {
  if (!name) return;
  metrics.counters.set(name, 0);
};

/**
 * Get all performance metrics
 * @returns {Object} - All performance metrics
 */
export const getAllMetrics = () => {
  return {
    measures: Object.fromEntries(metrics.measures),
    counters: Object.fromEntries(metrics.counters)
  };
};

/**
 * Clear all performance metrics
 */
export const clearAllMetrics = () => {
  metrics.marks.clear();
  metrics.measures.clear();
  metrics.counters.clear();
};

// Export the performance service
export const performanceService = {
  startTiming,
  endTiming,
  incrementCounter,
  getCounter,
  resetCounter,
  getAllMetrics,
  clearAllMetrics
};

export default performanceService;
