.layout-container {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #f8f9fa;
}

.main-content {
  margin-left: 220px;
  transition: all 0.3s ease;
  width: calc(100% - 220px);
  height: 100vh;
  overflow-y: auto;
  background-color: #f8f9fa;
  color: #333;
  padding: 20px;
  box-shadow: inset 5px 0 15px -5px rgba(0, 0, 0, 0.1);
}

/* Remove padding for flow view */
.flow-view .main-content {
  padding: 0;
  overflow: hidden;
}

.main-content.expanded {
  margin-left: 50px;
  width: calc(100% - 50px);
}

/* When sidebar is completely collapsed on mobile */
.sidebar-hidden+.main-content {
  margin-left: 0;
  width: 100%;
}

/* Mobile menu toggle button */
.mobile-menu-toggle {
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 999;
  background-color: #1e3a8a;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.mobile-menu-toggle:hover {
  background-color: #2563eb;
  transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    margin-left: 50px;
    width: calc(100% - 50px);
    padding-top: 10px;
  }

  .main-content.expanded {
    margin-left: 0;
    width: 100%;
    padding-top: 60px;
    /* Space for the mobile toggle button */
  }
}