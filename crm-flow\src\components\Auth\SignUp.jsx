import { useState } from 'react';
import { createUserWithEmailAndPassword, signInWithPopup } from 'firebase/auth';
import { auth, googleProvider, db } from '../../services/firebase';
import { doc, setDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { useNavigate, Link } from 'react-router-dom';
import { joinOrganizationByUuid } from '../../services/invitationService';
import 'bootstrap/dist/css/bootstrap.min.css';

const SignUp = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [organizationId, setOrganizationId] = useState('');
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSignUp = async (e) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setLoading(true);

    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Check if this email is already part of an organization
      // Query for users with this email
      const usersRef = collection(db, "users");
      const q = query(usersRef, where("email", "==", email));
      const querySnapshot = await getDocs(q);

      let existingOrgId = null;
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        if (userData.organizationId) {
          existingOrgId = userData.organizationId;
        }
      });

      // If organization ID is provided, use it instead of existing one
      const finalOrgId = organizationId || existingOrgId;

      // Create a user document in Firestore
      await setDoc(doc(db, "users", user.uid), {
        name,
        email,
        createdAt: new Date(),
        organizationId: finalOrgId,
        role: finalOrgId ? 'member' : null,
        photoURL: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`
      });

      // If organization ID is provided, join that organization
      if (organizationId) {
        const joinResult = await joinOrganizationByUuid(user.uid, organizationId);

        if (joinResult.success) {
          setSuccessMessage(`Account created and joined organization successfully!`);
        } else {
          setError(`Account created but failed to join organization: ${joinResult.error}`);
        }
      } else {
        setSuccessMessage('Account created successfully!');
      }

      // Navigate after a short delay to show the success message
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setError('');
    setSuccessMessage('');
    setLoading(true);

    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;

      // Check if this email is already part of an organization
      const usersRef = collection(db, "users");
      const q = query(usersRef, where("email", "==", user.email));
      const querySnapshot = await getDocs(q);

      let existingOrgId = null;
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        if (userData.organizationId) {
          existingOrgId = userData.organizationId;
        }
      });

      // If organization ID is provided, use it instead of existing one
      const finalOrgId = organizationId || existingOrgId;

      // Check if user document exists, if not create one
      await setDoc(doc(db, "users", user.uid), {
        name: user.displayName,
        email: user.email,
        createdAt: new Date(),
        organizationId: finalOrgId,
        role: finalOrgId ? 'member' : null,
        photoURL: user.photoURL || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.displayName)}&background=random`
      }, { merge: true });

      // If organization ID is provided, join that organization
      if (organizationId) {
        const joinResult = await joinOrganizationByUuid(user.uid, organizationId);

        if (joinResult.success) {
          setSuccessMessage(`Account created and joined organization successfully!`);
        } else {
          setError(`Account created but failed to join organization: ${joinResult.error}`);
        }
      } else {
        setSuccessMessage('Account created successfully!');
      }

      // Navigate after a short delay to show the success message
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container-fluid d-flex align-items-center justify-content-center"
      style={{
        height: '100vh',
        overflow: 'hidden',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      }}>
      <div className="row justify-content-center w-100">
        <div className="col-md-5">
          <div className="card shadow-lg" style={{ borderRadius: '16px', border: 'none', backgroundColor: '#2a2a2a', color: '#e0e0e0' }}>
            <div className="card-body p-5">
              <div className="text-center mb-4">
                <h2 style={{ fontWeight: '700', color: '#e0e0e0' }}>Create an Account</h2>
                <p className="text-muted">Join CRM Flow to manage your opportunities</p>
              </div>

              {error && <div className="alert alert-danger">{error}</div>}
              {successMessage && <div className="alert alert-success">{successMessage}</div>}

              <form onSubmit={handleSignUp}>
                <div className="mb-4">
                  <label htmlFor="name" className="form-label">Full Name</label>
                  <input
                    type="text"
                    className="form-control form-control-lg"
                    id="name"
                    placeholder="John Doe"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    style={{ borderRadius: '8px', padding: '12px 16px' }}
                    required
                  />
                </div>
                <div className="mb-4">
                  <label htmlFor="email" className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-control form-control-lg"
                    id="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    style={{ borderRadius: '8px', padding: '12px 16px' }}
                    required
                  />
                </div>
                <div className="mb-4">
                  <label htmlFor="password" className="form-label">Password</label>
                  <input
                    type="password"
                    className="form-control form-control-lg"
                    id="password"
                    placeholder="Create a strong password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    style={{ borderRadius: '8px', padding: '12px 16px' }}
                    required
                  />
                </div>
                <div className="mb-4">
                  <label htmlFor="organizationId" className="form-label">Organization ID (Optional)</label>
                  <input
                    type="text"
                    className="form-control form-control-lg"
                    id="organizationId"
                    placeholder="Enter organization ID to join"
                    value={organizationId}
                    onChange={(e) => setOrganizationId(e.target.value)}
                    style={{ borderRadius: '8px', padding: '12px 16px' }}
                  />
                  <small className="form-text text-muted">If you have an organization ID, enter it here to join that organization.</small>
                </div>
                <div className="d-grid gap-3 mt-5">
                  <button
                    type="submit"
                    className="btn btn-primary btn-lg"
                    style={{
                      borderRadius: '8px',
                      padding: '12px',
                      fontWeight: '600',
                      background: '#4F46E5',
                      border: 'none'
                    }}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Processing...
                      </>
                    ) : 'Create Account'}
                  </button>
                  <button
                    type="button"
                    className="btn btn-outline-secondary btn-lg"
                    onClick={handleGoogleSignUp}
                    style={{
                      borderRadius: '8px',
                      padding: '12px',
                      fontWeight: '600',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px'
                    }}
                    disabled={loading}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-google" viewBox="0 0 16 16">
                      <path d="M15.545 6.558a9.42 9.42 0 0 1 .139 1.626c0 2.434-.87 4.492-2.384 5.885h.002C11.978 15.292 10.158 16 8 16A8 8 0 1 1 8 0a7.689 7.689 0 0 1 5.352 2.082l-2.284 2.284A4.347 4.347 0 0 0 8 3.166c-2.087 0-3.86 1.408-4.492 3.304a4.792 4.792 0 0 0 0 3.063h.003c.635 1.893 2.405 3.301 4.492 3.301 1.078 0 2.004-.276 2.722-.764h-.003a3.702 3.702 0 0 0 1.599-2.431H8v-3.08h7.545z" />
                    </svg>
                    Sign Up with Google
                  </button>
                </div>
              </form>

              <div className="text-center mt-4">
                <p className="mb-0">Already have an account? <Link to="/signin" className="text-decoration-none fw-bold" style={{ color: '#4F46E5' }}>Sign In</Link></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
