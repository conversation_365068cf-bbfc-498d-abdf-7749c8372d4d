import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import 'bootstrap/dist/css/bootstrap.min.css';
import './SubscriptionStyles.css';

const SubscriptionPage = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();

  // Define subscription plans with features
  const plans = [
    {
      id: 'free',
      name: t('subscription.plans.free'),
      price: '$0',
      period: t('subscription.perMonth', 'per month'),
      features: {
        users: '1',
        organizations: '1',
        contacts: '50',
        storage: '500 MB',
        support: t('subscription.supportTypes.email', 'Email')
      },
      isPopular: false,
      buttonVariant: 'outline-primary'
    },
    {
      id: 'starter',
      name: t('subscription.plans.starter'),
      price: '$9.99',
      period: t('subscription.perMonth', 'per month'),
      features: {
        users: '3',
        organizations: '2',
        contacts: '250',
        storage: '2 GB',
        support: t('subscription.supportTypes.email', 'Email')
      },
      isPopular: true,
      buttonVariant: 'primary'
    },
    {
      id: 'professional',
      name: t('subscription.plans.professional'),
      price: '$24.99',
      period: t('subscription.perMonth', 'per month'),
      features: {
        users: '10',
        organizations: '5',
        contacts: '1,000',
        storage: '10 GB',
        support: t('subscription.supportTypes.priority', 'Priority'),
        analytics: t('common.yes'),
        customBranding: t('common.yes')
      },
      isPopular: false,
      buttonVariant: 'outline-primary'
    },
    {
      id: 'enterprise',
      name: t('subscription.plans.enterprise'),
      price: '$99.99',
      period: t('subscription.perMonth', 'per month'),
      features: {
        users: t('subscription.unlimited', 'Unlimited'),
        organizations: '15',
        contacts: t('subscription.unlimited', 'Unlimited'),
        storage: '100 GB',
        support: t('subscription.supportTypes.dedicated', 'Dedicated'),
        analytics: t('common.yes'),
        customBranding: t('common.yes'),
        apiAccess: t('common.yes')
      },
      isPopular: false,
      buttonVariant: 'outline-primary'
    }
  ];

  // Mock current subscription (replace with actual data from your backend)
  const currentSubscription = {
    plan: 'free',
    renewDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
  };

  return (
    <div className="subscription-page">
      <div className="container py-4">
        <h1 className="mb-4">{t('subscription.title')}</h1>

        {/* Current Subscription */}
        <div className="card mb-5">
          <div className="card-header">
            <h5 className="mb-0">{t('subscription.currentPlan')}</h5>
          </div>
          <div className="card-body">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <h4 className="mb-1">
                  {t(`subscription.plans.${currentSubscription.plan}`)}
                </h4>
                <p className="text-muted mb-0">
                  {t('subscription.renewsOn')}: {currentSubscription.renewDate}
                </p>
              </div>
              <button className="btn btn-primary">
                {t('subscription.manageBilling')}
              </button>
            </div>
          </div>
        </div>

        {/* Subscription Plans */}
        <h2 className="mb-4">{t('subscription.upgradePlan')}</h2>
        <div className="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-5">
          {plans.map((plan) => (
            <div className="col" key={plan.id}>
              <div className={`card h-100 subscription-card ${plan.isPopular ? 'subscription-card-popular' : ''}`}>
                {plan.isPopular && (
                  <div className="popular-badge">
                    {t('subscription.mostPopular', 'Most Popular')}
                  </div>
                )}
                <div className="card-header text-center">
                  <h5 className="mb-0">{plan.name}</h5>
                </div>
                <div className="card-body d-flex flex-column">
                  <div className="text-center mb-4">
                    <span className="display-5 fw-bold">{plan.price}</span>
                    <span className="text-muted ms-2">{plan.period}</span>
                  </div>
                  <ul className="list-unstyled mb-4">
                    <li className="mb-2">
                      <i className="bi bi-person-fill text-primary me-2"></i>
                      <strong>{t('subscription.features.users')}:</strong> {plan.features.users}
                    </li>
                    <li className="mb-2">
                      <i className="bi bi-building-fill text-primary me-2"></i>
                      <strong>{t('subscription.features.organizations')}:</strong> {plan.features.organizations}
                    </li>
                    <li className="mb-2">
                      <i className="bi bi-people-fill text-primary me-2"></i>
                      <strong>{t('subscription.features.contacts')}:</strong> {plan.features.contacts}
                    </li>
                    <li className="mb-2">
                      <i className="bi bi-hdd-fill text-primary me-2"></i>
                      <strong>{t('subscription.features.storage')}:</strong> {plan.features.storage}
                    </li>
                    <li className="mb-2">
                      <i className="bi bi-headset text-primary me-2"></i>
                      <strong>{t('subscription.features.support')}:</strong> {plan.features.support}
                    </li>
                    {plan.features.analytics && (
                      <li className="mb-2">
                        <i className="bi bi-graph-up text-primary me-2"></i>
                        <strong>{t('subscription.features.analytics')}:</strong> {plan.features.analytics}
                      </li>
                    )}
                    {plan.features.customBranding && (
                      <li className="mb-2">
                        <i className="bi bi-palette-fill text-primary me-2"></i>
                        <strong>{t('subscription.features.customBranding')}:</strong> {plan.features.customBranding}
                      </li>
                    )}
                    {plan.features.apiAccess && (
                      <li className="mb-2">
                        <i className="bi bi-code-slash text-primary me-2"></i>
                        <strong>{t('subscription.features.apiAccess')}:</strong> {plan.features.apiAccess}
                      </li>
                    )}
                  </ul>
                  <div className="mt-auto">
                    <button 
                      className={`btn btn-${plan.buttonVariant} w-100`}
                      disabled={plan.id === currentSubscription.plan}
                    >
                      {plan.id === currentSubscription.plan 
                        ? t('subscription.currentPlan') 
                        : t('subscription.selectPlan', 'Select Plan')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPage;
