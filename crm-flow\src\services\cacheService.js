/**
 * Cache Service
 * Provides a centralized caching mechanism for Firebase data
 */

// Cache configuration
const DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
const EXTENDED_CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

// Main cache storage
const cache = {
  collections: new Map(),
  documents: new Map(),
  queries: new Map(),
  timestamps: new Map(),
  expirations: new Map()
};

/**
 * Set cache item with expiration
 * @param {string} cacheType - Type of cache (collections, documents, queries)
 * @param {string} key - Cache key
 * @param {any} data - Data to cache
 * @param {number} duration - Cache duration in milliseconds
 */
const setCacheItem = (cacheType, key, data, duration = DEFAULT_CACHE_DURATION) => {
  if (!cache[cacheType]) {
    console.error(`Invalid cache type: ${cacheType}`);
    return;
  }
  
  // Store the data
  cache[cacheType].set(key, data);
  
  // Set timestamp and expiration
  const now = Date.now();
  cache.timestamps.set(key, now);
  cache.expirations.set(key, now + duration);
  
  // Log cache operation in development
  if (process.env.NODE_ENV !== 'production') {
    console.log(`[Cache] Set ${cacheType}:${key}, expires in ${duration/1000}s`);
  }
};

/**
 * Get cache item if valid
 * @param {string} cacheType - Type of cache (collections, documents, queries)
 * @param {string} key - Cache key
 * @returns {any|null} - Cached data or null if expired/not found
 */
const getCacheItem = (cacheType, key) => {
  if (!cache[cacheType]) {
    console.error(`Invalid cache type: ${cacheType}`);
    return null;
  }
  
  // Check if item exists in cache
  if (!cache[cacheType].has(key)) {
    return null;
  }
  
  // Check if item is expired
  const now = Date.now();
  const expiration = cache.expirations.get(key);
  
  if (now > expiration) {
    // Item is expired, remove it
    invalidateCacheItem(cacheType, key);
    return null;
  }
  
  // Log cache hit in development
  if (process.env.NODE_ENV !== 'production') {
    console.log(`[Cache] Hit ${cacheType}:${key}`);
  }
  
  return cache[cacheType].get(key);
};

/**
 * Invalidate a specific cache item
 * @param {string} cacheType - Type of cache (collections, documents, queries)
 * @param {string} key - Cache key
 */
const invalidateCacheItem = (cacheType, key) => {
  if (!cache[cacheType]) {
    console.error(`Invalid cache type: ${cacheType}`);
    return;
  }
  
  cache[cacheType].delete(key);
  cache.timestamps.delete(key);
  cache.expirations.delete(key);
  
  // Log cache invalidation in development
  if (process.env.NODE_ENV !== 'production') {
    console.log(`[Cache] Invalidated ${cacheType}:${key}`);
  }
};

/**
 * Invalidate all cache items of a specific type
 * @param {string} cacheType - Type of cache (collections, documents, queries)
 */
const invalidateCache = (cacheType) => {
  if (!cache[cacheType]) {
    console.error(`Invalid cache type: ${cacheType}`);
    return;
  }
  
  // Get all keys for this cache type
  const keys = Array.from(cache[cacheType].keys());
  
  // Invalidate each key
  keys.forEach(key => {
    invalidateCacheItem(cacheType, key);
  });
  
  // Log cache invalidation in development
  if (process.env.NODE_ENV !== 'production') {
    console.log(`[Cache] Invalidated all ${cacheType} (${keys.length} items)`);
  }
};

/**
 * Invalidate all cache items related to a specific collection
 * @param {string} collectionName - Collection name
 */
const invalidateCollection = (collectionName) => {
  // Invalidate collection cache
  const collectionKey = `collection:${collectionName}`;
  invalidateCacheItem('collections', collectionKey);
  
  // Invalidate all documents in this collection
  const documentKeys = Array.from(cache.documents.keys())
    .filter(key => key.startsWith(`document:${collectionName}/`));
  
  documentKeys.forEach(key => {
    invalidateCacheItem('documents', key);
  });
  
  // Invalidate all queries for this collection
  const queryKeys = Array.from(cache.queries.keys())
    .filter(key => key.startsWith(`query:${collectionName}`));
  
  queryKeys.forEach(key => {
    invalidateCacheItem('queries', key);
  });
  
  // Log cache invalidation in development
  if (process.env.NODE_ENV !== 'production') {
    console.log(`[Cache] Invalidated collection ${collectionName} and related items`);
  }
};

/**
 * Generate a cache key for a query
 * @param {string} collectionName - Collection name
 * @param {Array} filters - Array of filter objects with field, operator, value
 * @returns {string} - Cache key
 */
const generateQueryKey = (collectionName, filters = []) => {
  if (!filters || !Array.isArray(filters) || filters.length === 0) {
    return `query:${collectionName}:all`;
  }
  
  // Sort filters to ensure consistent keys regardless of filter order
  const sortedFilters = [...filters].sort((a, b) => {
    if (a.field < b.field) return -1;
    if (a.field > b.field) return 1;
    return 0;
  });
  
  // Create a string representation of the filters
  const filterString = sortedFilters.map(f => 
    `${f.field}${f.operator}${JSON.stringify(f.value)}`
  ).join('|');
  
  return `query:${collectionName}:${filterString}`;
};

// Public API
export const cacheService = {
  // Collection caching
  setCollection: (collectionName, data, duration = DEFAULT_CACHE_DURATION) => {
    const key = `collection:${collectionName}`;
    setCacheItem('collections', key, data, duration);
  },
  
  getCollection: (collectionName) => {
    const key = `collection:${collectionName}`;
    return getCacheItem('collections', key);
  },
  
  // Document caching
  setDocument: (collectionName, documentId, data, duration = DEFAULT_CACHE_DURATION) => {
    const key = `document:${collectionName}/${documentId}`;
    setCacheItem('documents', key, data, duration);
  },
  
  getDocument: (collectionName, documentId) => {
    const key = `document:${collectionName}/${documentId}`;
    return getCacheItem('documents', key);
  },
  
  // Query caching
  setQuery: (collectionName, filters, data, duration = DEFAULT_CACHE_DURATION) => {
    const key = generateQueryKey(collectionName, filters);
    setCacheItem('queries', key, data, duration);
  },
  
  getQuery: (collectionName, filters) => {
    const key = generateQueryKey(collectionName, filters);
    return getCacheItem('queries', key);
  },
  
  // Invalidation methods
  invalidateDocument: (collectionName, documentId) => {
    const key = `document:${collectionName}/${documentId}`;
    invalidateCacheItem('documents', key);
    
    // Also invalidate collection and queries since they might contain this document
    invalidateCollection(collectionName);
  },
  
  invalidateCollection,
  
  // Clear all caches
  clearAll: () => {
    invalidateCache('collections');
    invalidateCache('documents');
    invalidateCache('queries');
  }
};

export default cacheService;
