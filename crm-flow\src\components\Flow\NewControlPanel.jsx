import { memo } from 'react';

const NewControlPanel = ({
  onToggleArchived,
  onSaveToFirebase,
  onSyncWithFirebase,
  showArchived,
  hasUnsavedChanges,
  hasRemoteChanges
}) => {
  return (
    <div className="control-panel">
      <button
        className={`control-btn ${showArchived ? 'active' : ''}`}
        onClick={onToggleArchived}
        title={showArchived ? 'Hide Archived' : 'Show Archived'}
      >
        <i className="bi bi-archive"></i>
      </button>

      <button
        className={`control-btn ${hasUnsavedChanges ? 'warning' : ''}`}
        onClick={onSaveToFirebase}
        title="Save to Firebase"
        disabled={!hasUnsavedChanges}
      >
        <i className="bi bi-cloud-upload"></i>
        {hasUnsavedChanges && (
          <span className="notification-dot red"></span>
        )}
      </button>

      <button
        className="control-btn"
        onClick={onSyncWithFirebase}
        title="Sync with Firebase"
      >
        <i className="bi bi-arrow-repeat"></i>
        {hasRemoteChanges && (
          <span className="notification-dot green"></span>
        )}
      </button>
    </div>
  );
};

export default memo(NewControlPanel);
