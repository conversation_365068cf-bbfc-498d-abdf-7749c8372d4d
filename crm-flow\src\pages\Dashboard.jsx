import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { collection, getDocs, query, where, orderBy, limit, doc, updateDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { getUserData } from '../services/dataAccessService';
import { checkSampleDataExists, populateSampleData } from '../services/sampleData';
import Modal from '../components/UI/Modal';
import OpportunityForm from '../components/Forms/OpportunityForm';
import ContactForm from '../components/Forms/ContactForm';
import CompanyForm from '../components/Forms/CompanyForm';
import TaskForm from '../components/Forms/TaskForm';
import 'bootstrap/dist/css/bootstrap.min.css';
import './DashboardStyles.css';

const Dashboard = () => {
  const { currentUser, organization, userProfile } = useAuth();
  const { formatCurrency, availableCurrencies } = useCurrency();

  // Format currency with opportunity's specific currency if available
  const formatOpportunityCurrency = (value, currencyCode) => {
    if (currencyCode) {
      // Special case for JPY and other currencies that don't typically use decimal places
      const fractionDigits = ['JPY', 'KRW', 'VND', 'IDR'].includes(currencyCode) ? 0 : 2;

      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: fractionDigits,
        maximumFractionDigits: fractionDigits
      }).format(value);
    }

    // Fall back to user's default currency
    return formatCurrency(value);
  };
  const [stats, setStats] = useState({
    opportunities: 0,
    contacts: 0
  });
  const [recentOpportunities, setRecentOpportunities] = useState([]);
  const [upcomingTasks, setUpcomingTasks] = useState([]);
  const [loading, setLoading] = useState(true);

  const [dataMessage, setDataMessage] = useState('');
  const [showOpportunityModal, setShowOpportunityModal] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);
  const [showCompanyModal, setShowCompanyModal] = useState(false);
  const [showTaskModal, setShowTaskModal] = useState(false);

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'New':
        return 'bg-primary';
      case 'Qualified':
        return 'bg-info';
      case 'Proposal':
        return 'bg-warning';
      case 'Negotiation':
        return 'bg-danger';
      case 'Closed Won':
        return 'bg-success';
      case 'Closed Lost':
        return 'bg-secondary';
      case 'Archived':
        return 'bg-dark';
      default:
        return 'bg-primary';
    }
  };

  useEffect(() => {
    const initializeData = async () => {
      try {
        if (currentUser) {
          // Check if sample data exists
          const hasData = await checkSampleDataExists();

          // If no data exists, create sample data
          if (!hasData) {
            setDataMessage('Creating sample data...');
            const result = await populateSampleData(currentUser.uid);
            if (result.success) {
              setDataMessage('Sample data created successfully!');
              setTimeout(() => setDataMessage(''), 3000);
            } else {
              setDataMessage(`Error: ${result.message}`);
            }
          }

          // Fetch dashboard data
          fetchDashboardData();
        }
      } catch (error) {
        console.error('Error initializing data:', error);
        setDataMessage(`Error: ${error.message}`);
      }
    };

    initializeData();
  }, [currentUser]);

  const fetchDashboardData = async () => {
    try {
      const orgId = organization?.id || null;

      // Get data based on organization context
      const opportunities = await getUserData('opportunities', currentUser.uid, orgId);
      const contacts = await getUserData('contacts', currentUser.uid, orgId);
      const tasks = await getUserData('tasks', currentUser.uid, orgId);

      console.log('Dashboard - Fetched opportunities:', opportunities);
      console.log('Dashboard - Organization ID:', orgId);
      console.log('Dashboard - Current User ID:', currentUser.uid);

      setStats({
        opportunities: opportunities.length,
        contacts: contacts.length
      });

      // Get recent opportunities - include archived ones too so they can be restored
      const recentOpportunitiesData = opportunities
        .sort((a, b) => {
          // Sort by createdAt in descending order
          const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt);
          const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt);
          return dateB - dateA;
        })
        .slice(0, 5);

      setRecentOpportunities(recentOpportunitiesData);

      // Get upcoming tasks - include archived ones too so they can be restored
      const upcomingTasksData = tasks
        .filter(task => !task.completed)
        .sort((a, b) => {
          // Sort by dueDate in ascending order
          const dateA = a.dueDate?.toDate?.() || new Date(a.dueDate);
          const dateB = b.dueDate?.toDate?.() || new Date(b.dueDate);
          return dateA - dateB;
        })
        .slice(0, 5);

      setUpcomingTasks(upcomingTasksData);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '100%' }}>
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="dashboard-container">
        {/* Dashboard Header */}
        <div className="dashboard-header">
          <h1 className="dashboard-title">Dashboard</h1>
          <div className="dashboard-actions">
            <button className="dashboard-btn dashboard-btn-primary" onClick={() => setShowOpportunityModal(true)}>
              <i className="bi bi-plus-circle"></i>New Opportunity
            </button>
          </div>
        </div>

        {dataMessage && (
          <div className="dashboard-card" style={{ marginBottom: '1.5rem' }}>
            <div className="dashboard-card-body" style={{ padding: '1rem' }}>
              <div className="d-flex justify-content-between align-items-center">
                <div>{dataMessage}</div>
                <button
                  className="dashboard-btn dashboard-btn-outline dashboard-btn-sm"
                  onClick={() => setDataMessage('')}
                >
                  <i className="bi bi-x"></i>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Stats Section */}
        <div className="stats-section">
          <div className="stats-grid">
            {/* Opportunities Stats */}
            <div className="stats-card">
              <div className="stats-card-content">
                <div className="stats-card-icon" style={{ color: '#ff6b6b' }}>
                  <i className="bi bi-graph-up"></i>
                </div>
                <div className="stats-card-info">
                  <h2 className="stats-card-value">{stats.opportunities}</h2>
                  <p className="stats-card-label">Opportunities</p>
                </div>
              </div>
            </div>

            {/* Contacts Stats */}
            <div className="stats-card">
              <div className="stats-card-content">
                <div className="stats-card-icon" style={{ color: '#4ecdc4' }}>
                  <i className="bi bi-person"></i>
                </div>
                <div className="stats-card-info">
                  <h2 className="stats-card-value">{stats.contacts}</h2>
                  <p className="stats-card-label">Contacts</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Opportunities */}
        <div className="row">
          <div className="col-12">
            <div className="dashboard-card">
              <div className="dashboard-card-header">
                <h3 className="dashboard-card-title">Recent Opportunities</h3>
                <Link to="/opportunities" className="dashboard-btn dashboard-btn-outline dashboard-btn-sm">
                  View All
                </Link>
              </div>
              <div className="dashboard-card-body">
                {recentOpportunities.length > 0 ? (
                  <ul className="dashboard-list">
                    {recentOpportunities.map(opp => (
                      <li key={opp.id} className="dashboard-list-item">
                        <Link to={`/opportunities/${opp.id}`} style={{ textDecoration: 'none', color: 'inherit', display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                          <div className="dashboard-list-content">
                            <div className="dashboard-list-title">{opp.name}</div>
                            <div className="dashboard-list-subtitle">{opp.company}</div>
                          </div>
                          <div className="dashboard-list-meta">
                            <div className="dashboard-list-value">
                              {formatOpportunityCurrency(opp.value || 0, opp.currency)}
                              {opp.currency && opp.currency !== 'USD' && (
                                <small className="text-muted ms-1">({opp.currency})</small>
                              )}
                            </div>
                            <span className={`dashboard-badge ${getStatusBadgeColor(opp.status)}`}>{opp.status}</span>
                          </div>
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="empty-state">
                    <div className="empty-state-icon">
                      <i className="bi bi-graph-up"></i>
                    </div>
                    <p className="empty-state-text">No recent opportunities</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <Modal show={showOpportunityModal} onClose={() => setShowOpportunityModal(false)} size="lg">
        <OpportunityForm
          onClose={() => setShowOpportunityModal(false)}
          onSuccess={() => {
            // Refresh data after adding a new opportunity
            fetchDashboardData();
          }}
        />
      </Modal>

      <Modal show={showContactModal} onClose={() => setShowContactModal(false)} size="lg">
        <ContactForm
          onClose={() => setShowContactModal(false)}
          onSuccess={() => {
            // Refresh data after adding a new contact
            fetchDashboardData();
          }}
        />
      </Modal>

      <Modal show={showCompanyModal} onClose={() => setShowCompanyModal(false)} size="lg">
        <CompanyForm
          onClose={() => setShowCompanyModal(false)}
          onSuccess={() => {
            // Refresh data after adding a new company
            fetchDashboardData();
          }}
        />
      </Modal>


    </>
  );
};

export default Dashboard;
