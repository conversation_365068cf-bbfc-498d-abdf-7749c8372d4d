import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  query,
  where,
  updateDoc,
  deleteDoc,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';

// Collection names
const ORGANIZATIONS_COLLECTION = 'organizations';
const USERS_COLLECTION = 'users';

/**
 * Create a new organization
 * @param {string} ownerId - The user ID of the organization owner
 * @param {Object} organizationData - The organization data
 * @returns {Promise<Object>} - The result of the operation
 */
export const createOrganization = async (ownerId, organizationData) => {
  try {
    // Create organization document
    const orgRef = doc(collection(db, ORGANIZATIONS_COLLECTION));

    const orgData = {
      ...organizationData,
      ownerId,
      members: [ownerId], // Owner is automatically a member
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      subscription: {
        plan: 'free', // Default to free plan
        status: 'active',
        startDate: serverTimestamp(),
        endDate: null
      }
    };

    await setDoc(orgRef, orgData);

    // Update user document to set current organization
    const userRef = doc(db, USERS_COLLECTION, ownerId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      await updateDoc(userRef, {
        currentOrganizationId: orgRef.id, // Set as current organization
        role: 'owner', // Set user as organization owner
        updatedAt: serverTimestamp()
      });
    }

    return {
      success: true,
      organizationId: orgRef.id,
      message: 'Organization created successfully'
    };
  } catch (error) {
    console.error('Error creating organization:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get organization by ID
 * @param {string} organizationId - The organization ID
 * @returns {Promise<Object|null>} - The organization data or null if not found
 */
export const getOrganization = async (organizationId) => {
  try {
    console.log('Getting organization with ID:', organizationId);

    if (!organizationId) {
      console.error('Invalid organization ID provided:', organizationId);
      return null;
    }

    const orgRef = doc(db, ORGANIZATIONS_COLLECTION, organizationId);
    const orgDoc = await getDoc(orgRef);

    if (orgDoc.exists()) {
      const orgData = {
        id: orgDoc.id,
        ...orgDoc.data()
      };
      console.log('Organization found:', orgData.name);
      return orgData;
    }

    console.warn('Organization not found with ID:', organizationId);
    return null;
  } catch (error) {
    console.error('Error getting organization:', error);
    return null;
  }
};

/**
 * Get organization by owner ID
 * @param {string} ownerId - The owner's user ID
 * @returns {Promise<Object|null>} - The organization data or null if not found
 */
export const getOrganizationByOwner = async (ownerId) => {
  try {
    const orgsQuery = query(
      collection(db, ORGANIZATIONS_COLLECTION),
      where('ownerId', '==', ownerId)
    );

    const snapshot = await getDocs(orgsQuery);

    if (!snapshot.empty) {
      const orgDoc = snapshot.docs[0];
      return {
        id: orgDoc.id,
        ...orgDoc.data()
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting organization by owner:', error);
    return null;
  }
};

/**
 * Get organization by member ID
 * @param {string} userId - The member's user ID
 * @returns {Promise<Object|null>} - The organization data or null if not found
 */
export const getOrganizationByMember = async (userId) => {
  try {
    const orgsQuery = query(
      collection(db, ORGANIZATIONS_COLLECTION),
      where('members', 'array-contains', userId)
    );

    const snapshot = await getDocs(orgsQuery);

    if (!snapshot.empty) {
      const orgDoc = snapshot.docs[0];
      return {
        id: orgDoc.id,
        ...orgDoc.data()
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting organization by member:', error);
    return null;
  }
};

/**
 * Add a member to an organization
 * @param {string} organizationId - The organization ID
 * @param {string} userId - The user ID to add
 * @param {string} role - The role to assign to the user (default: 'member')
 * @returns {Promise<Object>} - The result of the operation
 */
export const addOrganizationMember = async (organizationId, userId, role = 'member') => {
  try {
    console.log(`Adding member ${userId} to organization ${organizationId} with role ${role}`);

    // Get the organization to check if it exists
    const orgRef = doc(db, ORGANIZATIONS_COLLECTION, organizationId);
    const orgDoc = await getDoc(orgRef);

    if (!orgDoc.exists()) {
      throw new Error('Organization not found');
    }

    // Get the current members array
    const orgData = orgDoc.data();
    const currentMembers = orgData.members || [];

    // Check if user is already a member
    if (currentMembers.includes(userId)) {
      console.log(`User ${userId} is already a member of organization ${organizationId}`);
      return {
        success: true,
        message: 'User is already a member of this organization'
      };
    }

    // Update organization document to add member
    await updateDoc(orgRef, {
      members: arrayUnion(userId),
      updatedAt: serverTimestamp()
    });

    // Check if user document exists
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      // Update existing user document
      await updateDoc(userRef, {
        currentOrganizationId: organizationId, // Set as current organization
        role,
        updatedAt: serverTimestamp()
      });
    } else {
      // User document doesn't exist, this shouldn't happen in normal flow
      // but we'll handle it just in case
      console.warn(`User document ${userId} not found when adding to organization`);
    }

    console.log(`Successfully added member ${userId} to organization ${organizationId}`);

    return {
      success: true,
      message: 'Member added successfully'
    };
  } catch (error) {
    console.error('Error adding organization member:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Remove a member from an organization
 * @param {string} organizationId - The organization ID
 * @param {string} userId - The user ID to remove
 * @returns {Promise<Object>} - The result of the operation
 */
export const removeOrganizationMember = async (organizationId, userId) => {
  try {
    // Update organization document to remove member
    const orgRef = doc(db, ORGANIZATIONS_COLLECTION, organizationId);
    await updateDoc(orgRef, {
      members: arrayRemove(userId),
      updatedAt: serverTimestamp()
    });

    // Update user document to remove current organization reference if it matches
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();

      // Remove the organization from the user's organizations array
      if (userData.organizations && userData.organizations.includes(organizationId)) {
        const updatedOrgs = userData.organizations.filter(id => id !== organizationId);

        // Update the user document with the filtered organizations array
        await updateDoc(userRef, {
          organizations: updatedOrgs,
          updatedAt: serverTimestamp()
        });
      }

      // Only clear the current organization if it matches the one they're being removed from
      if (userData.currentOrganizationId === organizationId) {
        // If user has other organizations, set the first one as current
        if (userData.organizations && userData.organizations.length > 1) {
          const remainingOrgs = userData.organizations.filter(id => id !== organizationId);
          await updateDoc(userRef, {
            currentOrganizationId: remainingOrgs[0],
            updatedAt: serverTimestamp()
          });
        } else {
          // No other organizations, clear current organization
          await updateDoc(userRef, {
            currentOrganizationId: null,
            role: null,
            updatedAt: serverTimestamp()
          });
        }
      }
    }

    return {
      success: true,
      message: 'Member removed successfully'
    };
  } catch (error) {
    console.error('Error removing organization member:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Update organization details
 * @param {string} organizationId - The organization ID
 * @param {Object} organizationData - The organization data to update
 * @returns {Promise<Object>} - The result of the operation
 */
export const updateOrganization = async (organizationId, organizationData) => {
  try {
    console.log(`Updating organization ${organizationId} with data:`, organizationData);

    // Get the organization to check if it exists
    const orgRef = doc(db, ORGANIZATIONS_COLLECTION, organizationId);
    const orgDoc = await getDoc(orgRef);

    if (!orgDoc.exists()) {
      throw new Error('Organization not found');
    }

    // Update organization document
    await updateDoc(orgRef, {
      ...organizationData,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'Organization updated successfully'
    };
  } catch (error) {
    console.error('Error updating organization:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Delete an organization and all its associated data
 * @param {string} organizationId - The organization ID
 * @param {string} ownerId - The owner's user ID
 * @returns {Promise<Object>} - The result of the operation
 */
export const deleteOrganization = async (organizationId, ownerId) => {
  try {
    console.log(`Deleting organization ${organizationId} owned by ${ownerId}`);

    // Get the organization to check if it exists and verify ownership
    const orgRef = doc(db, ORGANIZATIONS_COLLECTION, organizationId);
    const orgDoc = await getDoc(orgRef);

    if (!orgDoc.exists()) {
      throw new Error('Organization not found');
    }

    const orgData = orgDoc.data();
    if (orgData.ownerId !== ownerId) {
      throw new Error('Only the organization owner can delete the organization');
    }

    // Get all members to update their user documents
    const members = orgData.members || [];

    // 1. Delete all opportunities associated with this organization
    const opportunitiesQuery = query(
      collection(db, 'opportunities'),
      where('organizationId', '==', organizationId)
    );
    const opportunitiesSnapshot = await getDocs(opportunitiesQuery);

    // Use a batch to delete all opportunities
    let batch = writeBatch(db);
    let operationCount = 0;
    const MAX_BATCH_SIZE = 500; // Firestore limit

    opportunitiesSnapshot.forEach((doc) => {
      batch.delete(doc.ref);
      operationCount++;

      // If we reach the batch limit, commit and create a new batch
      if (operationCount >= MAX_BATCH_SIZE) {
        batch.commit();
        batch = writeBatch(db);
        operationCount = 0;
      }
    });

    // Commit any remaining operations
    if (operationCount > 0) {
      await batch.commit();
    }

    console.log(`Deleted ${opportunitiesSnapshot.size} opportunities`);

    // 2. Update all member user documents to remove this organization
    batch = writeBatch(db);
    operationCount = 0;

    for (const memberId of members) {
      const userRef = doc(db, USERS_COLLECTION, memberId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();

        // Remove the organization from the user's organizations array
        if (userData.organizations && userData.organizations.includes(organizationId)) {
          const updatedOrgs = userData.organizations.filter(id => id !== organizationId);

          // If this was the user's current organization, update that too
          if (userData.currentOrganizationId === organizationId) {
            // If user has other organizations, set the first one as current
            if (updatedOrgs.length > 0) {
              batch.update(userRef, {
                organizations: updatedOrgs,
                currentOrganizationId: updatedOrgs[0],
                updatedAt: serverTimestamp()
              });
            } else {
              // No other organizations, clear current organization
              batch.update(userRef, {
                organizations: updatedOrgs,
                currentOrganizationId: null,
                role: null,
                updatedAt: serverTimestamp()
              });
            }
          } else {
            // Just update the organizations array
            batch.update(userRef, {
              organizations: updatedOrgs,
              updatedAt: serverTimestamp()
            });
          }

          operationCount++;

          // If we reach the batch limit, commit and create a new batch
          if (operationCount >= MAX_BATCH_SIZE) {
            await batch.commit();
            batch = writeBatch(db);
            operationCount = 0;
          }
        }
      }
    }

    // Commit any remaining member updates
    if (operationCount > 0) {
      await batch.commit();
    }

    console.log(`Updated ${members.length} member documents`);

    // 3. Finally, delete the organization document
    await deleteDoc(orgRef);

    return {
      success: true,
      message: 'Organization and all associated data deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting organization:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Update organization subscription
 * @param {string} organizationId - The organization ID
 * @param {Object} subscriptionData - The subscription data to update
 * @returns {Promise<Object>} - The result of the operation
 */
export const updateOrganizationSubscription = async (organizationId, subscriptionData) => {
  try {
    const orgRef = doc(db, ORGANIZATIONS_COLLECTION, organizationId);

    await updateDoc(orgRef, {
      'subscription': subscriptionData,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'Subscription updated successfully'
    };
  } catch (error) {
    console.error('Error updating organization subscription:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get all members of an organization with their user details
 * @param {string} organizationId - The organization ID
 * @returns {Promise<Array>} - Array of user objects
 */
export const getOrganizationMembers = async (organizationId) => {
  try {
    console.log('Getting members for organization:', organizationId);

    if (!organizationId) {
      console.error('Invalid organization ID provided');
      return [];
    }

    // Get the organization to get the member IDs
    const org = await getOrganization(organizationId);

    if (!org) {
      console.error('Organization not found:', organizationId);
      return [];
    }

    if (!org.members || !Array.isArray(org.members) || org.members.length === 0) {
      console.log('No members found in organization:', organizationId);
      return [];
    }

    console.log('Organization members IDs:', org.members);

    // Get user documents for all members
    const userPromises = org.members.map(userId => {
      if (!userId) {
        console.warn('Invalid user ID in members array');
        return Promise.resolve(null);
      }
      const userRef = doc(db, USERS_COLLECTION, userId);
      return getDoc(userRef);
    });

    const userDocs = await Promise.all(userPromises);

    // Map user documents to user objects
    const members = userDocs
      .filter(userDoc => userDoc && userDoc.exists())
      .map(userDoc => ({
        id: userDoc.id,
        ...userDoc.data()
      }));

    console.log('Fetched organization members:', members);
    return members;
  } catch (error) {
    console.error('Error getting organization members:', error);
    return [];
  }
};

/**
 * Check if a user is the owner of an organization
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID
 * @returns {Promise<boolean>} - True if the user is the owner
 */
export const isOrganizationOwner = async (userId, organizationId) => {
  try {
    const org = await getOrganization(organizationId);
    return org && org.ownerId === userId;
  } catch (error) {
    console.error('Error checking organization ownership:', error);
    return false;
  }
};

/**
 * Check if a user is a member of an organization
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID
 * @returns {Promise<boolean>} - True if the user is a member
 */
export const isOrganizationMember = async (userId, organizationId) => {
  try {
    const org = await getOrganization(organizationId);
    return org && org.members && org.members.includes(userId);
  } catch (error) {
    console.error('Error checking organization membership:', error);
    return false;
  }
};

/**
 * Get all organizations a user is a member of (both owned and joined)
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - Array of organization objects with isOwner flag
 */
export const getAllUserOrganizations = async (userId) => {
  try {
    console.log('Getting all organizations for user:', userId);

    // Query for organizations where the user is a member
    const orgsQuery = query(
      collection(db, ORGANIZATIONS_COLLECTION),
      where('members', 'array-contains', userId)
    );

    const snapshot = await getDocs(orgsQuery);

    if (snapshot.empty) {
      console.log('No organizations found for user:', userId);
      return [];
    }

    // Map the documents to an array of organization objects
    // and add an isOwner flag to each organization
    const organizations = snapshot.docs.map(doc => {
      const orgData = doc.data();
      return {
        id: doc.id,
        ...orgData,
        isOwner: orgData.ownerId === userId
      };
    });

    console.log('Found organizations for user:', organizations.length);
    return organizations;
  } catch (error) {
    console.error('Error getting all user organizations:', error);
    return [];
  }
};
