import { doc, getDoc, onSnapshot } from 'firebase/firestore';
import { db } from './firebase';
import { getSessionNodePositions } from './sessionNodeService';

// Collection name
const NODE_POSITIONS_COLLECTION = 'nodePositions';

// Track listeners to clean them up
let unsubscribeListener = null;
let lastSyncTimestamp = null;
let remoteChangesAvailable = false;
let remotePositions = null;
let lastUpdatedByUser = null;

// Custom event for node position updates
export const NODE_POSITION_UPDATED_EVENT = 'node-position-updated';

/**
 * Start listening for remote changes to node positions
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID
 * @param {Function} onRemoteChanges - Callback when remote changes are detected
 * @returns {Function} - Unsubscribe function
 */
export const startListeningForRemoteChanges = (userId, organizationId, onRemoteChanges) => {
  // Clean up any existing listener
  if (unsubscribeListener) {
    unsubscribeListener();
    unsubscribeListener = null;
  }

  // Reset state
  remoteChangesAvailable = false;
  remotePositions = null;

  // Create a document reference for this organization's node positions
  const docRef = doc(db, NODE_POSITIONS_COLLECTION, organizationId);

  // Start listening for changes
  unsubscribeListener = onSnapshot(docRef, (docSnapshot) => {
    if (docSnapshot.exists()) {
      const data = docSnapshot.data();

      // If we have a last sync timestamp and the document was updated after that
      // and it wasn't updated by the current user
      if (lastSyncTimestamp && data.updatedAt &&
        new Date(data.updatedAt.toDate()) > new Date(lastSyncTimestamp) &&
        data.lastUpdatedBy !== userId) {

        // Store who updated the positions
        lastUpdatedByUser = data.lastUpdatedBy;

        // Store the remote positions
        remotePositions = data.positions;

        // Set flag that remote changes are available
        remoteChangesAvailable = true;

        // Call the callback
        if (onRemoteChanges) {
          onRemoteChanges(true);
        }

        // Dispatch custom event for node position updates
        const event = new CustomEvent(NODE_POSITION_UPDATED_EVENT, {
          detail: {
            updatedBy: data.lastUpdatedBy,
            timestamp: data.updatedAt.toDate()
          }
        });
        window.dispatchEvent(event);
      }
    }
  }, (error) => {
    console.error('Error listening for node position changes:', error);
  });

  return unsubscribeListener;
};

/**
 * Stop listening for remote changes
 */
export const stopListeningForRemoteChanges = () => {
  if (unsubscribeListener) {
    unsubscribeListener();
    unsubscribeListener = null;
  }
};

/**
 * Check if there are remote changes available to sync
 * @returns {boolean} - Whether there are remote changes
 */
export const hasRemoteChanges = () => {
  return remoteChangesAvailable;
};

/**
 * Get the remote positions
 * @returns {Object|null} - The remote positions or null if none
 */
export const getRemotePositions = () => {
  return remotePositions;
};

/**
 * Get the user ID who last updated the positions
 * @returns {string|null} - The user ID or null if none
 */
export const getLastUpdatedByUser = () => {
  return lastUpdatedByUser;
};

/**
 * Sync local changes with remote changes
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID
 * @returns {Promise<Object>} - The merged positions
 */
export const syncWithRemote = async (userId, organizationId) => {
  try {
    // Get the current local positions
    const localPositions = getSessionNodePositions();

    // If we don't have remote positions, fetch them
    if (!remotePositions) {
      const docRef = doc(db, NODE_POSITIONS_COLLECTION, organizationId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        remotePositions = docSnap.data().positions;
      } else {
        // No remote positions, just use local
        return { success: true, positions: localPositions };
      }
    }

    // Merge local and remote positions
    // For simplicity, we'll use remote positions as they're more likely to be the latest
    // In a real app, you might want a more sophisticated merge strategy
    const mergedPositions = { ...remotePositions };

    // Update the last sync timestamp
    lastSyncTimestamp = new Date();

    // Reset the remote changes flag
    remoteChangesAvailable = false;

    return {
      success: true,
      positions: mergedPositions
    };
  } catch (error) {
    console.error('Error syncing with remote:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Update the last sync timestamp
 */
export const updateLastSyncTimestamp = () => {
  lastSyncTimestamp = new Date();
  remoteChangesAvailable = false;
};
