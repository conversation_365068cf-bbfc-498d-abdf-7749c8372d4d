import { useState, useEffect } from 'react';
import {
  Table,
  Container,
  Text,
  Group,
  ActionIcon,
  Badge,
  Paper,
  Title,
  TextInput,
  LoadingOverlay,
  Select,
  Box,
  Tooltip,
  Progress,
  Slider,
  Flex,
  Switch
} from '@mantine/core';
import {
  IconRefresh,
  IconTrash,
  IconInfoCircle,
  IconSearch,
  IconStar,
  IconStarFilled,
  IconWifi,
  IconWifiOff
} from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';

function ServerList({
  servers,
  removeServer,
  refreshServer,
  addFavorite,
  removeFavorite,
  isFavorite,
  loading,
  refreshProgress,
  totalServersToRefresh
}) {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('hostname');
  const [sortDirection, setSortDirection] = useState('asc');
  const [favoriteStatus, setFavoriteStatus] = useState({});
  const [localLoading, setLocalLoading] = useState({});
  const [maxPing, setMaxPing] = useState(1000); // Default to show all servers
  const [pingFilterEnabled, setPingFilterEnabled] = useState(false); // Whether ping filter is enabled

  // Load favorite status for all servers
  useEffect(() => {
    const loadFavoriteStatus = async () => {
      const status = {};
      for (const server of servers) {
        try {
          status[`${server.address}:${server.port}`] = await isFavorite(server.address, server.port);
        } catch (error) {
          console.error(`Failed to check favorite status for ${server.address}:${server.port}:`, error);
          status[`${server.address}:${server.port}`] = false;
        }
      }
      setFavoriteStatus(status);
    };

    loadFavoriteStatus();
  }, [servers, isFavorite]);

  // Filter servers based on search query and ping
  const filteredServers = servers.filter(server => {
    // Filter by search query
    const hostname = server.hostname || '';
    const map = server.map || '';
    const address = server.address || '';

    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = (
      hostname.toLowerCase().includes(searchLower) ||
      map.toLowerCase().includes(searchLower) ||
      address.toLowerCase().includes(searchLower)
    );

    // Filter by ping if enabled
    let matchesPing = true;
    if (pingFilterEnabled) {
      const ping = server.ping || 999; // Default to high ping if not available
      matchesPing = ping <= maxPing;
    }

    // Server must match both search and ping criteria
    return matchesSearch && matchesPing;
  });

  // Sort servers
  const sortedServers = [...filteredServers].sort((a, b) => {
    let valueA, valueB;

    switch (sortBy) {
      case 'hostname':
        valueA = a.hostname || '';
        valueB = b.hostname || '';
        break;
      case 'map':
        valueA = a.map || '';
        valueB = b.map || '';
        break;
      case 'players':
        valueA = a.players ? a.players.length : 0;
        valueB = b.players ? b.players.length : 0;
        break;
      case 'ping':
        valueA = a.ping || 999;
        valueB = b.ping || 999;
        break;
      default:
        valueA = a.hostname || '';
        valueB = b.hostname || '';
    }

    if (sortDirection === 'asc') {
      return valueA > valueB ? 1 : -1;
    } else {
      return valueA < valueB ? 1 : -1;
    }
  });

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field) => {
    if (sortBy !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <Container size="100%" px="xs" py="xs">
      <Box pos="relative">
        <LoadingOverlay visible={loading} overlayBlur={2} />

        <Title order={3} mb="xs">Server List</Title>

        {loading && totalServersToRefresh > 0 && (
          <Box mb="xs">
            <Group justify="space-between" mb={2}>
              <Text size="xs">Refreshing: {refreshProgress}%</Text>
              <Text size="xs">{Math.floor(refreshProgress * totalServersToRefresh / 100)}/{totalServersToRefresh}</Text>
            </Group>
            <Progress value={refreshProgress} size="xs" radius="xs" striped animated color="blue" />
          </Box>
        )}

        <Group mb="xs" gap="xs">
          <TextInput
            placeholder="Search servers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.currentTarget.value)}
            leftSection={<IconSearch size={14} />}
            style={{ flexGrow: 1 }}
            size="xs"
          />
          <Select
            value={sortBy}
            onChange={setSortBy}
            data={[
              { value: 'hostname', label: 'Name' },
              { value: 'map', label: 'Map' },
              { value: 'players', label: 'Players' },
              { value: 'ping', label: 'Ping' }
            ]}
            placeholder="Sort by"
            style={{ width: 100 }}
            size="xs"
          />
        </Group>

        {/* Ping Filter */}
        <Paper p="xs" withBorder mb="xs">
          <Flex align="center" justify="space-between" mb={5}>
            <Group gap={8}>
              <Switch
                checked={pingFilterEnabled}
                onChange={(event) => setPingFilterEnabled(event.currentTarget.checked)}
                size="xs"
                color="blue"
                label={
                  <Group gap={5}>
                    {pingFilterEnabled ? <IconWifi size={14} /> : <IconWifiOff size={14} />}
                    <Text size="xs">Ping Filter</Text>
                  </Group>
                }
              />
            </Group>
            <Text size="xs" fw={500} color={pingFilterEnabled ? "blue" : "dimmed"}>
              {pingFilterEnabled ? `Max: ${maxPing} ms` : "Disabled"}
            </Text>
          </Flex>

          {pingFilterEnabled && (
            <>
              <Slider
                value={maxPing}
                onChange={setMaxPing}
                min={50}
                max={500}
                step={10}
                size="xs"
                color="blue"
                marks={[
                  { value: 50, label: '50ms' },
                  { value: 150, label: '150ms' },
                  { value: 300, label: '300ms' },
                  { value: 500, label: '500ms' },
                ]}
                styles={{
                  markLabel: { fontSize: '10px' }
                }}
              />

              {/* Show how many servers are filtered out */}
              {servers.length > 0 && (
                <Text size="xs" ta="right" mt={5} color="dimmed">
                  Showing {filteredServers.length} of {servers.length} servers
                  {servers.length - filteredServers.length > 0 && (
                    <Text span color="red" size="xs"> ({servers.length - filteredServers.length} filtered out)</Text>
                  )}
                </Text>
              )}
            </>
          )}
        </Paper>

        {sortedServers.length === 0 ? (
          <Paper p="xs" withBorder>
            <Text ta="center" size="xs">
              {servers.length === 0
                ? "No servers added yet. Click 'Add Server' to get started."
                : "No servers match your search criteria."}
            </Text>
          </Paper>
        ) : (
          <Table striped highlightOnHover style={{ backgroundColor: '#1a1b1e' }}>
            <Table.Thead>
              <Table.Tr style={{ height: '32px' }}>
                <Table.Th onClick={() => handleSort('hostname')} style={{ cursor: 'pointer', padding: '6px 8px' }}>
                  Server Name {getSortIcon('hostname')}
                </Table.Th>
                <Table.Th onClick={() => handleSort('map')} style={{ cursor: 'pointer', padding: '6px 8px' }}>
                  Map {getSortIcon('map')}
                </Table.Th>
                <Table.Th onClick={() => handleSort('players')} style={{ cursor: 'pointer', textAlign: 'center', padding: '6px 8px', width: '80px' }}>
                  Players {getSortIcon('players')}
                </Table.Th>
                <Table.Th onClick={() => handleSort('ping')} style={{ cursor: 'pointer', textAlign: 'center', padding: '6px 8px', width: '70px' }}>
                  Ping {getSortIcon('ping')}
                </Table.Th>
                <Table.Th style={{ textAlign: 'right', padding: '6px 8px', width: '120px' }}>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {sortedServers.map((server) => (
                <Table.Tr
                  key={`${server.address}:${server.port}`}
                  className="server-row"
                  style={{
                    height: '32px',
                    backgroundColor: '#1a1b1e',
                    cursor: 'pointer'
                  }}
                  onClick={() => navigate(`/server/${server.address}/${server.port}`)}
                >
                  <Table.Td style={{ padding: '4px 8px' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0' }}>
                      <Text size="xs" fw={500} style={{ lineHeight: 1.2 }}>{server.hostname || server.address}</Text>
                      <Text size="xs" c="dimmed" style={{ lineHeight: 1.2, fontSize: '10px' }}>{server.address}:{server.port}</Text>
                    </div>
                  </Table.Td>
                  <Table.Td style={{ padding: '4px 8px' }}>
                    <Text size="xs">{server.map || 'Unknown'}</Text>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center', padding: '4px 8px' }}>
                    <Badge size="xs" color="blue" variant="light">
                      {server.players ? server.players.length : 0}/{server.maxclients || '?'}
                    </Badge>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center', padding: '4px 8px' }}>
                    <Badge
                      size="xs"
                      color="blue"
                      variant={server.ping < 100 ? 'filled' : server.ping < 200 ? 'light' : 'outline'}
                    >
                      {server.ping || '?'}ms
                    </Badge>
                  </Table.Td>
                  <Table.Td style={{ padding: '4px 8px' }} onClick={(e) => e.stopPropagation()}>
                    <Group gap={4} justify="flex-end">
                      <ActionIcon
                        size="xs"
                        variant="subtle"
                        color="blue"
                        onClick={(e) => {
                          e.stopPropagation();
                          refreshServer(server.address, server.port);
                        }}
                      >
                        <IconRefresh size={14} />
                      </ActionIcon>
                      <ActionIcon
                        size="xs"
                        variant="subtle"
                        color="blue"
                        loading={localLoading[`${server.address}:${server.port}`]}
                        onClick={async (e) => {
                          e.stopPropagation();
                          const serverKey = `${server.address}:${server.port}`;
                          setLocalLoading(prev => ({ ...prev, [serverKey]: true }));

                          try {
                            if (favoriteStatus[serverKey]) {
                              await removeFavorite(server.address, server.port);
                              setFavoriteStatus(prev => ({ ...prev, [serverKey]: false }));
                            } else {
                              await addFavorite(server.address, server.port, server.hostname);
                              setFavoriteStatus(prev => ({ ...prev, [serverKey]: true }));
                            }
                          } catch (error) {
                            console.error("Failed to toggle favorite:", error);
                          } finally {
                            setLocalLoading(prev => ({ ...prev, [serverKey]: false }));
                          }
                        }}
                      >
                        {favoriteStatus[`${server.address}:${server.port}`]
                          ? <IconStarFilled size={14} />
                          : <IconStar size={14} />
                        }
                      </ActionIcon>
                      <ActionIcon
                        size="xs"
                        variant="subtle"
                        color="blue"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeServer(server.address, server.port);
                        }}
                      >
                        <IconTrash size={14} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        )}
      </Box>
    </Container>
  );
}

export default ServerList;
