{"name": "qwsb", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@mantine/core": "^8.0.1", "@mantine/form": "^8.0.1", "@mantine/hooks": "^8.0.1", "@mantine/notifications": "^8.0.1", "@tabler/icons-react": "^3.33.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-opener": "^2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-react": "^4.3.4", "vite": "^6.0.3"}}