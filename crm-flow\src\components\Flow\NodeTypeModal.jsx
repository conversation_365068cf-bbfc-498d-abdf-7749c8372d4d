import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

const NodeTypeModal = ({ isOpen, onClose, onSelectNodeType, position, sourceType }) => {
  const [selectedType, setSelectedType] = useState('contact');

  // Reset selected type when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedType('contact');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Selected node type:', selectedType);
    onSelectNodeType(selectedType);
    onClose();
  };

  // Define all possible node types
  const allNodeTypes = [
    { id: 'opportunity', label: 'Opportunity', icon: 'bi-graph-up' },
    { id: 'contact', label: 'Contact', icon: 'bi-person' },
    { id: 'company', label: 'Company', icon: 'bi-building' },
    { id: 'task', label: 'Task', icon: 'bi-check2-square' },
    { id: 'checklist', label: 'Checklist', icon: 'bi-list-check' },
    { id: 'note', label: 'Note', icon: 'bi-sticky' },
    { id: 'picture', label: 'Picture Collection', icon: 'bi-images' },
    { id: 'organization', label: 'Organization', icon: 'bi-diagram-3' }
  ];

  // Filter node types based on source type
  const nodeTypes = sourceType === 'opportunity'
    ? allNodeTypes.filter(type => type.id !== 'opportunity') // Exclude opportunity when source is opportunity
    : allNodeTypes;

  const modalStyle = {
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 1000,
    backgroundColor: 'rgba(40, 40, 40, 0.95)',
    borderRadius: '8px',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    padding: '12px',
    width: '260px',
    color: '#e0e0e0'
  };

  const overlayStyle = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 999
  };

  const titleStyle = {
    margin: '0 0 10px 0',
    fontSize: '16px',
    fontWeight: 600,
    color: '#fff',
    textAlign: 'center'
  };

  const nodeTypeListStyle = {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
    marginBottom: '10px'
  };

  const nodeTypeItemStyle = (isSelected) => ({
    display: 'flex',
    alignItems: 'center',
    padding: '6px 10px',
    borderRadius: '6px',
    cursor: 'pointer',
    backgroundColor: isSelected ? 'rgba(37, 99, 235, 0.3)' : 'rgba(60, 60, 60, 0.5)',
    border: isSelected ? '1px solid rgba(37, 99, 235, 0.5)' : '1px solid rgba(255, 255, 255, 0.1)',
    transition: 'all 0.2s ease'
  });

  const nodeIconStyle = {
    width: '24px',
    height: '24px',
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: '8px',
    fontSize: '14px',
    backgroundColor: 'rgba(255, 255, 255, 0.1)'
  };

  const buttonContainerStyle = {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '6px',
    marginTop: '10px'
  };

  const buttonStyle = (isPrimary) => ({
    padding: '6px 12px',
    borderRadius: '4px',
    cursor: 'pointer',
    fontWeight: 500,
    fontSize: '13px',
    backgroundColor: isPrimary ? '#2563eb' : 'transparent',
    color: isPrimary ? '#fff' : '#e0e0e0',
    border: isPrimary ? 'none' : '1px solid rgba(255, 255, 255, 0.2)',
    transition: 'all 0.2s ease'
  });

  return (
    <>
      <div style={overlayStyle} onClick={onClose} />
      <div style={modalStyle}>
        <h3 style={titleStyle}>Select Node Type</h3>
        <form onSubmit={handleSubmit}>
          <div style={nodeTypeListStyle}>
            {nodeTypes.map((type) => (
              <div
                key={type.id}
                style={nodeTypeItemStyle(selectedType === type.id)}
                onClick={() => setSelectedType(type.id)}
              >
                <div style={nodeIconStyle}>
                  <i className={`bi ${type.icon}`} style={{
                    color: type.id === 'opportunity' ? '#ff6b6b' :
                      type.id === 'contact' ? '#4ecdc4' :
                        type.id === 'company' ? '#45b7d1' :
                          type.id === 'task' ? '#ffd166' :
                            type.id === 'note' ? '#fbc02d' :
                              type.id === 'picture' ? '#f472b6' :
                                '#a78bfa'
                  }}></i>
                </div>
                <span>{type.label}</span>
              </div>
            ))}
          </div>
          <div style={buttonContainerStyle}>
            <button
              type="button"
              style={buttonStyle(false)}
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              style={buttonStyle(true)}
            >
              Create
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

NodeTypeModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSelectNodeType: PropTypes.func.isRequired,
  position: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired
  }).isRequired,
  sourceType: PropTypes.string
};

NodeTypeModal.defaultProps = {
  sourceType: null
};

export default NodeTypeModal;
