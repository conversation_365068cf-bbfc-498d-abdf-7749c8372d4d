#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8f9fa;
  color: #333;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: #333;
}

/* Modern theme for common Bootstrap components */
.card {
  background-color: #fff;
  border-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card-header {
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom-color: rgba(0, 0, 0, 0.1);
  padding: 0.75rem 1.25rem;
  font-weight: 600;
}

.card-body {
  background-color: #fff;
  padding: 1.25rem;
}

.table {
  color: #333;
}

/* Button styling */
.btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.375rem 1rem;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #2563eb;
  border-color: #2563eb;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
}

.btn-outline-primary {
  color: #2563eb;
  border-color: #2563eb;
}

.btn-outline-primary:hover {
  background-color: #2563eb;
  border-color: #2563eb;
  box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
}

/* Form controls */
.form-control, .form-select {
  border-radius: 6px;
  border-color: rgba(0, 0, 0, 0.2);
  padding: 0.5rem 0.75rem;
  transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
}

/* Badge styling */
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
  border-radius: 6px;
}

/* Alert styling */
.alert {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.table-light, .table-light>td, .table-light>th {
  background-color: #3a3a3a;
}

.table-sm td, .table-sm th {
  padding: 0.5rem;
}

.modal-content {
  background-color: #2a2a2a;
  color: #e0e0e0;
}

.form-control, .form-select {
  background-color: #3a3a3a;
  border-color: #555;
  color: #e0e0e0;
}

.form-control:focus, .form-select:focus {
  background-color: #3a3a3a;
  color: #e0e0e0;
}

.input-group-text {
  background-color: #444;
  border-color: #555;
  color: #e0e0e0;
}

.dropdown-menu {
  background-color: #3a3a3a;
  border-color: #555;
}

.dropdown-item {
  color: #e0e0e0;
}

.dropdown-item:hover {
  background-color: #444;
  color: #fff;
}

.list-group-item {
  background-color: #2a2a2a;
  border-color: #444;
  color: #e0e0e0;
}

.react-flow__node {
  border-radius: 3px;
  padding: 10px;
  width: 150px;
  font-size: 12px;
  color: #e0e0e0;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.react-flow__node-opportunity {
  border-color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.2);
}

.react-flow__node-contact {
  border-color: #4ecdc4;
  background-color: rgba(78, 205, 196, 0.2);
}

.react-flow__node-company {
  border-color: #45b7d1;
  background-color: rgba(69, 183, 209, 0.2);
}

.react-flow__node-task {
  border-color: #ffd166;
  background-color: rgba(255, 209, 102, 0.2);
}

.node-delete-button {
  opacity: 0;
  transition: opacity 0.2s;
  font-size: 16px !important;
  font-weight: bold;
}

.react-flow__node:hover .node-delete-button {
  opacity: 1;
}

.node-delete-button:hover {
  color: #d32f2f !important;
  transform: scale(1.2);
}