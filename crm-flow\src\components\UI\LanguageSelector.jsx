import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../contexts/LanguageContext';
import PropTypes from 'prop-types';
import './LanguageSelectorStyles.css';

const LanguageSelector = ({ variant = 'dropdown' }) => {
  const { t } = useTranslation();
  const { language, changeLanguage, languages } = useLanguage();

  // Flag emoji for each language
  const languageFlags = {
    en: '🇬🇧',
    es: '🇪🇸',
    fr: '🇫🇷',
    de: '🇩🇪',
    pl: '🇵🇱'
  };

  // Render as buttons (for settings page)
  if (variant === 'buttons') {
    return (
      <div className="language-buttons">
        {languages.map((lang) => (
          <button
            key={lang.code}
            className={`btn ${language === lang.code ? 'btn-primary' : 'btn-outline-secondary'}`}
            onClick={() => changeLanguage(lang.code)}
          >
            <span className="language-flag">{languageFlags[lang.code]}</span>
            {t(`settings.languages.${lang.code}`)}
          </button>
        ))}
      </div>
    );
  }

  // Render as dropdown (default, for compact display)
  return (
    <div className="dropdown language-dropdown">
      <button
        className="btn btn-sm btn-outline-secondary dropdown-toggle"
        type="button"
        data-bs-toggle="dropdown"
        aria-expanded="false"
      >
        <span className="language-flag">{languageFlags[language]}</span>
        {language.toUpperCase()}
      </button>
      <ul className="dropdown-menu dropdown-menu-end">
        {languages.map((lang) => (
          <li key={lang.code}>
            <button
              className={`dropdown-item ${language === lang.code ? 'active' : ''}`}
              onClick={() => changeLanguage(lang.code)}
            >
              <span className="language-flag">{languageFlags[lang.code]}</span>
              {t(`settings.languages.${lang.code}`)}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

LanguageSelector.propTypes = {
  variant: PropTypes.oneOf(['dropdown', 'buttons'])
};

export default LanguageSelector;
