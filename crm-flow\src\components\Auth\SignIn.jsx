import { useState } from 'react';
import { signInWithEmailAndPassword, signInWithPopup } from 'firebase/auth';
import { auth, googleProvider } from '../../services/firebase';
import { useNavigate } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';

const SignIn = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSignIn = async (e) => {
    e.preventDefault();
    try {
      await signInWithEmailAndPassword(auth, email, password);
      navigate('/dashboard');
    } catch (error) {
      setError(error.message);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signInWithPopup(auth, googleProvider);
      navigate('/dashboard');
    } catch (error) {
      setError(error.message);
    }
  };

  return (
    <div className="container-fluid d-flex align-items-center justify-content-center"
      style={{
        height: '100vh',
        overflow: 'hidden',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      }}>
      <div className="row justify-content-center w-100">
        <div className="col-md-5">
          <div className="card shadow-lg" style={{ borderRadius: '16px', border: 'none' }}>
            <div className="card-body p-5">
              <div className="text-center mb-4">
                <h2 style={{ fontWeight: '700', color: '#333' }}>Welcome to CRM Flow</h2>
                <p className="text-muted">Sign in to manage your opportunities</p>
              </div>

              {error && <div className="alert alert-danger">{error}</div>}

              <form onSubmit={handleSignIn}>
                <div className="mb-4">
                  <label htmlFor="email" className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-control form-control-lg"
                    id="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    style={{ borderRadius: '8px', padding: '12px 16px' }}
                    required
                  />
                </div>
                <div className="mb-4">
                  <div className="d-flex justify-content-between">
                    <label htmlFor="password" className="form-label">Password</label>
                    <a href="#" className="text-decoration-none" style={{ fontSize: '0.9rem' }}>Forgot password?</a>
                  </div>
                  <input
                    type="password"
                    className="form-control form-control-lg"
                    id="password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    style={{ borderRadius: '8px', padding: '12px 16px' }}
                    required
                  />
                </div>
                <div className="d-grid gap-3 mt-5">
                  <button
                    type="submit"
                    className="btn btn-primary btn-lg"
                    style={{
                      borderRadius: '8px',
                      padding: '12px',
                      fontWeight: '600',
                      background: '#4F46E5',
                      border: 'none'
                    }}
                  >
                    Sign In
                  </button>
                  <button
                    type="button"
                    className="btn btn-outline-secondary btn-lg"
                    onClick={handleGoogleSignIn}
                    style={{
                      borderRadius: '8px',
                      padding: '12px',
                      fontWeight: '600',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px'
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-google" viewBox="0 0 16 16">
                      <path d="M15.545 6.558a9.42 9.42 0 0 1 .139 1.626c0 2.434-.87 4.492-2.384 5.885h.002C11.978 15.292 10.158 16 8 16A8 8 0 1 1 8 0a7.689 7.689 0 0 1 5.352 2.082l-2.284 2.284A4.347 4.347 0 0 0 8 3.166c-2.087 0-3.86 1.408-4.492 3.304a4.792 4.792 0 0 0 0 3.063h.003c.635 1.893 2.405 3.301 4.492 3.301 1.078 0 2.004-.276 2.722-.764h-.003a3.702 3.702 0 0 0 1.599-2.431H8v-3.08h7.545z" />
                    </svg>
                    Sign In with Google
                  </button>
                </div>
              </form>

              <div className="text-center mt-4">
                <p className="mb-0">Don't have an account? <a href="/signup" className="text-decoration-none fw-bold" style={{ color: '#4F46E5' }}>Sign Up</a></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
