import { useState } from 'react';
import { useCurrency } from '../../contexts/CurrencyContext';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

const CurrencySelector = ({ variant = 'dropdown' }) => {
  const { t } = useTranslation();
  const { currency, setCurrency, availableCurrencies, loading } = useCurrency();
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState('');

  const handleCurrencyChange = async (currencyCode) => {
    if (currencyCode === currency) return;
    
    setIsUpdating(true);
    setError('');
    
    try {
      const result = await setCurrency(currencyCode);
      if (!result.success) {
        setError(result.error || 'Failed to update currency preference');
      }
    } catch (error) {
      console.error('Error updating currency:', error);
      setError(error.message);
    } finally {
      setIsUpdating(false);
    }
  };

  if (loading) {
    return <div className="text-center">Loading...</div>;
  }

  // Dropdown variant
  if (variant === 'dropdown') {
    return (
      <div className="currency-selector-dropdown">
        <select 
          className="form-select" 
          value={currency} 
          onChange={(e) => handleCurrencyChange(e.target.value)}
          disabled={isUpdating}
        >
          {availableCurrencies.map((curr) => (
            <option key={curr.code} value={curr.code}>
              {curr.code} - {curr.name} ({curr.symbol})
            </option>
          ))}
        </select>
        {isUpdating && (
          <div className="mt-2 text-info">
            <small>
              <i className="bi bi-arrow-repeat me-1"></i>
              Updating...
            </small>
          </div>
        )}
        {error && (
          <div className="mt-2 text-danger">
            <small>
              <i className="bi bi-exclamation-triangle-fill me-1"></i>
              {error}
            </small>
          </div>
        )}
      </div>
    );
  }

  // Buttons variant
  if (variant === 'buttons') {
    return (
      <div className="currency-selector-buttons">
        <div className="d-flex flex-wrap gap-2">
          {availableCurrencies.map((curr) => (
            <button
              key={curr.code}
              className={`btn ${currency === curr.code ? 'btn-primary' : 'btn-outline-secondary'}`}
              onClick={() => handleCurrencyChange(curr.code)}
              disabled={isUpdating || currency === curr.code}
            >
              <span className="d-flex align-items-center">
                <span className="me-1">{curr.symbol}</span>
                <span>{curr.code}</span>
              </span>
            </button>
          ))}
        </div>
        {isUpdating && (
          <div className="mt-2 text-info">
            <small>
              <i className="bi bi-arrow-repeat me-1"></i>
              Updating...
            </small>
          </div>
        )}
        {error && (
          <div className="mt-2 text-danger">
            <small>
              <i className="bi bi-exclamation-triangle-fill me-1"></i>
              {error}
            </small>
          </div>
        )}
      </div>
    );
  }

  // Cards variant
  return (
    <div className="currency-selector-cards">
      <div className="d-flex flex-wrap gap-3">
        {availableCurrencies.map((curr) => (
          <div
            key={curr.code}
            className={`currency-card ${currency === curr.code ? 'selected' : ''}`}
            onClick={() => handleCurrencyChange(curr.code)}
            style={{
              cursor: 'pointer',
              padding: '12px',
              borderRadius: '8px',
              border: currency === curr.code ? '2px solid #2563eb' : '1px solid #e5e7eb',
              backgroundColor: currency === curr.code ? '#eff6ff' : '#ffffff',
              minWidth: '120px',
              transition: 'all 0.2s ease',
            }}
          >
            <div className="d-flex flex-column align-items-center">
              <div className="currency-symbol mb-2" style={{ fontSize: '24px', fontWeight: 'bold' }}>
                {curr.symbol}
              </div>
              <div className="currency-code fw-bold">{curr.code}</div>
              <div className="currency-name small text-muted">{curr.name}</div>
            </div>
          </div>
        ))}
      </div>
      {isUpdating && (
        <div className="mt-3 text-info">
          <small>
            <i className="bi bi-arrow-repeat me-1"></i>
            Updating...
          </small>
        </div>
      )}
      {error && (
        <div className="mt-3 text-danger">
          <small>
            <i className="bi bi-exclamation-triangle-fill me-1"></i>
            {error}
          </small>
        </div>
      )}
    </div>
  );
};

CurrencySelector.propTypes = {
  variant: PropTypes.oneOf(['dropdown', 'buttons', 'cards'])
};

export default CurrencySelector;
