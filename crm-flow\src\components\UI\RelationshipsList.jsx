import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import RelationshipBadge from './RelationshipBadge';
import './RelationshipStyles.css';

/**
 * A component that displays a list of relationships for an entity
 *
 * @param {Object} props - Component props
 * @param {string} props.entityType - The type of the entity (e.g., "opportunity", "contact")
 * @param {string} props.entityId - The ID of the entity
 * @param {boolean} props.compact - Whether to display in compact mode (fewer relationships)
 * @param {boolean} props.hideBreadcrumbs - Whether to hide the breadcrumbs styling
 * @returns {JSX.Element} - The relationships list component
 */
const RelationshipsList = ({ entityType, entityId, compact = false, hideBreadcrumbs = false }) => {
  const [relationships, setRelationships] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRelationships = async () => {
      try {
        setLoading(true);

        // Query relationships where this entity is the source
        const sourceQuery = query(
          collection(db, 'relationships'),
          where('sourceType', '==', entityType),
          where('sourceId', '==', entityId)
          // Removed archived filter to see all relationships
        );

        // Query relationships where this entity is the target
        const targetQuery = query(
          collection(db, 'relationships'),
          where('targetType', '==', entityType),
          where('targetId', '==', entityId)
          // Removed archived filter to see all relationships
        );

        // Execute both queries
        const [sourceSnapshot, targetSnapshot] = await Promise.all([
          getDocs(sourceQuery),
          getDocs(targetQuery)
        ]);

        // Process source relationships
        const sourceRelationships = sourceSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          direction: 'outgoing'
        }));

        // Process target relationships
        const targetRelationships = targetSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          direction: 'incoming'
        }));

        // Combine and sort relationships
        const allRelationships = [...sourceRelationships, ...targetRelationships];

        // Fetch entity names for all relationships
        const enhancedRelationships = await Promise.all(
          allRelationships.map(async (rel) => {
            try {
              // Determine which entity to fetch (the one that's not the current entity)
              const fetchType = rel.direction === 'outgoing' ? rel.targetType : rel.sourceType;
              const fetchId = rel.direction === 'outgoing' ? rel.targetId : rel.sourceId;

              // Fetch the entity directly by its document ID
              const entityDoc = await getDoc(doc(db, `${fetchType}s`, fetchId));

              // Get the entity name based on its type
              let entityName = 'Unknown';
              if (entityDoc.exists()) {
                const entityData = entityDoc.data();
                if (fetchType === 'opportunity') entityName = entityData.name;
                else if (fetchType === 'contact') entityName = `${entityData.firstName} ${entityData.lastName}`;
                else if (fetchType === 'company') entityName = entityData.name;
                else if (fetchType === 'task') entityName = entityData.title;
              }

              return {
                ...rel,
                relatedEntityName: entityName,
                relatedEntityType: fetchType,
                relatedEntityId: fetchId
              };
            } catch (error) {
              console.error('Error fetching related entity:', error);
              return {
                ...rel,
                relatedEntityName: 'Unknown',
                relatedEntityType: rel.direction === 'outgoing' ? rel.targetType : rel.sourceType,
                relatedEntityId: rel.direction === 'outgoing' ? rel.targetId : rel.sourceId
              };
            }
          })
        );

        setRelationships(enhancedRelationships);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching relationships:', error);
        setError('Failed to load relationships');
        setLoading(false);
      }
    };

    if (entityType && entityId) {
      fetchRelationships();
    }
  }, [entityType, entityId]);

  if (loading) {
    return <div className="text-center"><small className="text-muted">Loading relationships...</small></div>;
  }

  if (error) {
    return <div className="text-danger"><small>{error}</small></div>;
  }

  if (relationships.length === 0) {
    return <div><small className="text-muted">No relationships found</small></div>;
  }

  // Limit the number of relationships shown in compact mode
  const displayRelationships = compact ? relationships.slice(0, 3) : relationships;

  // Group relationships by type
  const groupedRelationships = displayRelationships.reduce((acc, rel) => {
    const type = rel.relatedEntityType;
    if (!acc[type]) acc[type] = [];
    acc[type].push(rel);
    return acc;
  }, {});

  return (
    <div className="relationships-list">
      {Object.entries(groupedRelationships).map(([type, rels]) => (
        <div key={type} className="mb-2">
          <h6 className="text-capitalize mb-2">
            <i className={`bi ${type === 'opportunity' ? 'bi-graph-up' :
              type === 'contact' ? 'bi-person' :
                type === 'company' ? 'bi-building' :
                  type === 'task' ? 'bi-check2-square' : 'bi-link'}`}></i>
            {' '}{type}s
          </h6>
          <div className="d-flex flex-wrap">
            {rels.map(rel => (
              <RelationshipBadge
                key={rel.id}
                type={rel.type}
                entityType={rel.relatedEntityType}
                entityId={rel.relatedEntityId}
                entityName={rel.relatedEntityName}
                hideBreadcrumbs={hideBreadcrumbs}
              />
            ))}
          </div>
        </div>
      ))}

      {compact && relationships.length > 3 && (
        <div className="d-inline-block mt-2">
          <Link to={`/${entityType}s/${entityId}`} className="text-decoration-none">
            <small className="text-primary">+{relationships.length - 3} more</small>
          </Link>
        </div>
      )}
    </div>
  );
};

RelationshipsList.propTypes = {
  entityType: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  compact: PropTypes.bool,
  hideBreadcrumbs: PropTypes.bool
};

export default RelationshipsList;
