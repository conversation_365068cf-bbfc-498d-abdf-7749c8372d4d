import { memo, useEffect, useState } from 'react';

const NewNotification = ({ message, type, duration, onClose }) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (!message) return;

    // Show the notification
    setVisible(true);

    // Set a timer to hide the notification
    const timer = setTimeout(() => {
      setVisible(false);
      // Call onClose after animation completes
      setTimeout(() => {
        if (onClose) onClose();
      }, 300); // Animation duration
    }, duration || 3000);

    return () => clearTimeout(timer);
  }, [message, duration, onClose]);

  if (!message) return null;

  const typeClass = {
    'success': 'flow-notification-success',
    'error': 'flow-notification-error',
    'warning': 'flow-notification-warning',
    'info': 'flow-notification-info'
  }[type] || 'flow-notification-info';

  const icon = {
    'success': 'bi-check-circle',
    'error': 'bi-exclamation-circle',
    'warning': 'bi-exclamation-triangle',
    'info': 'bi-info-circle'
  }[type] || 'bi-info-circle';

  return (
    <div
      className={`flow-notification ${typeClass}`}
      style={{
        animation: visible ? 'fadeIn 0.3s ease' : 'fadeOut 0.3s ease',
        opacity: visible ? 1 : 0
      }}
    >
      <div className="flow-notification-content">
        <i className={`bi ${icon} me-2`}></i>
        {message}
      </div>
    </div>
  );
};

export default memo(NewNotification);
