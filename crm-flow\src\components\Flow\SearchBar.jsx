import React, { useState, useEffect, useRef } from 'react';
import './SearchBarStyles.css';

const SearchBar = ({ nodes, onNodeFound }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isActive, setIsActive] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchRef = useRef(null);
  const resultsRef = useRef(null);

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    if (value.trim() === '') {
      setSearchResults([]);
      return;
    }

    // Search through nodes
    const results = searchNodes(value);
    setSearchResults(results);
    setSelectedIndex(results.length > 0 ? 0 : -1);
  };

  // Search through nodes for matching text
  const searchNodes = (term) => {
    if (!nodes || !term) return [];
    
    const lowerTerm = term.toLowerCase();
    
    return nodes
      .filter(node => {
        const data = node.data;
        
        // Check different fields based on node type
        switch (node.type) {
          case 'opportunity':
            return (
              (data.name && data.name.toLowerCase().includes(lowerTerm)) ||
              (data.company && data.company.toLowerCase().includes(lowerTerm)) ||
              (data.description && data.description.toLowerCase().includes(lowerTerm))
            );
          case 'contact':
            return (
              (data.firstName && data.firstName.toLowerCase().includes(lowerTerm)) ||
              (data.lastName && data.lastName.toLowerCase().includes(lowerTerm)) ||
              (data.email && data.email.toLowerCase().includes(lowerTerm)) ||
              (data.company && data.company.toLowerCase().includes(lowerTerm))
            );
          case 'company':
            return (
              (data.name && data.name.toLowerCase().includes(lowerTerm)) ||
              (data.industry && data.industry.toLowerCase().includes(lowerTerm))
            );
          case 'task':
            return (
              (data.title && data.title.toLowerCase().includes(lowerTerm)) ||
              (data.description && data.description.toLowerCase().includes(lowerTerm))
            );
          case 'checklist':
            return (
              (data.title && data.title.toLowerCase().includes(lowerTerm)) ||
              (data.description && data.description.toLowerCase().includes(lowerTerm)) ||
              (data.items && data.items.some(item => 
                item.text && item.text.toLowerCase().includes(lowerTerm)
              ))
            );
          default:
            return false;
        }
      })
      .map(node => ({
        id: node.id,
        type: node.type,
        title: getNodeTitle(node),
        subtitle: getNodeSubtitle(node)
      }))
      .slice(0, 5); // Limit to 5 results
  };

  // Get the main title to display for a node
  const getNodeTitle = (node) => {
    const data = node.data;
    switch (node.type) {
      case 'opportunity':
        return data.name || 'Unnamed Opportunity';
      case 'contact':
        return `${data.firstName || ''} ${data.lastName || ''}`.trim() || 'Unnamed Contact';
      case 'company':
        return data.name || 'Unnamed Company';
      case 'task':
        return data.title || 'Unnamed Task';
      case 'checklist':
        return data.title || 'Unnamed Checklist';
      default:
        return 'Unknown Node';
    }
  };

  // Get the subtitle to display for a node
  const getNodeSubtitle = (node) => {
    const data = node.data;
    switch (node.type) {
      case 'opportunity':
        return data.company || '';
      case 'contact':
        return data.email || data.company || '';
      case 'company':
        return data.industry || '';
      case 'task':
        return data.description ? data.description.substring(0, 30) + (data.description.length > 30 ? '...' : '') : '';
      case 'checklist':
        return `${data.items?.length || 0} items`;
      default:
        return '';
    }
  };

  // Handle result selection
  const handleResultClick = (result) => {
    onNodeFound(result.id);
    setSearchTerm('');
    setSearchResults([]);
    setIsActive(false);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (searchResults.length === 0) return;

    // Arrow down
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < searchResults.length - 1 ? prev + 1 : prev
      );
    }
    // Arrow up
    else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > 0 ? prev - 1 : 0);
    }
    // Enter
    else if (e.key === 'Enter' && selectedIndex >= 0) {
      e.preventDefault();
      handleResultClick(searchResults[selectedIndex]);
    }
    // Escape
    else if (e.key === 'Escape') {
      e.preventDefault();
      setSearchTerm('');
      setSearchResults([]);
      setIsActive(false);
    }
  };

  // Handle clicks outside the search component
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        searchRef.current && 
        !searchRef.current.contains(event.target) &&
        resultsRef.current && 
        !resultsRef.current.contains(event.target)
      ) {
        setIsActive(false);
        setSearchResults([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Scroll selected result into view
  useEffect(() => {
    if (selectedIndex >= 0 && resultsRef.current) {
      const selectedElement = resultsRef.current.children[selectedIndex];
      if (selectedElement) {
        selectedElement.scrollIntoView({ block: 'nearest' });
      }
    }
  }, [selectedIndex]);

  return (
    <div className="flow-search-container">
      <div 
        className={`flow-search-bar ${isActive ? 'active' : ''}`}
        ref={searchRef}
      >
        <div className="flow-search-icon">
          <i className="bi bi-search"></i>
        </div>
        <input
          type="text"
          className="flow-search-input"
          placeholder="Search nodes..."
          value={searchTerm}
          onChange={handleSearchChange}
          onFocus={() => setIsActive(true)}
          onKeyDown={handleKeyDown}
        />
        {searchTerm && (
          <button 
            className="flow-search-clear" 
            onClick={() => {
              setSearchTerm('');
              setSearchResults([]);
            }}
          >
            <i className="bi bi-x"></i>
          </button>
        )}
      </div>

      {isActive && searchResults.length > 0 && (
        <div className="flow-search-results" ref={resultsRef}>
          {searchResults.map((result, index) => (
            <div
              key={result.id}
              className={`flow-search-result ${index === selectedIndex ? 'selected' : ''}`}
              onClick={() => handleResultClick(result)}
            >
              <div className="flow-search-result-icon">
                {result.type === 'opportunity' && <i className="bi bi-graph-up" style={{ color: '#ff6b6b' }}></i>}
                {result.type === 'contact' && <i className="bi bi-person" style={{ color: '#4ecdc4' }}></i>}
                {result.type === 'company' && <i className="bi bi-building" style={{ color: '#45b7d1' }}></i>}
                {result.type === 'task' && <i className="bi bi-check2-square" style={{ color: '#ffd166' }}></i>}
                {result.type === 'checklist' && <i className="bi bi-list-check" style={{ color: '#a78bfa' }}></i>}
              </div>
              <div className="flow-search-result-content">
                <div className="flow-search-result-title">{result.title}</div>
                {result.subtitle && (
                  <div className="flow-search-result-subtitle">{result.subtitle}</div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
