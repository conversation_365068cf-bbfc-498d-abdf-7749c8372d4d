import { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../services/firebase';

// Define available currencies
export const AVAILABLE_CURRENCIES = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
  { code: 'CNY', symbol: '¥', name: 'Chinese Yuan' },
  { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
  { code: 'BRL', symbol: 'R$', name: 'Brazilian Real' },
  { code: 'MX<PERSON>', symbol: 'Mex$', name: 'Mexican Peso' },
  { code: 'PLN', symbol: 'zł', name: 'Polish Złoty' },
];

// Create the context
const CurrencyContext = createContext();

// Create a provider component
export const CurrencyProvider = ({ children }) => {
  const { currentUser, userProfile } = useAuth();
  const [currency, setCurrency] = useState('USD');
  const [loading, setLoading] = useState(true);

  // Load user's currency preference from profile
  useEffect(() => {
    const loadCurrencyPreference = async () => {
      if (currentUser && userProfile) {
        try {
          // If user has a currency preference in their profile, use it
          if (userProfile.currency) {
            setCurrency(userProfile.currency);
          }
        } catch (error) {
          console.error('Error loading currency preference:', error);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadCurrencyPreference();
  }, [currentUser, userProfile]);

  // Update user's currency preference
  const updateCurrencyPreference = async (newCurrency) => {
    if (!currentUser) return { success: false, error: 'User not authenticated' };

    try {
      // Update in Firestore
      const userRef = doc(db, 'users', currentUser.uid);
      await updateDoc(userRef, { currency: newCurrency });

      // Update local state
      setCurrency(newCurrency);

      return { success: true };
    } catch (error) {
      console.error('Error updating currency preference:', error);
      return { success: false, error: error.message };
    }
  };

  // Format a value according to the selected currency
  const formatCurrency = (value) => {
    if (value === undefined || value === null) return '-';

    // Find the currency object
    const currencyObj = AVAILABLE_CURRENCIES.find(c => c.code === currency) || AVAILABLE_CURRENCIES[0];

    // Special case for JPY and other currencies that don't typically use decimal places
    const fractionDigits = ['JPY', 'KRW', 'VND', 'IDR'].includes(currency) ? 0 : 2;

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: fractionDigits,
      maximumFractionDigits: fractionDigits
    }).format(value);
  };

  // Get currency symbol
  const getCurrencySymbol = () => {
    const currencyObj = AVAILABLE_CURRENCIES.find(c => c.code === currency) || AVAILABLE_CURRENCIES[0];
    return currencyObj.symbol;
  };

  // Context value
  const value = {
    currency,
    setCurrency: updateCurrencyPreference,
    formatCurrency,
    getCurrencySymbol,
    availableCurrencies: AVAILABLE_CURRENCIES,
    loading
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};

// Custom hook to use the currency context
export const useCurrency = () => {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};
