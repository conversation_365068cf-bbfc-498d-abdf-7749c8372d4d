import { createContext, useContext, useState, useEffect } from 'react';
import { onAuthStateChanged, updateProfile } from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '../services/firebase';
import { getOrganization } from '../services/organizationService';
import { getUserMemberOrganizations } from '../services/userOrganizationService';
import { getSubscriptionPlan } from '../services/subscriptionService';

// For debugging
const DEBUG = true;

const AuthContext = createContext();

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [organization, setOrganization] = useState(null);
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch user profile data from Firestore
  const fetchUserProfile = async (user, specificOrgId = null) => {
    try {
      if (DEBUG) console.log('Fetching user profile for:', user.uid, 'specificOrgId:', specificOrgId);

      const userRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        // User profile exists
        const userData = userDoc.data();
        if (DEBUG) console.log('User data loaded:', userData);
        setUserProfile({ id: user.uid, ...userData });

        // If a specific organization ID is provided, fetch that organization
        if (specificOrgId) {
          if (DEBUG) console.log('Fetching specific organization:', specificOrgId);
          const org = await getOrganization(specificOrgId);
          if (org && org.members && org.members.includes(user.uid)) {
            if (DEBUG) console.log('Setting organization from specific ID:', org.name);
            setOrganization(org);

            // Update user's current organization in Firestore
            await updateDoc(userRef, {
              currentOrganizationId: specificOrgId
            });

            // Fetch subscription data
            const subscriptionData = await getSubscriptionPlan(org.id);
            setSubscription(subscriptionData);
          } else {
            if (DEBUG) console.log('User is not a member of the specified organization');
            setOrganization(null);
            setSubscription(null);
          }
        }
        // Otherwise, if user belongs to an organization, fetch organization data
        else if (userData.currentOrganizationId) {
          if (DEBUG) console.log('Fetching current organization:', userData.currentOrganizationId);
          try {
            const org = await getOrganization(userData.currentOrganizationId);
            if (org && org.members && org.members.includes(user.uid)) {
              if (DEBUG) console.log('Setting organization from current ID:', org.name);
              setOrganization(org);

              // Fetch subscription data
              const subscriptionData = await getSubscriptionPlan(org.id);
              setSubscription(subscriptionData);
            } else {
              if (DEBUG) console.log('User is not a member of their current organization or org not found');
              setOrganization(null);
              setSubscription(null);

              // Clear the invalid currentOrganizationId
              await updateDoc(userRef, {
                currentOrganizationId: null
              });

              // Try to find another organization
              const orgs = await getUserMemberOrganizations(user.uid);
              if (orgs && orgs.length > 0) {
                const newOrg = orgs[0]; // Use the first organization
                if (DEBUG) console.log('Found another organization to use:', newOrg.name);
                setOrganization(newOrg);

                // Update user's current organization in Firestore
                await updateDoc(userRef, {
                  currentOrganizationId: newOrg.id
                });

                // Fetch subscription data
                const subscriptionData = await getSubscriptionPlan(newOrg.id);
                setSubscription(subscriptionData);
              }
            }
          } catch (error) {
            console.error('Error fetching organization:', error);
            setOrganization(null);
            setSubscription(null);

            // Clear the invalid currentOrganizationId
            await updateDoc(userRef, {
              currentOrganizationId: null
            });
          }
        } else {
          // If no current organization is set, try to find one where the user is a member
          if (DEBUG) console.log('No current organization, looking for organizations where user is a member');
          try {
            const orgs = await getUserMemberOrganizations(user.uid);
            if (DEBUG) console.log('Found organizations:', orgs.length);

            if (orgs && orgs.length > 0) {
              const org = orgs[0]; // Use the first organization
              if (DEBUG) console.log('Setting organization from member orgs:', org.name);
              setOrganization(org);

              // Update user's current organization in Firestore
              await updateDoc(userRef, {
                currentOrganizationId: org.id
              });

              // Fetch subscription data
              const subscriptionData = await getSubscriptionPlan(org.id);
              setSubscription(subscriptionData);
            } else {
              if (DEBUG) console.log('User has no organizations');
              setOrganization(null);
              setSubscription(null);
            }
          } catch (error) {
            console.error('Error fetching member organizations:', error);
            setOrganization(null);
            setSubscription(null);
          }
        }
      } else {
        // Create user profile if it doesn't exist
        const newUserData = {
          name: user.displayName || '',
          email: user.email || '',
          photoURL: user.photoURL || '',
          createdAt: new Date(),
          role: 'user', // Default role
          currentOrganizationId: null // No current organization by default
        };

        await setDoc(userRef, newUserData);
        setUserProfile({ id: user.uid, ...newUserData });
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // User is signed in
        await fetchUserProfile(user);
      } else {
        // User is signed out
        setUserProfile(null);
        setOrganization(null);
        setSubscription(null);
      }

      setCurrentUser(user);
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  // Function to switch to a different organization
  const switchOrganization = async (organizationId) => {
    if (currentUser && organizationId) {
      try {
        // Get the organization data
        const org = await getOrganization(organizationId);

        if (!org) {
          console.error('Organization not found');
          return false;
        }

        if (!org.members || !org.members.includes(currentUser.uid)) {
          console.error('User is not a member of this organization');
          return false;
        }

        // Update the user's current organization in Firestore
        const userRef = doc(db, 'users', currentUser.uid);
        await updateDoc(userRef, {
          currentOrganizationId: organizationId,
          updatedAt: serverTimestamp()
        });

        // Update the local state
        setOrganization(org);

        // Fetch subscription data
        const subscriptionData = await getSubscriptionPlan(org.id);
        setSubscription(subscriptionData);

        return true;
      } catch (error) {
        console.error('Error switching organization:', error);
        return false;
      }
    }
    return false;
  };

  // Refresh user profile and sync photoURL with Firebase Auth
  const refreshUserProfile = async () => {
    if (currentUser) {
      await fetchUserProfile(currentUser);

      // Sync photoURL between Firestore and Firebase Auth
      if (userProfile && userProfile.photoURL && currentUser.photoURL !== userProfile.photoURL) {
        try {
          await updateProfile(currentUser, {
            photoURL: userProfile.photoURL
          });
          console.log('Updated user photoURL in Firebase Auth');
        } catch (error) {
          console.error('Error updating user photoURL in Firebase Auth:', error);
        }
      }
    }
  };

  const value = {
    currentUser,
    userProfile,
    organization,
    subscription,
    loading,
    refreshUserProfile,
    switchOrganization
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
