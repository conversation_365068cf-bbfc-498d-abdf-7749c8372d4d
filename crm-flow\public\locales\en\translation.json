{"common": {"loading": "Loading...", "error": "An error occurred", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "filter": "Filter", "noResults": "No results found", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "required": "Required", "optional": "Optional", "yes": "Yes", "no": "No", "confirm": "Confirm", "actions": "Actions", "joining": "Joining...", "saving": "Saving..."}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "displayName": "Display Name", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "resetPasswordInstructions": "Enter your email address and we'll send you instructions to reset your password.", "account": "Account", "signOutDescription": "Sign out from your account"}, "navigation": {"dashboard": "Dashboard", "opportunities": "Opportunities", "contacts": "Contacts", "companies": "Companies", "tasks": "Tasks", "checklists": "Checklists", "flow": "Flow View", "settings": "Settings", "organizations": "Organizations", "plugins": "Plugins"}, "dashboard": {"title": "Dashboard", "recentOpportunities": "Recent Opportunities", "upcomingTasks": "Upcoming Tasks", "stats": {"opportunities": "Opportunities", "contacts": "Contacts", "companies": "Companies", "tasks": "Tasks"}, "viewAll": "View All", "noOpportunities": "No recent opportunities", "noTasks": "No upcoming tasks"}, "opportunities": {"title": "Opportunities", "newOpportunity": "New Opportunity", "name": "Name", "company": "Company", "value": "Value", "status": "Status", "closeDate": "Close Date", "description": "Description", "phone": "Phone", "email": "Email", "address": "Address", "statuses": {"new": "New", "qualified": "Qualified", "proposal": "Proposal", "negotiation": "Negotiation", "closed_won": "Closed Won", "closed_lost": "Closed Lost"}}, "contacts": {"title": "Title", "newContact": "New Contact", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "company": "Company", "position": "Position", "address": "Address", "notes": "Notes", "noContacts": "No contacts found"}, "companies": {"title": "Companies", "newCompany": "New Company", "name": "Name", "industry": "Industry", "size": "Size", "website": "Website", "phone": "Phone", "address": "Address", "description": "Description", "noCompanies": "No companies found"}, "tasks": {"title": "Title", "newTask": "New Task", "description": "Description", "dueDate": "Due Date", "priority": "Priority", "status": "Status", "assignedTo": "Assigned To", "relatedTo": "Related To", "priorities": {"high": "High", "medium": "Medium", "low": "Low"}, "statuses": {"notStarted": "Not Started", "inProgress": "In Progress", "completed": "Completed"}, "noTasks": "No tasks found"}, "checklists": {"title": "Title", "newChecklist": "New Checklist", "description": "Description", "items": "Items", "addItem": "Add Item", "noItems": "No items yet", "completed": "Completed", "noChecklists": "No checklists found"}, "flow": {"title": "Flow View", "showArchived": "Show Archived", "saveToFirebase": "Save to Firebase", "sync": "Sync", "syncWithFirebase": "Sync with Firebase", "unsavedChanges": "Unsaved Changes", "remoteChanges": "Remote Changes", "createNode": "Create New Node", "selectNodeType": "Select Node Type", "opportunity": "Opportunity", "contact": "Contact", "company": "Company", "task": "Task", "checklist": "Checklist", "cancel": "Cancel", "create": "Create", "edit": "Edit", "delete": "Delete", "archive": "Archive", "unarchive": "Unarchive", "connect": "Connect", "disconnect": "Disconnect", "nodeTypes": {"opportunity": "Opportunity", "contact": "Contact", "company": "Company", "task": "Task", "checklist": "Checklist", "organization": "Organization"}}, "settings": {"title": "Settings", "profile": "Profile", "account": "Account", "notifications": "Notifications", "language": "Language", "theme": "Theme", "currency": "<PERSON><PERSON><PERSON><PERSON>", "subscription": "Subscription", "organizations": "Organizations", "avatar": "Avatar", "avatarHelp": "Click on an avatar to select it as your profile picture.", "languages": {"selectLanguage": "Select your preferred language:", "changeInfo": "Your language preference will be saved to your profile and applied across all your devices.", "en": "English", "es": "Spanish", "fr": "French", "de": "German", "pl": "Polish"}, "currencies": {"selectCurrency": "Select your preferred currency:", "changeInfo": "Your currency preference will be saved to your profile and applied across all your devices."}, "themes": {"dark": "Dark", "light": "Light"}, "saveChanges": "Save Changes"}, "organizations": {"title": "Organizations", "newOrganization": "New Organization", "createOrganization": "Create Organization", "editOrganization": "Edit Organization", "name": "Name", "description": "Description", "industry": "Industry", "size": "Size", "members": "members", "owner": "Owner", "member": "Member", "current": "Current", "addMember": "Add Member", "removeMember": "Remove Member", "leaveOrganization": "Leave Organization", "deleteOrganization": "Delete Organization", "noOrganizations": "No Organizations Found", "noOrganizationsDesc": "You don't belong to any organizations yet.", "switchOrganization": "Switch Organization", "currentOrganization": "Current Organization", "joinOrganization": "Join Organization", "organizationUUID": "Organization UUID", "enterOrganizationUUID": "Enter organization UUID", "enterUUIDToJoin": "Enter the UUID of the organization you want to join.", "loading": "Loading your organizations...", "switchSuccess": "Successfully switched organization", "switchError": "Failed to switch organization"}, "subscription": {"title": "Subscription", "currentPlan": "Current Plan", "upgradePlan": "Upgrade Plan", "manageBilling": "Manage Billing", "cancelSubscription": "Cancel Subscription", "renewsOn": "Renews on", "plans": {"free": "Free", "starter": "Starter", "basic": "Basic", "professional": "Professional", "premium": "Premium", "enterprise": "Enterprise"}, "features": {"users": "Users", "organizations": "Organizations", "contacts": "Contacts", "storage": "Storage", "support": "Support", "analytics": "Analytics", "customBranding": "Custom Branding", "apiAccess": "API Access"}}, "errors": {"required": "This field is required", "invalidEmail": "Invalid email address", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters", "invalidDate": "Invalid date", "networkError": "Network error. Please check your connection.", "unknownError": "An unknown error occurred. Please try again."}, "plugins": {"title": "Plugins", "description": "Extend your CRM with powerful plugins", "importOpportunities": {"title": "Import Opportunities", "description": "Import opportunities from various data formats", "button": "Import Opportunities"}, "importExcel": {"title": "Import from Excel", "description": "Import opportunities from Excel files with field mapping", "button": "Import from Excel"}}}