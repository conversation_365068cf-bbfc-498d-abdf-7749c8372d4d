# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage
*.lcov
.nyc_output

# Production build files
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Tauri build artifacts
src-tauri/target/
src-tauri/Cargo.lock
src-tauri/WixTools

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Rust specific
**/*.rs.bk
*.pdb

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS specific
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux specific
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Package files
*.tgz
*.zip
*.tar.gz

# Debug files
debug/
