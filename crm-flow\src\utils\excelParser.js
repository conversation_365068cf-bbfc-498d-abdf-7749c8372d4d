/**
 * Excel parser utility for importing data from Excel files
 * Uses the xlsx library to parse Excel files
 */

// We'll use the xlsx library for parsing Excel files
// This will be imported dynamically to avoid issues if the package isn't installed

/**
 * Parse an Excel file and return the data as an array of objects
 * @param {File} file - The Excel file to parse
 * @param {Object} options - Options for parsing
 * @param {number} options.sheetIndex - The index of the sheet to parse (default: 0)
 * @param {string} options.sheetName - The name of the sheet to parse (takes precedence over sheetIndex)
 * @returns {Promise<Array>} - Promise resolving to an array of objects representing the rows
 */
export const parseExcelFile = async (file, options = {}) => {
  try {
    // Dynamically import xlsx to handle cases where it might not be installed
    const XLSX = await import('xlsx').catch(() => {
      throw new Error('XLSX library not found. Please run "npm install xlsx" to use Excel import features.');
    });
    
    // Read the file
    const data = await readFileAsArrayBuffer(file);
    const workbook = XLSX.read(data, { type: 'array' });
    
    // Get the sheet
    const sheetName = options.sheetName || workbook.SheetNames[options.sheetIndex || 0];
    if (!sheetName || !workbook.Sheets[sheetName]) {
      throw new Error('Sheet not found in Excel file');
    }
    
    // Convert to JSON
    const jsonData = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], {
      header: options.headerRow ? undefined : 1,
      range: options.headerRow ? options.headerRow - 1 : undefined,
      defval: ''
    });
    
    return jsonData;
  } catch (error) {
    console.error('Error parsing Excel file:', error);
    throw error;
  }
};

/**
 * Read a file as an ArrayBuffer
 * @param {File} file - The file to read
 * @returns {Promise<ArrayBuffer>} - Promise resolving to the file contents as an ArrayBuffer
 */
const readFileAsArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(e);
    reader.readAsArrayBuffer(file);
  });
};

/**
 * Get the list of sheets in an Excel file
 * @param {File} file - The Excel file
 * @returns {Promise<Array<string>>} - Promise resolving to an array of sheet names
 */
export const getExcelSheets = async (file) => {
  try {
    const XLSX = await import('xlsx').catch(() => {
      throw new Error('XLSX library not found. Please run "npm install xlsx" to use Excel import features.');
    });
    
    const data = await readFileAsArrayBuffer(file);
    const workbook = XLSX.read(data, { type: 'array' });
    
    return workbook.SheetNames;
  } catch (error) {
    console.error('Error getting Excel sheets:', error);
    throw error;
  }
};

/**
 * Get a preview of the data in an Excel file (first few rows)
 * @param {File} file - The Excel file
 * @param {Object} options - Options for parsing
 * @param {number} options.sheetIndex - The index of the sheet to parse (default: 0)
 * @param {string} options.sheetName - The name of the sheet to parse (takes precedence over sheetIndex)
 * @param {number} options.maxRows - Maximum number of rows to return (default: 5)
 * @returns {Promise<Array>} - Promise resolving to an array of objects representing the rows
 */
export const getExcelPreview = async (file, options = {}) => {
  const data = await parseExcelFile(file, options);
  const maxRows = options.maxRows || 5;
  return data.slice(0, maxRows);
};

/**
 * Normalize field names to match the expected format (lowercase with underscores)
 * @param {Array} data - The data to normalize
 * @returns {Array} - The normalized data
 */
export const normalizeFieldNames = (data) => {
  return data.map(item => {
    const normalizedItem = {};
    Object.keys(item).forEach(key => {
      const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');
      normalizedItem[normalizedKey] = item[key];
    });
    return normalizedItem;
  });
};
