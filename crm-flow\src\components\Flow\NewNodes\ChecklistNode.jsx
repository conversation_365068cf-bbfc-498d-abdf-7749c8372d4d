import { memo, useState, useRef } from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '../../../services/firebase';
import NodeMenu from './NodeMenu';

const ChecklistNode = ({ data, selected }) => {
  const [newItem, setNewItem] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [editingItemId, setEditingItemId] = useState(null);
  const [editText, setEditText] = useState('');
  const inputRef = useRef(null);
  const editInputRef = useRef(null);

  // Extract the actual ID from the node ID (format: "checklist-id")
  const getActualId = () => {
    if (!data.id) return null;
    const parts = data.id.split('-');
    return parts.length > 1 ? parts[1] : null;
  };

  // Handle node actions
  const handleEdit = () => {
    if (data.onEdit) data.onEdit(data.id);
  };

  const handleDelete = () => {
    if (data.onDelete) data.onDelete(data.id);
  };

  const handleArchive = () => {
    if (data.onArchive) data.onArchive(data.id);
  };

  const handleRestore = () => {
    if (data.onRestore) data.onRestore(data.id);
  };

  // Add a new checklist item
  const handleAddItem = async (e) => {
    e.preventDefault();
    if (!newItem.trim() || loading) return;

    setLoading(true);
    setError('');

    try {
      const actualId = getActualId();
      if (!actualId) throw new Error('Invalid checklist ID');

      // Get current items or initialize empty array
      const items = Array.isArray(data.items) ? [...data.items] : [];

      // Add new item with completed status set to false
      const newItemObj = {
        id: Date.now().toString(), // Simple unique ID
        text: newItem.trim(),
        completed: false,
        createdAt: new Date()
      };

      items.push(newItemObj);

      // Update the document in Firestore
      const checklistRef = doc(db, 'checklists', actualId);
      await updateDoc(checklistRef, { items });

      // Update local state
      data.items = items;

      // Clear input
      setNewItem('');
    } catch (error) {
      console.error('Error adding checklist item:', error);
      setError('Failed to add item');
    } finally {
      setLoading(false);
    }
  };

  // Toggle item completion status
  const handleToggleItem = async (itemId) => {
    setLoading(true);
    setError('');

    try {
      const actualId = getActualId();
      if (!actualId) throw new Error('Invalid checklist ID');

      // Get current items
      const items = Array.isArray(data.items) ? [...data.items] : [];

      // Find and toggle the item
      const updatedItems = items.map(item => {
        if (item.id === itemId) {
          return { ...item, completed: !item.completed };
        }
        return item;
      });

      // Update the document in Firestore
      const checklistRef = doc(db, 'checklists', actualId);
      await updateDoc(checklistRef, { items: updatedItems });

      // Update local state
      data.items = updatedItems;
    } catch (error) {
      console.error('Error toggling checklist item:', error);
      setError('Failed to update item');
    } finally {
      setLoading(false);
    }
  };

  // Start editing an item
  const handleEditItem = (item) => {
    setEditingItemId(item.id);
    setEditText(item.text);
    // Focus the edit input after rendering
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 10);
  };

  // Save edited item
  const handleSaveEdit = async (itemId) => {
    if (!editText.trim() || loading) return;

    setLoading(true);
    setError('');

    try {
      const actualId = getActualId();
      if (!actualId) throw new Error('Invalid checklist ID');

      // Get current items
      const items = Array.isArray(data.items) ? [...data.items] : [];

      // Update the item text
      const updatedItems = items.map(item => {
        if (item.id === itemId) {
          return { ...item, text: editText.trim() };
        }
        return item;
      });

      // Update the document in Firestore
      const checklistRef = doc(db, 'checklists', actualId);
      await updateDoc(checklistRef, { items: updatedItems });

      // Update local state
      data.items = updatedItems;

      // Exit edit mode
      setEditingItemId(null);
      setEditText('');
    } catch (error) {
      console.error('Error updating checklist item:', error);
      setError('Failed to update item');
    } finally {
      setLoading(false);
    }
  };

  // Delete an item
  const handleDeleteItem = async (itemId) => {
    setLoading(true);
    setError('');

    try {
      const actualId = getActualId();
      if (!actualId) throw new Error('Invalid checklist ID');

      // Get current items
      const items = Array.isArray(data.items) ? [...data.items] : [];

      // Filter out the item to delete
      const updatedItems = items.filter(item => item.id !== itemId);

      // Update the document in Firestore
      const checklistRef = doc(db, 'checklists', actualId);
      await updateDoc(checklistRef, { items: updatedItems });

      // Update local state
      data.items = updatedItems;
    } catch (error) {
      console.error('Error deleting checklist item:', error);
      setError('Failed to delete item');
    } finally {
      setLoading(false);
    }
  };

  // Calculate completion percentage
  const getCompletionPercentage = () => {
    if (!data.items || data.items.length === 0) return 0;
    const completedCount = data.items.filter(item => item.completed).length;
    return Math.round((completedCount / data.items.length) * 100);
  };

  // Calculate completion percentage
  const completionPercentage = getCompletionPercentage();

  return (
    <div className={`node-card node-checklist ${data.archived ? 'node-archived' : ''}`}>
      <NodeMenu
        onEdit={handleEdit}
        onDelete={handleDelete}
        onArchive={handleArchive}
        isArchived={data.archived}
        onRestore={handleRestore}
        nodeType="checklist"
      />
      <div className="node-header">
        <h6 className="node-title">Checklist</h6>
        <span className="node-badge" style={{ backgroundColor: '#a78bfa' }}>
          {data.items?.filter(item => item.completed).length || 0}/{data.items?.length || 0}
        </span>
      </div>
      <div className="node-body">
        <h5 className="node-name">{data.title}</h5>

        {error && <div style={{ color: '#ff6b6b', fontSize: '11px', marginBottom: '8px' }}>{error}</div>}

        {/* Progress bar */}
        <div className="checklist-progress">
          <div
            className="checklist-progress-bar"
            style={{ width: `${completionPercentage}%` }}
            title={`${completionPercentage}% completed`}
          ></div>
        </div>

        <div className="checklist-items">
          {data.items && data.items.length > 0 ? (
            <div>
              {data.items.map(item => (
                <div key={item.id} className="checklist-item">
                  {editingItemId === item.id ? (
                    <div className="checklist-edit-form">
                      <input
                        ref={editInputRef}
                        type="text"
                        className="checklist-edit-input"
                        value={editText}
                        onChange={(e) => setEditText(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit(item.id)}
                        disabled={loading}
                      />
                      <button
                        type="button"
                        className="checklist-save-btn"
                        onClick={() => handleSaveEdit(item.id)}
                        disabled={loading}
                      >
                        <i className="bi bi-check"></i>
                      </button>
                    </div>
                  ) : (
                    <>
                      <input
                        type="checkbox"
                        className="checklist-checkbox"
                        checked={item.completed}
                        onChange={() => handleToggleItem(item.id)}
                        disabled={loading}
                        id={`check-${item.id}`}
                      />
                      <label
                        className={`checklist-label ${item.completed ? 'completed' : ''}`}
                        htmlFor={`check-${item.id}`}
                      >
                        {item.text}
                      </label>
                      <div className="checklist-actions">
                        <button
                          type="button"
                          className="checklist-btn edit"
                          onClick={() => handleEditItem(item)}
                          disabled={loading}
                          title="Edit item"
                        >
                          <i className="bi bi-pencil"></i>
                        </button>
                        <button
                          type="button"
                          className="checklist-btn delete"
                          onClick={() => handleDeleteItem(item.id)}
                          disabled={loading}
                          title="Delete item"
                        >
                          <i className="bi bi-x-lg"></i>
                        </button>
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="checklist-empty">No items yet</div>
          )}
        </div>

        <form onSubmit={handleAddItem} className="checklist-form">
          <input
            ref={inputRef}
            type="text"
            className="checklist-input"
            placeholder="Add new item..."
            value={newItem}
            onChange={(e) => setNewItem(e.target.value)}
            disabled={loading}
          />
          <button
            type="submit"
            className="checklist-add-btn"
            disabled={!newItem.trim() || loading}
            title="Add item"
          >
            <i className="bi bi-plus"></i>
          </button>
        </form>
      </div>
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#555' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: '#555' }}
      />
    </div>
  );
};

export default memo(ChecklistNode);
