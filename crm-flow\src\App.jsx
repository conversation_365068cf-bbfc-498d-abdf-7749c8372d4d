import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { CurrencyProvider } from './contexts/CurrencyContext';
import ProtectedRoute from './components/ProtectedRoute';
import SignIn from './components/Auth/SignIn';
import SignUp from './components/Auth/SignUp';
import Dashboard from './pages/Dashboard';
import FlowPage from './pages/FlowPage';
import ContactsPage from './pages/ContactsPage';
import CompaniesPage from './pages/CompaniesPage';
import OpportunitiesPage from './pages/OpportunitiesPage';
import OpportunityDetailPage from './pages/OpportunityDetailPage';
import ImportOpportunitiesPage from './pages/ImportOpportunitiesPage';
import ImportExcelPage from './pages/ImportExcelPage';
import PluginsPage from './pages/PluginsPage';
import TasksPage from './pages/TasksPage';
// Chat page import removed
import ChecklistsPage from './pages/ChecklistsPage';
import OrganizationPage from './pages/OrganizationPage';
// SettingsPage removed - functionality moved to UserSettingsPage
import UserSettingsPage from './pages/UserSettingsPage';
import SubscriptionPage from './pages/SubscriptionPage';
import 'bootstrap/dist/css/bootstrap.min.css';

function App() {
  return (
    <Router>
      <AuthProvider>
        <LanguageProvider>
          <CurrencyProvider>
            <Routes>
              <Route path="/" element={<Navigate to="/signin" />} />
              <Route path="/signin" element={<SignIn />} />
              <Route path="/signup" element={<SignUp />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/flow"
                element={
                  <ProtectedRoute>
                    <FlowPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/contacts"
                element={
                  <ProtectedRoute>
                    <ContactsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/companies"
                element={
                  <ProtectedRoute>
                    <CompaniesPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/opportunities"
                element={
                  <ProtectedRoute>
                    <OpportunitiesPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/opportunities/:id"
                element={
                  <ProtectedRoute>
                    <OpportunityDetailPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/plugins"
                element={
                  <ProtectedRoute>
                    <PluginsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/import-opportunities"
                element={
                  <ProtectedRoute>
                    <ImportOpportunitiesPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/import-excel"
                element={
                  <ProtectedRoute>
                    <ImportExcelPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/tasks"
                element={
                  <ProtectedRoute>
                    <TasksPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/checklists"
                element={
                  <ProtectedRoute>
                    <ChecklistsPage />
                  </ProtectedRoute>
                }
              />
              {/* Chat route removed */}
              <Route
                path="/organization"
                element={
                  <ProtectedRoute>
                    <OrganizationPage />
                  </ProtectedRoute>
                }
              />
              {/* Settings route removed - functionality moved to UserSettingsPage */}
              <Route
                path="/user-settings"
                element={
                  <ProtectedRoute>
                    <UserSettingsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/subscription"
                element={
                  <ProtectedRoute>
                    <SubscriptionPage />
                  </ProtectedRoute>
                }
              />
            </Routes>
          </CurrencyProvider>
        </LanguageProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
