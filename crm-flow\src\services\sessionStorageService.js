/**
 * Session Storage Service
 * Provides utilities for storing and retrieving data from session storage
 * Used primarily for the flow view to reduce Firebase queries
 */

// Prefix for all session storage keys
const KEY_PREFIX = 'crm_flow_';

// Key for tracking unsaved changes
const UNSAVED_CHANGES_KEY = `${KEY_PREFIX}unsaved_changes`;

// Key for tracking last sync time
const LAST_SYNC_TIME_KEY = `${KEY_PREFIX}last_sync_time`;

/**
 * Generate a key for node positions
 * @param {string} organizationId - The organization ID
 * @returns {string} - The session storage key
 */
const getNodePositionsKey = (organizationId) => `${KEY_PREFIX}node_positions_${organizationId}`;

/**
 * Save node positions to session storage
 * @param {string} organizationId - The organization ID
 * @param {Object} positions - The node positions data
 */
export const saveNodePositionsToSession = (organizationId, positions) => {
  if (!organizationId || !positions) return;
  
  try {
    const key = getNodePositionsKey(organizationId);
    sessionStorage.setItem(key, JSON.stringify(positions));
    
    // Mark as having unsaved changes
    setUnsavedChanges(true);
  } catch (error) {
    console.error('Error saving node positions to session storage:', error);
  }
};

/**
 * Get node positions from session storage
 * @param {string} organizationId - The organization ID
 * @returns {Object|null} - The node positions data or null if not found
 */
export const getNodePositionsFromSession = (organizationId) => {
  if (!organizationId) return null;
  
  try {
    const key = getNodePositionsKey(organizationId);
    const data = sessionStorage.getItem(key);
    
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Error getting node positions from session storage:', error);
    return null;
  }
};

/**
 * Set the unsaved changes flag
 * @param {boolean} hasChanges - Whether there are unsaved changes
 */
export const setUnsavedChanges = (hasChanges) => {
  try {
    sessionStorage.setItem(UNSAVED_CHANGES_KEY, JSON.stringify(hasChanges));
  } catch (error) {
    console.error('Error setting unsaved changes flag:', error);
  }
};

/**
 * Check if there are unsaved changes
 * @returns {boolean} - Whether there are unsaved changes
 */
export const hasUnsavedChanges = () => {
  try {
    const data = sessionStorage.getItem(UNSAVED_CHANGES_KEY);
    return data ? JSON.parse(data) : false;
  } catch (error) {
    console.error('Error checking unsaved changes flag:', error);
    return false;
  }
};

/**
 * Set the last sync time
 * @param {number} timestamp - The timestamp of the last sync
 */
export const setLastSyncTime = (timestamp = Date.now()) => {
  try {
    sessionStorage.setItem(LAST_SYNC_TIME_KEY, JSON.stringify(timestamp));
  } catch (error) {
    console.error('Error setting last sync time:', error);
  }
};

/**
 * Get the last sync time
 * @returns {number|null} - The timestamp of the last sync or null if not found
 */
export const getLastSyncTime = () => {
  try {
    const data = sessionStorage.getItem(LAST_SYNC_TIME_KEY);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Error getting last sync time:', error);
    return null;
  }
};

/**
 * Clear all flow-related session storage
 */
export const clearFlowSessionStorage = () => {
  try {
    // Get all keys
    const keys = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key.startsWith(KEY_PREFIX)) {
        keys.push(key);
      }
    }
    
    // Remove all flow-related keys
    keys.forEach(key => {
      sessionStorage.removeItem(key);
    });
  } catch (error) {
    console.error('Error clearing flow session storage:', error);
  }
};
