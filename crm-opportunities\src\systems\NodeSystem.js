// Modular Node System - All nodes are constructed from property arrays
import { v4 as uuidv4 } from 'uuid';

// Base Node Class
export class BaseNode {
  constructor(type, properties = []) {
    this.id = uuidv4();
    this.type = type;
    this.properties = properties;
    this.position = { x: 0, y: 0 };
    this.created_at = new Date().toISOString();
    this.updated_at = new Date().toISOString();
  }

  // Get property value by key
  getProperty(key) {
    const prop = this.properties.find(p => p.key === key);
    return prop ? prop.value : null;
  }

  // Set property value by key
  setProperty(key, value) {
    const prop = this.properties.find(p => p.key === key);
    if (prop) {
      prop.value = value;
      this.updated_at = new Date().toISOString();
    }
  }

  // Add new property
  addProperty(property) {
    this.properties.push(property);
    this.updated_at = new Date().toISOString();
  }

  // Remove property by key
  removeProperty(key) {
    this.properties = this.properties.filter(p => p.key !== key);
    this.updated_at = new Date().toISOString();
  }

  // Get all properties as key-value object
  getPropertiesAsObject() {
    const obj = {};
    this.properties.forEach(prop => {
      obj[prop.key] = prop.value;
    });
    return obj;
  }

  // Update position
  updatePosition(x, y) {
    this.position = { x, y };
    this.updated_at = new Date().toISOString();
  }

  // Convert to flow node format
  toFlowNode() {
    return {
      id: this.id,
      type: this.type,
      position: this.position,
      data: {
        ...this.getPropertiesAsObject(),
        nodeType: this.type,
        properties: this.properties,
        created_at: this.created_at,
        updated_at: this.updated_at
      }
    };
  }
}

// Property Definition Class
export class PropertyDefinition {
  constructor(key, label, type, defaultValue = null, options = {}) {
    this.key = key;
    this.label = label;
    this.type = type; // 'text', 'number', 'date', 'select', 'boolean', 'currency', 'percentage'
    this.defaultValue = defaultValue;
    this.options = options; // For select types, validation rules, etc.
    this.required = options.required || false;
    this.validation = options.validation || null;
  }
}

// Node Type Registry
export class NodeTypeRegistry {
  constructor() {
    this.nodeTypes = new Map();
    this.registerDefaultTypes();
  }

  // Register a new node type
  registerNodeType(typeName, config) {
    this.nodeTypes.set(typeName, {
      name: typeName,
      label: config.label,
      description: config.description,
      icon: config.icon,
      color: config.color,
      properties: config.properties || [],
      category: config.category || 'general'
    });
  }

  // Get node type configuration
  getNodeType(typeName) {
    return this.nodeTypes.get(typeName);
  }

  // Get all node types
  getAllNodeTypes() {
    return Array.from(this.nodeTypes.values());
  }

  // Get node types by category
  getNodeTypesByCategory(category) {
    return Array.from(this.nodeTypes.values()).filter(type => type.category === category);
  }

  // Create a new node instance
  createNode(typeName, initialData = {}) {
    const nodeType = this.getNodeType(typeName);
    if (!nodeType) {
      throw new Error(`Node type '${typeName}' not found`);
    }

    // Create properties from type definition
    const properties = nodeType.properties.map(propDef => ({
      key: propDef.key,
      label: propDef.label,
      type: propDef.type,
      value: initialData[propDef.key] !== undefined ? initialData[propDef.key] : propDef.defaultValue,
      options: propDef.options,
      required: propDef.required
    }));

    const node = new BaseNode(typeName, properties);
    
    // Set position if provided
    if (initialData.position) {
      node.updatePosition(initialData.position.x, initialData.position.y);
    }

    return node;
  }

  // Register default node types
  registerDefaultTypes() {
    // Opportunity Node
    this.registerNodeType('opportunity', {
      label: 'Opportunity',
      description: 'Sales opportunity with pipeline tracking',
      icon: 'Target',
      color: '#3b82f6',
      category: 'sales',
      properties: [
        new PropertyDefinition('title', 'Title', 'text', '', { required: true }),
        new PropertyDefinition('description', 'Description', 'text', ''),
        new PropertyDefinition('value', 'Value', 'currency', 0),
        new PropertyDefinition('stage', 'Stage', 'select', 'lead', {
          options: ['lead', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']
        }),
        new PropertyDefinition('probability', 'Probability', 'percentage', 0),
        new PropertyDefinition('expected_close_date', 'Expected Close Date', 'date', null),
        new PropertyDefinition('company_id', 'Company ID', 'number', null),
        new PropertyDefinition('contact_id', 'Contact ID', 'number', null)
      ]
    });

    // Company Node
    this.registerNodeType('company', {
      label: 'Company',
      description: 'Company or organization',
      icon: 'Building',
      color: '#059669',
      category: 'contacts',
      properties: [
        new PropertyDefinition('name', 'Company Name', 'text', '', { required: true }),
        new PropertyDefinition('industry', 'Industry', 'text', ''),
        new PropertyDefinition('website', 'Website', 'text', ''),
        new PropertyDefinition('phone', 'Phone', 'text', ''),
        new PropertyDefinition('email', 'Email', 'text', ''),
        new PropertyDefinition('address', 'Address', 'text', ''),
        new PropertyDefinition('size', 'Company Size', 'select', 'small', {
          options: ['startup', 'small', 'medium', 'large', 'enterprise']
        })
      ]
    });

    // Contact Node
    this.registerNodeType('contact', {
      label: 'Contact',
      description: 'Individual contact person',
      icon: 'User',
      color: '#7c3aed',
      category: 'contacts',
      properties: [
        new PropertyDefinition('first_name', 'First Name', 'text', '', { required: true }),
        new PropertyDefinition('last_name', 'Last Name', 'text', '', { required: true }),
        new PropertyDefinition('email', 'Email', 'text', ''),
        new PropertyDefinition('phone', 'Phone', 'text', ''),
        new PropertyDefinition('position', 'Position', 'text', ''),
        new PropertyDefinition('company_id', 'Company ID', 'number', null),
        new PropertyDefinition('linkedin', 'LinkedIn', 'text', '')
      ]
    });

    // Task Node
    this.registerNodeType('task', {
      label: 'Task',
      description: 'Action item or task',
      icon: 'CheckSquare',
      color: '#f59e0b',
      category: 'productivity',
      properties: [
        new PropertyDefinition('title', 'Task Title', 'text', '', { required: true }),
        new PropertyDefinition('description', 'Description', 'text', ''),
        new PropertyDefinition('status', 'Status', 'select', 'pending', {
          options: ['pending', 'in_progress', 'completed', 'cancelled']
        }),
        new PropertyDefinition('priority', 'Priority', 'select', 'medium', {
          options: ['low', 'medium', 'high', 'urgent']
        }),
        new PropertyDefinition('due_date', 'Due Date', 'date', null),
        new PropertyDefinition('assigned_to', 'Assigned To', 'text', ''),
        new PropertyDefinition('related_opportunity_id', 'Related Opportunity', 'number', null)
      ]
    });

    // Meeting Node
    this.registerNodeType('meeting', {
      label: 'Meeting',
      description: 'Meeting or call',
      icon: 'Calendar',
      color: '#ef4444',
      category: 'activities',
      properties: [
        new PropertyDefinition('title', 'Meeting Title', 'text', '', { required: true }),
        new PropertyDefinition('description', 'Description', 'text', ''),
        new PropertyDefinition('date', 'Date & Time', 'date', null),
        new PropertyDefinition('duration', 'Duration (minutes)', 'number', 60),
        new PropertyDefinition('location', 'Location', 'text', ''),
        new PropertyDefinition('attendees', 'Attendees', 'text', ''),
        new PropertyDefinition('meeting_type', 'Type', 'select', 'in_person', {
          options: ['in_person', 'video_call', 'phone_call']
        }),
        new PropertyDefinition('related_opportunity_id', 'Related Opportunity', 'number', null)
      ]
    });

    // Note Node
    this.registerNodeType('note', {
      label: 'Note',
      description: 'Text note or memo',
      icon: 'FileText',
      color: '#6b7280',
      category: 'general',
      properties: [
        new PropertyDefinition('title', 'Note Title', 'text', '', { required: true }),
        new PropertyDefinition('content', 'Content', 'text', ''),
        new PropertyDefinition('tags', 'Tags', 'text', ''),
        new PropertyDefinition('related_opportunity_id', 'Related Opportunity', 'number', null),
        new PropertyDefinition('related_company_id', 'Related Company', 'number', null)
      ]
    });
  }
}

// Global registry instance
export const nodeRegistry = new NodeTypeRegistry();
