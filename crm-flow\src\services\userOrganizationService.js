import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from './firebase';

// Collection name
const ORGANIZATIONS_COLLECTION = 'organizations';

/**
 * Get all organizations created by a user
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - Array of organization objects
 */
export const getUserOrganizations = async (userId) => {
  try {
    // Query for organizations where the user is the owner
    const orgsQuery = query(
      collection(db, ORGANIZATIONS_COLLECTION),
      where('ownerId', '==', userId)
    );

    const snapshot = await getDocs(orgsQuery);
    
    if (snapshot.empty) {
      return [];
    }

    // Map the documents to an array of organization objects
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting user organizations:', error);
    return [];
  }
};

/**
 * Get all organizations where a user is a member
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - Array of organization objects
 */
export const getUserMemberOrganizations = async (userId) => {
  try {
    // Query for organizations where the user is a member
    const orgsQuery = query(
      collection(db, ORGANIZATIONS_COLLECTION),
      where('members', 'array-contains', userId)
    );

    const snapshot = await getDocs(orgsQuery);
    
    if (snapshot.empty) {
      return [];
    }

    // Map the documents to an array of organization objects
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting user member organizations:', error);
    return [];
  }
};
