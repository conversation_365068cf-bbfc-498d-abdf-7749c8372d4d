/**
 * Production Build Script
 * 
 * This script builds the application for production deployment.
 * It sets the NODE_ENV to 'production' and runs the build command.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m'
  }
};

// Log with color
const log = {
  info: (message) => console.log(`${colors.fg.cyan}[INFO]${colors.reset} ${message}`),
  success: (message) => console.log(`${colors.fg.green}[SUCCESS]${colors.reset} ${message}`),
  warning: (message) => console.log(`${colors.fg.yellow}[WARNING]${colors.reset} ${message}`),
  error: (message) => console.log(`${colors.fg.red}[ERROR]${colors.reset} ${message}`),
  step: (step, message) => console.log(`${colors.fg.magenta}[STEP ${step}]${colors.reset} ${message}`)
};

// Print banner
console.log(`
${colors.fg.cyan}${colors.bright}======================================${colors.reset}
${colors.fg.cyan}${colors.bright}  CRM Flow Production Build Script   ${colors.reset}
${colors.fg.cyan}${colors.bright}======================================${colors.reset}
`);

// Start time
const startTime = Date.now();

try {
  // Step 1: Clean previous build
  log.step(1, 'Cleaning previous build...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
    log.success('Previous build cleaned');
  } else {
    log.info('No previous build found');
  }
  
  // Step 2: Check for environment.js file
  log.step(2, 'Checking environment configuration...');
  const envFilePath = path.join('src', 'config', 'environment.js');
  if (!fs.existsSync(envFilePath)) {
    log.error('Environment configuration file not found!');
    log.info('Creating default environment.js file...');
    
    // Create the directory if it doesn't exist
    const dirPath = path.join('src', 'config');
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // Create a basic environment.js file
    const envFileContent = `
/**
 * Environment Configuration
 */
export const environment = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  firebase: {
    apiKey: "AIzaSyD2lO-uO7sngNF0uvbnxZVBc2B7FehApVw",
    authDomain: "janusz-8d5dc.firebaseapp.com",
    projectId: "janusz-8d5dc",
    storageBucket: "janusz-8d5dc.firebasestorage.app",
    messagingSenderId: "360044806489",
    appId: "1:360044806489:web:30e47ff54184393601cc48"
  },
  logging: {
    logLevel: 'warn',
    enableFirebaseLogging: false,
    logPerformance: true
  },
  cache: {
    defaultCacheDuration: 5 * 60 * 1000,
    extendedCacheDuration: 30 * 60 * 1000,
    usePersistentCache: true,
    maxCacheSize: 'unlimited'
  },
  app: {
    enableOfflinePersistence: true
  }
};
export default environment;
    `;
    
    fs.writeFileSync(envFilePath, envFileContent.trim());
    log.success('Created default environment.js file');
  } else {
    log.success('Environment configuration found');
  }
  
  // Step 3: Install dependencies if needed
  log.step(3, 'Checking dependencies...');
  if (!fs.existsSync('node_modules')) {
    log.info('Node modules not found, installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    log.success('Dependencies installed');
  } else {
    log.success('Dependencies already installed');
  }
  
  // Step 4: Build the application
  log.step(4, 'Building for production...');
  execSync('SET NODE_ENV=production && npm run build', { stdio: 'inherit' });
  log.success('Production build completed');
  
  // Step 5: Verify build
  log.step(5, 'Verifying build...');
  if (!fs.existsSync('dist')) {
    throw new Error('Build directory not found after build process');
  }
  
  const indexHtml = path.join('dist', 'index.html');
  if (!fs.existsSync(indexHtml)) {
    throw new Error('index.html not found in build directory');
  }
  
  log.success('Build verification passed');
  
  // Calculate build time
  const buildTime = ((Date.now() - startTime) / 1000).toFixed(2);
  log.info(`Build completed in ${buildTime} seconds`);
  
  // Print success message
  console.log(`
${colors.fg.green}${colors.bright}======================================${colors.reset}
${colors.fg.green}${colors.bright}  Production Build Successful!       ${colors.reset}
${colors.fg.green}${colors.bright}======================================${colors.reset}

${colors.fg.white}The production build is available in the ${colors.fg.cyan}'dist'${colors.fg.white} directory.${colors.reset}

${colors.fg.white}To serve the production build locally:${colors.reset}
${colors.fg.yellow}npm run serve${colors.reset}

${colors.fg.white}To deploy to a hosting service, upload the contents of the ${colors.fg.cyan}'dist'${colors.fg.white} directory.${colors.reset}
`);
  
} catch (error) {
  log.error(`Build failed: ${error.message}`);
  console.error(error);
  process.exit(1);
}
