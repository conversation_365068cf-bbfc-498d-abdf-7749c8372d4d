import { useState, useEffect } from 'react';
import { collection, addDoc, getDocs, doc, updateDoc } from 'firebase/firestore';
import { addUserDocument } from '../../services/dataAccessService';
import { db } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';
import 'bootstrap/dist/css/bootstrap.min.css';

const TaskForm = ({ onClose, onSuccess, taskData = null, isEditing = false }) => {
  const { currentUser, organization } = useAuth();
  const [formData, setFormData] = useState({
    title: '',
    dueDate: '',
    status: 'Not Started',
    priority: 'Medium',
    assignedTo: '',
    relatedTo: {
      type: '',
      id: ''
    },
    description: ''
  });
  const [opportunities, setOpportunities] = useState([]);
  const [contacts, setContacts] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [relatedItems, setRelatedItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Initialize form with task data if editing
  useEffect(() => {
    if (isEditing && taskData) {
      // Format the date for the date input (YYYY-MM-DD)
      let formattedDate = '';
      if (taskData.dueDate) {
        const date = new Date(taskData.dueDate);
        formattedDate = date.toISOString().split('T')[0];
      }

      setFormData({
        id: taskData.id,
        title: taskData.title || '',
        dueDate: formattedDate,
        status: taskData.status || 'Not Started',
        priority: taskData.priority || 'Medium',
        assignedTo: taskData.assignedTo || '',
        relatedTo: taskData.relatedTo || { type: '', id: '' },
        description: taskData.description || ''
      });
    }
  }, [isEditing, taskData]);

  // Fetch related entities for dropdowns
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch opportunities
        const opportunitiesSnapshot = await getDocs(collection(db, 'opportunities'));
        const opportunitiesData = opportunitiesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          entityType: 'opportunity'
        }));
        setOpportunities(opportunitiesData);

        // Fetch contacts
        const contactsSnapshot = await getDocs(collection(db, 'contacts'));
        const contactsData = contactsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          entityType: 'contact'
        }));
        setContacts(contactsData);

        // Fetch companies
        const companiesSnapshot = await getDocs(collection(db, 'companies'));
        const companiesData = companiesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          entityType: 'company'
        }));
        setCompanies(companiesData);

        // Combine all for the related to dropdown
        setRelatedItems([
          ...opportunitiesData.map(item => ({
            id: item.id,
            name: `${item.name} (Opportunity)`,
            type: 'opportunity'
          })),
          ...contactsData.map(item => ({
            id: item.id,
            name: `${item.name} (Contact)`,
            type: 'contact'
          })),
          ...companiesData.map(item => ({
            id: item.id,
            name: `${item.name} (Company)`,
            type: 'company'
          }))
        ]);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load data. Please try again.');
      }
    };

    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === 'relatedEntity') {
      if (value) {
        const [type, id] = value.split('|');
        setFormData({
          ...formData,
          relatedTo: {
            type,
            id
          }
        });
      } else {
        setFormData({
          ...formData,
          relatedTo: {
            type: '',
            id: ''
          }
        });
      }
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validate form
      if (!formData.title || !formData.dueDate) {
        throw new Error('Please fill in all required fields');
      }

      // Prepare task data
      const taskData = {
        ...formData,
        assignedTo: formData.assignedTo || currentUser.uid,
        updatedAt: new Date()
      };

      // Add organization ID if available
      if (organization?.id) {
        taskData.organizationId = organization.id;
      }

      let docId;

      if (isEditing && taskData.id) {
        // Update existing task
        const taskId = taskData.id;
        // Remove id from the data object before updating
        const { id, ...dataToUpdate } = taskData;
        const taskRef = doc(db, 'tasks', taskId);
        await updateDoc(taskRef, dataToUpdate);
        docId = taskId;
      } else {
        // Create new task using the dataAccessService
        console.log('Creating new task with data:', taskData);

        // Use the addUserDocument function to add the task with proper context
        const result = await addUserDocument('tasks', taskData, currentUser.uid, organization?.id);

        if (result.success) {
          docId = result.id;
          console.log('Task created with ID:', docId);
        } else {
          throw new Error(result.error || 'Failed to create task');
        }
      }

      // Show success message
      setSuccess(true);

      if (!isEditing) {
        // Only reset form for new tasks
        setFormData({
          title: '',
          dueDate: '',
          status: 'Not Started',
          priority: 'Medium',
          assignedTo: '',
          relatedTo: {
            type: '',
            id: ''
          },
          description: ''
        });
      }

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(docId, taskData);
      }

      // Close modal after 1.5 seconds if onClose is provided
      if (onClose) {
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('Error adding task:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Format date as YYYY-MM-DD for the date input
  const formatDateForInput = (date) => {
    const d = new Date(date);
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${d.getFullYear()}-${month}-${day}`;
  };

  // Set default due date to tomorrow for new tasks
  useEffect(() => {
    // Only set default date if not editing
    if (!isEditing) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      setFormData(prev => ({
        ...prev,
        dueDate: formatDateForInput(tomorrow)
      }));
    }
  }, [isEditing]);

  return (
    <div className="card shadow border-0">
      <div className="card-header bg-warning text-dark">
        <h5 className="mb-0">{isEditing ? 'Edit Task' : 'Add New Task'}</h5>
      </div>
      <div className="card-body">
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success" role="alert">
            Task {isEditing ? 'updated' : 'created'} successfully!
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label htmlFor="title" className="form-label">Task Title *</label>
            <input
              type="text"
              className="form-control"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
            />
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="dueDate" className="form-label">Due Date *</label>
              <input
                type="date"
                className="form-control"
                id="dueDate"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleChange}
                required
              />
            </div>

            <div className="col-md-6">
              <label htmlFor="status" className="form-label">Status</label>
              <select
                className="form-select"
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
              >
                <option value="Not Started">Not Started</option>
                <option value="In Progress">In Progress</option>
                <option value="Completed">Completed</option>
                <option value="Deferred">Deferred</option>
              </select>
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="priority" className="form-label">Priority</label>
              <select
                className="form-select"
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleChange}
              >
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
              </select>
            </div>

            <div className="col-md-6">
              <label htmlFor="relatedEntity" className="form-label">Related To</label>
              <select
                className="form-select"
                id="relatedEntity"
                name="relatedEntity"
                value={formData.relatedTo.type && formData.relatedTo.id ? `${formData.relatedTo.type}|${formData.relatedTo.id}` : ''}
                onChange={handleChange}
              >
                <option value="">None</option>
                <optgroup label="Opportunities">
                  {opportunities.map(opp => (
                    <option key={`opp-${opp.id}`} value={`opportunity|${opp.id}`}>
                      {opp.name}
                    </option>
                  ))}
                </optgroup>
                <optgroup label="Contacts">
                  {contacts.map(contact => (
                    <option key={`contact-${contact.id}`} value={`contact|${contact.id}`}>
                      {contact.name}
                    </option>
                  ))}
                </optgroup>
                <optgroup label="Companies">
                  {companies.map(company => (
                    <option key={`company-${company.id}`} value={`company|${company.id}`}>
                      {company.name}
                    </option>
                  ))}
                </optgroup>
              </select>
            </div>
          </div>

          <div className="mb-3">
            <label htmlFor="description" className="form-label">Description</label>
            <textarea
              className="form-control"
              id="description"
              name="description"
              rows="3"
              value={formData.description}
              onChange={handleChange}
            ></textarea>
          </div>

          <div className="d-flex justify-content-end gap-2 mt-4">
            {onClose && (
              <button
                type="button"
                className="btn btn-outline-secondary"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              className="btn btn-warning"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Saving...
                </>
              ) : isEditing ? 'Update Task' : 'Save Task'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskForm;
