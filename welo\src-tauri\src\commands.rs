use crate::database::{Database, Server, ServerHistory, ServerWithStatus};
use crate::quakeworld::{validate_server_address, QuakeWorldClient, QueryResult};
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use tauri::State;

#[derive(Debug, Serialize, Deserialize)]
pub struct AddServerRequest {
    pub name: String,
    pub address: String,
    pub port: u16,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AddServerResponse {
    pub message: String,
    pub server: Server,
    pub initial_status: QueryResult,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TestServerRequest {
    pub address: String,
    pub port: u16,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateServerNameRequest {
    pub name: String,
}

pub type DatabaseState = Mutex<Database>;

#[tauri::command]
pub async fn get_servers(db: State<'_, DatabaseState>) -> Result<Vec<ServerWithStatus>, String> {
    let db = db
        .lock()
        .map_err(|e| format!("Database lock error: {}", e))?;
    db.get_servers_with_status()
        .map_err(|e| format!("Database error: {}", e))
}

#[tauri::command]
pub async fn add_server(
    db: State<'_, DatabaseState>,
    name: String,
    address: String,
    port: u16,
) -> Result<AddServerResponse, String> {
    // Validate input
    if name.trim().is_empty() {
        return Err("Server name cannot be empty".to_string());
    }

    if address.trim().is_empty() {
        return Err("Server address cannot be empty".to_string());
    }

    validate_server_address(&address, port).map_err(|e| format!("Invalid address: {}", e))?;

    // Add server to database
    let server_id = {
        let db = db
            .lock()
            .map_err(|e| format!("Database lock error: {}", e))?;
        db.add_server(&name, &address, port as i32)
            .map_err(|e| format!("Failed to add server: {}", e))?
    };

    // Get the newly created server
    let server = {
        let db = db
            .lock()
            .map_err(|e| format!("Database lock error: {}", e))?;
        db.get_server_by_id(server_id)
            .map_err(|e| format!("Database error: {}", e))?
            .ok_or("Server not found after creation")?
    };

    // Query the server immediately
    let client = QuakeWorldClient::new()
        .map_err(|e| format!("Failed to create QuakeWorld client: {}", e))?;

    let query_result = client.query_server(&address, port);
    let initial_status = match query_result {
        Ok(result) => {
            // Update database with result
            let db = db
                .lock()
                .map_err(|e| format!("Database lock error: {}", e))?;
            let _ = db.update_server_status(server_id, result.is_online);
            let _ = db.add_server_history(
                server_id,
                result.is_online,
                result.ping,
                result.player_count,
                result.max_players,
                result.map_name.as_deref(),
                result.game_mode.as_deref(),
                result
                    .server_info
                    .as_ref()
                    .map(|info| serde_json::to_string(info).unwrap_or_default())
                    .as_deref(),
            );
            result
        }
        Err(e) => QueryResult {
            success: false,
            is_online: false,
            ping: None,
            error: Some(format!("Query failed: {}", e)),
            server_info: None,
            player_count: None,
            max_players: None,
            map_name: None,
            game_mode: None,
            hostname: None,
            players: Vec::new(),
        },
    };

    Ok(AddServerResponse {
        message: "Server added successfully".to_string(),
        server,
        initial_status,
    })
}

#[tauri::command]
pub async fn delete_server(db: State<'_, DatabaseState>, server_id: i64) -> Result<String, String> {
    let db = db
        .lock()
        .map_err(|e| format!("Database lock error: {}", e))?;
    let deleted = db
        .delete_server(server_id)
        .map_err(|e| format!("Database error: {}", e))?;

    if deleted {
        Ok("Server deleted successfully".to_string())
    } else {
        Err("Server not found".to_string())
    }
}

#[tauri::command]
pub async fn update_server_name(
    db: State<'_, DatabaseState>,
    server_id: i64,
    name: String,
) -> Result<String, String> {
    if name.trim().is_empty() {
        return Err("Server name cannot be empty".to_string());
    }

    let db = db
        .lock()
        .map_err(|e| format!("Database lock error: {}", e))?;
    let updated = db
        .update_server_name(server_id, &name)
        .map_err(|e| format!("Database error: {}", e))?;

    if updated {
        Ok("Server name updated successfully".to_string())
    } else {
        Err("Server not found".to_string())
    }
}

#[tauri::command]
pub async fn query_server(
    db: State<'_, DatabaseState>,
    server_id: i64,
) -> Result<QueryResult, String> {
    // Get server info
    let server = {
        let db = db
            .lock()
            .map_err(|e| format!("Database lock error: {}", e))?;
        db.get_server_by_id(server_id)
            .map_err(|e| format!("Database error: {}", e))?
            .ok_or("Server not found")?
    };

    // Query the server
    let client = QuakeWorldClient::new()
        .map_err(|e| format!("Failed to create QuakeWorld client: {}", e))?;

    let result = client.query_server(&server.address, server.port as u16);

    match result {
        Ok(query_result) => {
            // Update database with result
            let db = db
                .lock()
                .map_err(|e| format!("Database lock error: {}", e))?;
            let _ = db.update_server_status(server_id, query_result.is_online);
            let _ = db.add_server_history(
                server_id,
                query_result.is_online,
                query_result.ping,
                query_result.player_count,
                query_result.max_players,
                query_result.map_name.as_deref(),
                query_result.game_mode.as_deref(),
                query_result
                    .server_info
                    .as_ref()
                    .map(|info| serde_json::to_string(info).unwrap_or_default())
                    .as_deref(),
            );
            Ok(query_result)
        }
        Err(e) => {
            let error_result = QueryResult {
                success: false,
                is_online: false,
                ping: None,
                error: Some(format!("Query failed: {}", e)),
                server_info: None,
                player_count: None,
                max_players: None,
                map_name: None,
                game_mode: None,
                hostname: None,
                players: Vec::new(),
            };

            // Still update database to mark as offline
            let db = db
                .lock()
                .map_err(|e| format!("Database lock error: {}", e))?;
            let _ = db.update_server_status(server_id, false);
            let _ = db.add_server_history(server_id, false, None, None, None, None, None, None);

            Ok(error_result)
        }
    }
}

#[tauri::command]
pub async fn query_all_servers(db: State<'_, DatabaseState>) -> Result<Vec<QueryResult>, String> {
    // Get all servers
    let servers = {
        let db = db
            .lock()
            .map_err(|e| format!("Database lock error: {}", e))?;
        db.get_all_servers()
            .map_err(|e| format!("Database error: {}", e))?
    };

    let mut results = Vec::new();
    let client = QuakeWorldClient::new()
        .map_err(|e| format!("Failed to create QuakeWorld client: {}", e))?;

    for server in servers {
        let result = client.query_server(&server.address, server.port as u16);

        let query_result = match result {
            Ok(mut query_result) => {
                // Update database with result
                let db = db
                    .lock()
                    .map_err(|e| format!("Database lock error: {}", e))?;
                let _ = db.update_server_status(server.id, query_result.is_online);
                let _ = db.add_server_history(
                    server.id,
                    query_result.is_online,
                    query_result.ping,
                    query_result.player_count,
                    query_result.max_players,
                    query_result.map_name.as_deref(),
                    query_result.game_mode.as_deref(),
                    query_result
                        .server_info
                        .as_ref()
                        .map(|info| serde_json::to_string(info).unwrap_or_default())
                        .as_deref(),
                );
                query_result
            }
            Err(e) => {
                let error_result = QueryResult {
                    success: false,
                    is_online: false,
                    ping: None,
                    error: Some(format!("Query failed: {}", e)),
                    server_info: None,
                    player_count: None,
                    max_players: None,
                    map_name: None,
                    game_mode: None,
                    hostname: None,
                    players: Vec::new(),
                };

                // Update database to mark as offline
                let db = db
                    .lock()
                    .map_err(|e| format!("Database lock error: {}", e))?;
                let _ = db.update_server_status(server.id, false);
                let _ = db.add_server_history(server.id, false, None, None, None, None, None, None);

                error_result
            }
        };

        results.push(query_result);

        // Small delay between queries
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }

    Ok(results)
}

#[tauri::command]
pub async fn get_server_history(
    db: State<'_, DatabaseState>,
    server_id: i64,
    limit: Option<i32>,
) -> Result<Vec<ServerHistory>, String> {
    let db = db
        .lock()
        .map_err(|e| format!("Database lock error: {}", e))?;
    db.get_server_history(server_id, limit.unwrap_or(50))
        .map_err(|e| format!("Database error: {}", e))
}

#[tauri::command]
pub async fn test_server(address: String, port: u16) -> Result<QueryResult, String> {
    validate_server_address(&address, port).map_err(|e| format!("Invalid address: {}", e))?;

    let client = QuakeWorldClient::new()
        .map_err(|e| format!("Failed to create QuakeWorld client: {}", e))?;

    client
        .query_server(&address, port)
        .map_err(|e| format!("Query failed: {}", e))
}
