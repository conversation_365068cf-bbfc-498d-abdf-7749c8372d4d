/**
 * Debounce utility functions
 * These functions help reduce the number of Firebase operations
 */

// Store debounce timers
const debounceTimers = new Map();

/**
 * Debounce a function call
 * @param {Function} func - The function to debounce
 * @param {string} key - A unique key to identify this debounce operation
 * @param {number} delay - Delay in milliseconds
 * @param {Array} args - Arguments to pass to the function
 */
export const debounce = (func, key, delay, ...args) => {
  // Clear any existing timer for this key
  if (debounceTimers.has(key)) {
    clearTimeout(debounceTimers.get(key));
  }
  
  // Set a new timer
  const timerId = setTimeout(() => {
    func(...args);
    debounceTimers.delete(key);
  }, delay);
  
  // Store the timer ID
  debounceTimers.set(key, timerId);
};

/**
 * Cancel a debounced operation
 * @param {string} key - The key of the operation to cancel
 */
export const cancelDebounce = (key) => {
  if (debounceTimers.has(key)) {
    clearTimeout(debounceTimers.get(key));
    debounceTimers.delete(key);
  }
};

/**
 * Execute a debounced operation immediately and cancel the timer
 * @param {Function} func - The function to execute
 * @param {string} key - The key of the operation
 * @param {Array} args - Arguments to pass to the function
 */
export const executeNow = (func, key, ...args) => {
  cancelDebounce(key);
  func(...args);
};

/**
 * Debounce a Firebase write operation
 * @param {Function} writeFunc - The Firebase write function
 * @param {string} collectionName - The collection name
 * @param {string} documentId - The document ID
 * @param {Object} data - The data to write
 * @param {number} delay - Delay in milliseconds (default: 2000ms)
 */
export const debounceFirebaseWrite = (writeFunc, collectionName, documentId, data, delay = 2000) => {
  const key = `${collectionName}:${documentId}`;
  debounce(writeFunc, key, delay, collectionName, documentId, data);
};

/**
 * Execute a Firebase write immediately
 * @param {Function} writeFunc - The Firebase write function
 * @param {string} collectionName - The collection name
 * @param {string} documentId - The document ID
 * @param {Object} data - The data to write
 */
export const executeFirebaseWriteNow = (writeFunc, collectionName, documentId, data) => {
  const key = `${collectionName}:${documentId}`;
  executeNow(writeFunc, key, collectionName, documentId, data);
};
