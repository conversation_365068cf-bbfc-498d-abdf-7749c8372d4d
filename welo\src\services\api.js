import { invoke } from '@tauri-apps/api/core';

// Fetch all servers
export const fetchServers = async () => {
  return await invoke('get_servers');
};

// Add a new server
export const addServer = async (serverData) => {
  return await invoke('add_server', {
    name: serverData.name,
    address: serverData.address,
    port: serverData.port,
  });
};

// Delete a server
export const deleteServer = async (serverId) => {
  return await invoke('delete_server', { server_id: serverId });
};

// Update server name
export const updateServerName = async (serverId, newName) => {
  return await invoke('update_server_name', {
    server_id: serverId,
    name: newName
  });
};

// Query a specific server
export const queryServer = async (serverId) => {
  return await invoke('query_server', { server_id: serverId });
};

// Query all servers
export const queryAllServers = async () => {
  return await invoke('query_all_servers');
};

// Get server history
export const getServerHistory = async (serverId, limit = 50) => {
  return await invoke('get_server_history', { server_id: serverId, limit });
};

// Test server connection without saving
export const testServer = async (address, port) => {
  return await invoke('test_server', {
    address,
    port
  });
};
