import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import OpportunityImporter from '../components/Importers/OpportunityImporter';
import MainLayout from '../components/Layout/MainLayout';
import 'bootstrap/dist/css/bootstrap.min.css';

const ImportOpportunitiesPage = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [importComplete, setImportComplete] = useState(false);
  const [importResults, setImportResults] = useState(null);

  // Handle import completion
  const handleImportComplete = (results) => {
    setImportResults(results);
    setImportComplete(true);
  };

  // Navigate to opportunities page
  const goToOpportunities = () => {
    navigate('/opportunities');
  };

  // Handle new import
  const handleNewImport = () => {
    setImportComplete(false);
    setImportResults(null);
  };

  return (
    <MainLayout>
      <div className="container py-4">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h2 className="mb-0">Import Opportunities</h2>
          <button
            className="btn btn-outline-primary"
            onClick={goToOpportunities}
          >
            <i className="bi bi-arrow-left me-2"></i>
            Back to Opportunities
          </button>
        </div>

        {importComplete && importResults ? (
          <div className="card">
            <div className="card-header bg-success text-white">
              <h5 className="mb-0">Import Completed</h5>
            </div>
            <div className="card-body">
              <div className="text-center mb-4">
                <div className="display-1 text-success mb-3">
                  <i className="bi bi-check-circle"></i>
                </div>
                <h4>Successfully imported {importResults.success} opportunities</h4>
                {importResults.failed > 0 && (
                  <p className="text-danger">
                    Failed to import {importResults.failed} opportunities
                  </p>
                )}
              </div>
              <div className="d-flex justify-content-center">
                <button
                  className="btn btn-primary me-3"
                  onClick={goToOpportunities}
                >
                  View Opportunities
                </button>
                <button
                  className="btn btn-outline-secondary"
                  onClick={handleNewImport}
                >
                  Import More
                </button>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="alert alert-info mb-4">
              <h5><i className="bi bi-info-circle me-2"></i>Import Instructions</h5>
              <p>
                You can import opportunities from JSON, CSV, table format, or markdown table.
                The importer will automatically detect the format and parse the data.
              </p>
              <p>
                Required fields: Name, Contact Info, Location, and Issue (which will be mapped to the description field).
              </p>
              <div className="mt-3">
                <h6>Example JSON Format:</h6>
                <pre className="bg-light p-2 rounded small" style={{ maxHeight: '150px', overflow: 'auto' }}>
                  {`[
  {
    "Name": "John Doe",
    "Contact Info": "+1234567890",
    "Location": "New York",
    "Issue": "TV repair, 55 inches"
  },
  {
    "Name": "Jane Smith",
    "Contact Info": "<EMAIL>",
    "Location": "Chicago",
    "Issue": "Samsung TV repair, sound issues"
  }
]`}
                </pre>
              </div>
            </div>

            <OpportunityImporter onImportComplete={handleImportComplete} />
          </>
        )}
      </div>
    </MainLayout>
  );
};

export default ImportOpportunitiesPage;
