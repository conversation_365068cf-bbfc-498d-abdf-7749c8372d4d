use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::{SocketAddr, UdpSocket};
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Player {
    pub name: String,
    pub frags: i32,
    pub time: i32,
    pub ping: i32,
    pub team: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerInfo {
    pub hostname: Option<String>,
    pub map: Option<String>,
    pub gamedir: Option<String>,
    pub maxclients: Option<i32>,
    pub version: Option<String>,
    pub players: Vec<Player>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QueryResult {
    pub success: bool,
    pub is_online: bool,
    pub ping: Option<i32>,
    pub error: Option<String>,
    pub server_info: Option<ServerInfo>,
    pub player_count: Option<i32>,
    pub max_players: Option<i32>,
    pub map_name: Option<String>,
    pub game_mode: Option<String>,
    pub hostname: Option<String>,
    pub players: Vec<Player>,
}

pub struct QuakeWorldClient {
    socket: UdpSocket,
}

impl QuakeWorldClient {
    pub fn new() -> Result<Self> {
        let socket = UdpSocket::bind("0.0.0.0:0")?;
        socket.set_read_timeout(Some(Duration::from_secs(5)))?;
        socket.set_write_timeout(Some(Duration::from_secs(5)))?;
        
        Ok(QuakeWorldClient { socket })
    }

    pub fn query_server(&self, address: &str, port: u16) -> Result<QueryResult> {
        let start_time = Instant::now();
        
        // Create the server address
        let server_addr: SocketAddr = format!("{}:{}", address, port).parse()
            .map_err(|_| anyhow!("Invalid server address"))?;

        // QuakeWorld status query command
        // The command is: \xff\xff\xff\xffstatus 31\n
        let query = b"\xff\xff\xff\xffstatus 31\n";
        
        // Send the query
        self.socket.send_to(query, server_addr)?;
        
        // Read the response
        let mut buffer = [0u8; 4096];
        match self.socket.recv_from(&mut buffer) {
            Ok((size, _)) => {
                let ping = start_time.elapsed().as_millis() as i32;
                let response = String::from_utf8_lossy(&buffer[..size]);
                
                match self.parse_status_response(&response) {
                    Ok(server_info) => {
                        Ok(QueryResult {
                            success: true,
                            is_online: true,
                            ping: Some(ping),
                            error: None,
                            player_count: Some(server_info.players.len() as i32),
                            max_players: server_info.maxclients,
                            map_name: server_info.map.clone(),
                            game_mode: server_info.gamedir.clone(),
                            hostname: server_info.hostname.clone(),
                            players: server_info.players.clone(),
                            server_info: Some(server_info),
                        })
                    }
                    Err(e) => {
                        Ok(QueryResult {
                            success: false,
                            is_online: false,
                            ping: Some(ping),
                            error: Some(format!("Failed to parse response: {}", e)),
                            server_info: None,
                            player_count: None,
                            max_players: None,
                            map_name: None,
                            game_mode: None,
                            hostname: None,
                            players: Vec::new(),
                        })
                    }
                }
            }
            Err(e) => {
                Ok(QueryResult {
                    success: false,
                    is_online: false,
                    ping: None,
                    error: Some(format!("Network error: {}", e)),
                    server_info: None,
                    player_count: None,
                    max_players: None,
                    map_name: None,
                    game_mode: None,
                    hostname: None,
                    players: Vec::new(),
                })
            }
        }
    }

    fn parse_status_response(&self, response: &str) -> Result<ServerInfo> {
        // QuakeWorld status response format:
        // \xff\xff\xff\xffn\server_info\nplayer_info\nplayer_info\n...
        
        if !response.starts_with("\u{ff}\u{ff}\u{ff}\u{ff}n") {
            return Err(anyhow!("Invalid response format"));
        }

        let content = &response[5..]; // Skip the header
        let lines: Vec<&str> = content.lines().collect();
        
        if lines.is_empty() {
            return Err(anyhow!("Empty response"));
        }

        // Parse server info (first line)
        let server_vars = self.parse_info_string(lines[0])?;
        
        // Parse players (remaining lines)
        let mut players = Vec::new();
        for line in &lines[1..] {
            if !line.trim().is_empty() {
                if let Ok(player) = self.parse_player_line(line) {
                    players.push(player);
                }
            }
        }

        Ok(ServerInfo {
            hostname: server_vars.get("hostname").cloned(),
            map: server_vars.get("map").cloned(),
            gamedir: server_vars.get("gamedir").cloned(),
            maxclients: server_vars.get("maxclients")
                .and_then(|s| s.parse().ok()),
            version: server_vars.get("version").cloned(),
            players,
        })
    }

    fn parse_info_string(&self, info_string: &str) -> Result<HashMap<String, String>> {
        let mut vars = HashMap::new();
        
        // Info string format: \key1\value1\key2\value2\...
        let parts: Vec<&str> = info_string.split('\\').collect();
        
        // Skip the first empty part and process pairs
        for chunk in parts[1..].chunks(2) {
            if chunk.len() == 2 {
                vars.insert(chunk[0].to_string(), chunk[1].to_string());
            }
        }
        
        Ok(vars)
    }

    fn parse_player_line(&self, line: &str) -> Result<Player> {
        // Player line format: frags ping time name
        // Example: "25 50 1234 PlayerName"
        
        let parts: Vec<&str> = line.trim().split_whitespace().collect();
        if parts.len() < 4 {
            return Err(anyhow!("Invalid player line format"));
        }

        let frags = parts[0].parse().unwrap_or(0);
        let ping = parts[1].parse().unwrap_or(0);
        let time = parts[2].parse().unwrap_or(0);
        let name = parts[3..].join(" ");

        Ok(Player {
            name,
            frags,
            time,
            ping,
            team: None, // QuakeWorld doesn't always have team info in status
        })
    }

    pub fn ping_server(&self, address: &str, port: u16) -> Result<i32> {
        let start_time = Instant::now();
        
        let server_addr: SocketAddr = format!("{}:{}", address, port).parse()
            .map_err(|_| anyhow!("Invalid server address"))?;

        // Simple ping query
        let query = b"\xff\xff\xff\xffping\n";
        
        self.socket.send_to(query, server_addr)?;
        
        let mut buffer = [0u8; 64];
        match self.socket.recv_from(&mut buffer) {
            Ok(_) => {
                let ping = start_time.elapsed().as_millis() as i32;
                Ok(ping)
            }
            Err(e) => Err(anyhow!("Ping failed: {}", e)),
        }
    }
}

pub fn validate_server_address(address: &str, port: u16) -> Result<()> {
    if address.is_empty() {
        return Err(anyhow!("Address cannot be empty"));
    }
    
    if port == 0 {
        return Err(anyhow!("Port cannot be 0"));
    }
    
    // Try to parse as socket address to validate
    let _: SocketAddr = format!("{}:{}", address, port).parse()
        .map_err(|_| anyhow!("Invalid address format"))?;
    
    Ok(())
}
