/**
 * Environment Configuration
 * 
 * This file contains environment-specific configuration settings.
 * It helps distinguish between development and production environments.
 */

// Determine the current environment
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Firebase project configuration
const firebaseConfig = {
  apiKey: "AIzaSyD2lO-uO7sngNF0uvbnxZVBc2B7FehApVw",
  authDomain: "janusz-8d5dc.firebaseapp.com",
  projectId: "janusz-8d5dc",
  storageBucket: "janusz-8d5dc.firebasestorage.app",
  messagingSenderId: "360044806489",
  appId: "1:360044806489:web:30e47ff54184393601cc48"
};

// Logging configuration
const loggingConfig = {
  // In development, log everything
  // In production, only log errors and warnings
  logLevel: isDevelopment ? 'debug' : 'warn',
  
  // Enable detailed Firebase logging in development only
  enableFirebaseLogging: isDevelopment,
  
  // Whether to log performance metrics
  logPerformance: isProduction
};

// Cache configuration
const cacheConfig = {
  // Default cache duration in milliseconds
  defaultCacheDuration: 5 * 60 * 1000, // 5 minutes
  
  // Extended cache duration for rarely changing data
  extendedCacheDuration: 30 * 60 * 1000, // 30 minutes
  
  // Whether to use persistent cache
  usePersistentCache: true,
  
  // Maximum cache size (unlimited in production)
  maxCacheSize: isProduction ? 'unlimited' : 50 * 1024 * 1024 // 50MB in development
};

// Performance configuration
const performanceConfig = {
  // Whether to enable performance monitoring
  enableMonitoring: isProduction,
  
  // Trace sample rate (percentage of traces to collect)
  traceSampleRate: isProduction ? 0.1 : 1.0,
  
  // Whether to collect network timing data
  collectNetworkTiming: isProduction
};

// Export the configuration
export const environment = {
  isDevelopment,
  isProduction,
  firebase: firebaseConfig,
  logging: loggingConfig,
  cache: cacheConfig,
  performance: performanceConfig,
  
  // App-specific configuration
  app: {
    name: 'CRM Flow',
    version: '1.0.0',
    
    // Default debounce delay for Firebase writes (ms)
    defaultDebounceDelay: 2000,
    
    // Maximum batch size for Firestore operations
    maxBatchSize: 450,
    
    // Whether to enable offline persistence
    enableOfflinePersistence: true
  }
};

export default environment;
