[package]
name = "audioapp2"
version = "0.1.0"
edition = "2021"

# Set the default for Windows to use the windows subsystem (no console window)
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "windef", "minwindef", "shellapi"] }

[dependencies]
eframe = { version = "0.26.0", features = ["persistence", "accesskit"] }
egui = "0.26.0"
cpal = "0.15.2"
windows-volume-control = "0.1.1"
windows = { version = "0.52.0", features = ["Win32_Media_Audio", "Win32_System_Com", "Win32_Foundation", "Win32_UI_WindowsAndMessaging"] }
raw-window-handle = "0.5.0"
winapi = { version = "0.3.9", features = ["winuser", "windef", "minwindef", "shellapi", "combaseapi", "objbase", "mmdeviceapi", "propkeydef", "winerror", "guiddef", "wtypes"] }

# This tells <PERSON><PERSON> to build a Windows GUI app (no console window)
[target.'cfg(windows)'.build-dependencies]

# Specify that this is a GUI application on Windows
[package.metadata.cargo-feature-combinations]

[package.metadata.windows]
subsystem = "windows"
