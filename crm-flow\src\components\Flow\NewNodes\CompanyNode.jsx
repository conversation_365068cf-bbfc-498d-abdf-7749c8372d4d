import { memo } from 'react';
import { <PERSON>le, Position } from '@xyflow/react';
import NodeMenu from './NodeMenu';

const CompanyNode = ({ data, selected }) => {
  const formatDate = (date) => {
    if (!date) return 'Unknown';
    return typeof date === 'object' && date.toDate
      ? date.toDate().toLocaleDateString()
      : new Date(date).toLocaleDateString();
  };

  // Handle node actions
  const handleEdit = () => {
    if (data.onEdit) {
      // Get the node ID (format: "company-id")
      const nodeId = data.id;

      // Extract the actual ID from the node ID
      const parts = nodeId.split('-');
      const actualId = parts.slice(1).join('-'); // Join in case ID contains hyphens

      console.log(`Editing company node: type=company, id=${actualId}`);

      // Create a custom event with the correct format
      const event = new CustomEvent('editNode', {
        detail: { type: 'company', id: actualId }
      });
      window.dispatchEvent(event);
    }
  };

  const handleDelete = () => {
    if (data.onDelete) data.onDelete(data.id);
  };

  const handleArchive = () => {
    if (data.onArchive) data.onArchive(data.id);
  };

  const handleRestore = () => {
    if (data.onRestore) data.onRestore(data.id);
  };

  return (
    <div className={`node-card node-company ${data.archived ? 'node-archived' : ''}`}>
      <NodeMenu
        onEdit={handleEdit}
        onDelete={handleDelete}
        onArchive={handleArchive}
        isArchived={data.archived}
        onRestore={handleRestore}
        nodeType="company"
      />
      <div className="node-header">
        <h6 className="node-title">Company</h6>
      </div>
      <div className="node-body">
        <h5 className="node-name">{data.name}</h5>
        <p className="node-detail">Industry: {data.industry || 'N/A'}</p>
        <p className="node-detail">Size: {data.size || 'N/A'}</p>
        <p className="node-detail">Website: {data.website || 'N/A'}</p>
        <p className="node-date">Created: {formatDate(data.createdAt)}</p>
      </div>
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#555' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: '#555' }}
      />
    </div>
  );
};

export default memo(CompanyNode);
