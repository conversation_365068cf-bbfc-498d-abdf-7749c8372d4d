import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create database connection
const db = new Database(path.join(__dirname, 'quakeworld.db'));

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Create tables
const createTables = () => {
  // Servers table
  const createServersTable = db.prepare(`
    CREATE TABLE IF NOT EXISTS servers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      address TEXT NOT NULL,
      port INTEGER NOT NULL,
      added_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_checked DATETIME,
      is_online BOOLEAN DEFAULT 0,
      UNIQUE(address, port)
    )
  `);

  // Server status history table
  const createHistoryTable = db.prepare(`
    CREATE TABLE IF NOT EXISTS server_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      server_id INTEGER NOT NULL,
      checked_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      is_online BOOLEAN NOT NULL,
      ping INTEGER,
      player_count INTEGER,
      max_players INTEGER,
      map_name TEXT,
      game_mode TEXT,
      server_info TEXT,
      FOREIGN KEY (server_id) REFERENCES servers (id) ON DELETE CASCADE
    )
  `);

  createServersTable.run();
  createHistoryTable.run();
};

// Initialize database
createTables();

// Database operations
export const serverDb = {
  // Add a new server
  addServer: db.prepare(`
    INSERT INTO servers (name, address, port)
    VALUES (?, ?, ?)
  `),

  // Get all servers
  getAllServers: db.prepare(`
    SELECT * FROM servers
    ORDER BY added_date DESC
  `),

  // Get server by ID
  getServerById: db.prepare(`
    SELECT * FROM servers WHERE id = ?
  `),

  // Update server status
  updateServerStatus: db.prepare(`
    UPDATE servers 
    SET last_checked = CURRENT_TIMESTAMP, is_online = ?
    WHERE id = ?
  `),

  // Delete server
  deleteServer: db.prepare(`
    DELETE FROM servers WHERE id = ?
  `),

  // Add server history entry
  addServerHistory: db.prepare(`
    INSERT INTO server_history (
      server_id, is_online, ping, player_count, max_players, map_name, game_mode, server_info
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `),

  // Get server history
  getServerHistory: db.prepare(`
    SELECT * FROM server_history 
    WHERE server_id = ? 
    ORDER BY checked_date DESC 
    LIMIT ?
  `),

  // Get latest status for all servers
  getLatestServerStatuses: db.prepare(`
    SELECT
      s.*,
      h.ping,
      h.player_count,
      h.max_players,
      h.map_name,
      h.game_mode
    FROM servers s
    LEFT JOIN (
      SELECT
        h1.server_id,
        h1.ping,
        h1.player_count,
        h1.max_players,
        h1.map_name,
        h1.game_mode
      FROM server_history h1
      INNER JOIN (
        SELECT server_id, MAX(checked_date) as latest_check
        FROM server_history
        GROUP BY server_id
      ) h2 ON h1.server_id = h2.server_id AND h1.checked_date = h2.latest_check
    ) h ON s.id = h.server_id
    ORDER BY s.added_date DESC
  `)
};

export default db;
