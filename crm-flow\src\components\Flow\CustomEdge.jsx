import React, { useState } from 'react';
import { BaseEdge, EdgeLabelRenderer, getBezierPath } from '@xyflow/react';

const CustomEdge = ({
  id,
  source,
  target,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  data,
  selected,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  // Get the path for the edge
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  // Calculate the position for the delete button (middle of the edge)
  const deleteButtonX = (sourceX + targetX) / 2;
  const deleteButtonY = (sourceY + targetY) / 2;

  // Handle delete button click
  const handleDeleteClick = (event) => {
    event.stopPropagation();
    if (data && data.onDelete) {
      data.onDelete(id);
    }
  };

  return (
    <>
      {/* The actual edge path */}
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={{
          ...style,
          stroke: selected || isHovered ? '#ff6b6b' : style.stroke || '#b1b1b7',
          strokeWidth: selected || isHovered ? 2 : style.strokeWidth || 1.5,
          cursor: 'pointer',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      />

      {/* Delete button rendered using EdgeLabelRenderer */}
      <EdgeLabelRenderer>
        {(selected || isHovered) && (
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${deleteButtonX}px, ${deleteButtonY}px)`,
              pointerEvents: 'all',
              zIndex: 10,
            }}
          >
            <button
              className="edge-delete-button"
              onClick={handleDeleteClick}
              title="Delete connection"
            >
              <i className="bi bi-x-circle-fill"></i>
            </button>
          </div>
        )}
      </EdgeLabelRenderer>
    </>
  );
};

export default CustomEdge;
