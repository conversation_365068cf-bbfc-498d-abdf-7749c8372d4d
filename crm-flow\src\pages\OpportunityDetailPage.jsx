import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { doc, getDoc, deleteDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import Modal from '../components/UI/Modal';
import OpportunityForm from '../components/Forms/OpportunityForm';
import ContactForm from '../components/Forms/ContactForm';
import TaskForm from '../components/Forms/TaskForm';
import RelationshipsList from '../components/UI/RelationshipsList';
import 'bootstrap/dist/css/bootstrap.min.css';

const OpportunityDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [opportunity, setOpportunity] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);
  const [showTaskModal, setShowTaskModal] = useState(false);

  // Fetch opportunity details
  useEffect(() => {
    const fetchOpportunity = async () => {
      try {
        setLoading(true);
        const opportunityDoc = await getDoc(doc(db, 'opportunities', id));

        if (opportunityDoc.exists()) {
          setOpportunity({
            id: opportunityDoc.id,
            ...opportunityDoc.data()
          });
        } else {
          console.error('Opportunity not found');
          // Redirect to opportunities page if not found
          navigate('/opportunities');
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching opportunity:', error);
        setLoading(false);
      }
    };

    if (currentUser && id) {
      fetchOpportunity();
    }
  }, [currentUser, id, navigate]);

  // Handle opportunity deletion
  const handleDeleteOpportunity = async () => {
    if (window.confirm('Are you sure you want to delete this opportunity?')) {
      try {
        await deleteDoc(doc(db, 'opportunities', id));
        // Redirect to opportunities page after deletion
        navigate('/opportunities');
      } catch (error) {
        console.error('Error deleting opportunity:', error);
      }
    }
  };

  // Use the currency context
  const { formatCurrency, availableCurrencies } = useCurrency();

  // Format currency with opportunity's specific currency if available
  const formatOpportunityCurrency = (value, currencyCode) => {
    if (currencyCode) {
      // Special case for JPY and other currencies that don't typically use decimal places
      const fractionDigits = ['JPY', 'KRW', 'VND', 'IDR'].includes(currencyCode) ? 0 : 2;

      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: fractionDigits,
        maximumFractionDigits: fractionDigits
      }).format(value);
    }

    // Fall back to user's default currency
    return formatCurrency(value);
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'New':
        return 'bg-primary';
      case 'Qualified':
        return 'bg-info';
      case 'Proposal':
        return 'bg-warning';
      case 'Negotiation':
        return 'bg-danger';
      case 'Closed Won':
        return 'bg-success';
      case 'Closed Lost':
        return 'bg-secondary';
      case 'Archived':
        return 'bg-dark';
      default:
        return 'bg-primary';
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '100%' }}>
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!opportunity) {
    return (
      <div style={{ height: '100%', width: '100%', overflow: 'auto', padding: '20px' }}>
        <div className="container">
          <div className="alert alert-danger">
            Opportunity not found. It may have been deleted.
          </div>
          <button
            className="btn btn-primary"
            onClick={() => navigate('/opportunities')}
          >
            Back to Opportunities
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%', overflow: 'auto', padding: '20px' }}>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <div className="d-flex align-items-center">
            <button
              className="btn btn-outline-secondary me-3"
              onClick={() => navigate('/opportunities')}
            >
              <i className="bi bi-arrow-left"></i>
            </button>
            <h2>{opportunity.name}</h2>
          </div>
          <div>
            <button
              className="btn btn-primary me-2"
              onClick={() => setShowEditModal(true)}
            >
              <i className="bi bi-pencil me-1"></i>
              Edit
            </button>
            <button
              className="btn btn-danger"
              onClick={handleDeleteOpportunity}
            >
              <i className="bi bi-trash me-1"></i>
              Delete
            </button>
          </div>
        </div>

        <div className="row">
          <div className="col-md-8">
            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">Opportunity Details</h5>
              </div>
              <div className="card-body">
                <div className="row mb-3">
                  <div className="col-md-6">
                    <h6 className="text-muted">Company</h6>
                    <p className="mb-0">{opportunity.company || 'N/A'}</p>
                  </div>
                  <div className="col-md-6">
                    <h6 className="text-muted">Value</h6>
                    <p className="mb-0">
                      {opportunity.value ? formatOpportunityCurrency(opportunity.value, opportunity.currency) : 'N/A'}
                      {opportunity.currency && (
                        <span className="ms-2 text-muted small">({opportunity.currency})</span>
                      )}
                    </p>
                  </div>
                </div>
                <div className="row mb-3">
                  <div className="col-md-6">
                    <h6 className="text-muted">Status</h6>
                    <span className={`badge ${getStatusBadgeColor(opportunity.status)}`}>
                      {opportunity.status}
                    </span>
                  </div>
                  <div className="col-md-6">
                    <h6 className="text-muted">Close Date</h6>
                    <p className="mb-0">
                      {opportunity.closeDate ? new Date(opportunity.closeDate).toLocaleDateString() : 'N/A'}
                    </p>
                  </div>
                </div>
                <div className="row">
                  <div className="col-12">
                    <h6 className="text-muted">Description</h6>
                    <p className="mb-0">{opportunity.description || 'No description provided.'}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">Notes</h5>
              </div>
              <div className="card-body">
                <p>{opportunity.notes || 'No notes available.'}</p>
              </div>
            </div>

            <div className="card mb-4">
              <div className="card-header d-flex justify-content-between align-items-center">
                <h5 className="mb-0">
                  <i className="bi bi-diagram-3 me-2"></i>
                  Connections
                </h5>
                <button
                  className="btn btn-sm btn-outline-primary"
                  onClick={() => navigate('/flow')}
                >
                  <i className="bi bi-diagram-3 me-1"></i>
                  View in Flow
                </button>
              </div>
              <div className="card-body">
                <p className="text-muted mb-3">
                  <small>These are the connections made to this opportunity in the flow view:</small>
                </p>
                <RelationshipsList entityType="opportunity" entityId={id} />
              </div>
            </div>
          </div>

          <div className="col-md-4">
            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">Related Information</h5>
              </div>
              <div className="card-body">
                <div className="mb-3">
                  <h6 className="text-muted">Created</h6>
                  <p className="mb-0">
                    {opportunity.createdAt ? new Date(opportunity.createdAt.toDate()).toLocaleString() : 'N/A'}
                  </p>
                </div>
                <div>
                  <h6 className="text-muted">Last Updated</h6>
                  <p className="mb-0">
                    {opportunity.updatedAt ? new Date(opportunity.updatedAt.toDate()).toLocaleString() : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Quick Actions</h5>
              </div>
              <div className="card-body">
                <div className="d-grid gap-2">
                  <button
                    className="btn btn-outline-primary"
                    onClick={() => setShowContactModal(true)}
                  >
                    <i className="bi bi-person-plus me-1"></i>
                    Add Contact
                  </button>
                  <button
                    className="btn btn-outline-warning"
                    onClick={() => setShowTaskModal(true)}
                  >
                    <i className="bi bi-check2-square me-1"></i>
                    Create Task
                  </button>
                  <button className="btn btn-outline-secondary" onClick={() => navigate('/flow')}>
                    <i className="bi bi-diagram-3 me-1"></i>
                    View in Flow
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Opportunity Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="lg"
      >
        <OpportunityForm
          onClose={() => setShowEditModal(false)}
          onSuccess={() => {
            setShowEditModal(false);
            // Refresh opportunity data
            const fetchOpportunity = async () => {
              const opportunityDoc = await getDoc(doc(db, 'opportunities', id));
              if (opportunityDoc.exists()) {
                setOpportunity({
                  id: opportunityDoc.id,
                  ...opportunityDoc.data()
                });
              }
            };
            fetchOpportunity();
          }}
          opportunityData={opportunity}
          isEditing={true}
        />
      </Modal>

      {/* Add Contact Modal */}
      <Modal
        show={showContactModal}
        onClose={() => setShowContactModal(false)}
        size="lg"
      >
        <ContactForm
          onClose={() => setShowContactModal(false)}
          onSuccess={() => {
            setShowContactModal(false);
            // You could refresh related contacts here if needed
          }}
          initialData={{
            company: opportunity?.company,
            relatedOpportunity: opportunity?.id
          }}
        />
      </Modal>

      {/* Add Task Modal */}
      <Modal
        show={showTaskModal}
        onClose={() => setShowTaskModal(false)}
        size="lg"
      >
        <TaskForm
          onClose={() => setShowTaskModal(false)}
          onSuccess={() => {
            setShowTaskModal(false);
            // You could refresh related tasks here if needed
          }}
          initialData={{
            relatedTo: 'Opportunity',
            relatedId: opportunity?.id,
            relatedName: opportunity?.name
          }}
        />
      </Modal>
    </div>
  );
};

export default OpportunityDetailPage;
