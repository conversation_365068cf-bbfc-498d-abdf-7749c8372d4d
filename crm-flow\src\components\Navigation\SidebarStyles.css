/* Sidebar Styles */
.sidebar {
  width: 220px;
  height: 100vh;
  background-color: #1e293b;
  color: #e0e0e0;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  overflow-x: hidden;
  /* Ensure the sidebar is a flex container with space between items */
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  width: 50px;
}

.sidebar-header {
  padding: 10px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  height: 50px;
}

.sidebar.collapsed .sidebar-header {
  padding: 10px 0;
  justify-content: center;
  position: relative;
  height: 50px;
}

.logo-container {
  flex: 1;
  margin-right: 10px;
  overflow: hidden;
}

.app-logo {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.app-logo i {
  font-size: 1.5rem;
  color: #60a5fa;
  margin-right: 10px;
}

.app-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toggle-btn {
  background-color: rgba(255, 255, 255, 0.05);
  border: none;
  color: #fff;
  cursor: pointer;
  width: 30px;
  height: 30px;
  font-size: 0.9rem;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
  min-width: 30px;
  aspect-ratio: 1/1;
}

.sidebar.collapsed .toggle-btn {
  margin: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.sidebar-user {
  padding: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
  background-color: rgba(0, 0, 0, 0.15);
}

.sidebar.collapsed .sidebar-user {
  padding: 12px 0;
  display: flex;
  justify-content: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
}

.user-avatar-placeholder {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #60a5fa;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  border: 2px solid #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
}

.user-details {
  margin-left: 10px;
  overflow: hidden;
}

.user-name {
  font-weight: 500;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  font-size: 0.75rem;
  color: #aaa;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  padding: 2px 0;
  /* This will make the menu take up all available space, pushing the user section to the bottom */
  display: flex;
  flex-direction: column;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 7px 15px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0;
  margin: 1px 0;
  border-left: 3px solid transparent;
}

.sidebar.collapsed .nav-link {
  padding: 7px 0;
  justify-content: center;
  margin: 1px 0;
  border-left: none;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: #fff;
  border-left-color: rgba(255, 255, 255, 0.3);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.05);
  color: #60a5fa;
  border-left-color: #60a5fa;
}

.nav-link i {
  font-size: 1.1rem;
  min-width: 24px;
  text-align: center;
}

.sidebar.collapsed .nav-link i {
  min-width: unset;
  margin: 0;
}

.nav-link span {
  margin-left: 10px;
  white-space: nowrap;
}

.sidebar-footer {
  padding: 8px 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 2px;
}

.sign-out-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 6px 12px;
  background-color: rgba(220, 53, 69, 0.05);
  color: #ff6b6b;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sidebar.collapsed .sign-out-btn {
  justify-content: center;
  padding: 6px 0;
  margin: 2px 5px;
  width: auto;
  border-radius: 3px;
}

.sign-out-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.sign-out-btn i {
  font-size: 1.1rem;
  min-width: 24px;
  text-align: center;
}

.sidebar.collapsed .sign-out-btn i {
  min-width: unset;
  margin: 0;
}

.sign-out-btn span {
  margin-left: 10px;
  white-space: nowrap;
}

/* Main content adjustment */
.main-content {
  margin-left: 220px;
  transition: all 0.3s ease;
  width: calc(100% - 220px);
  height: 100vh;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 20px;
}

.main-content.expanded {
  margin-left: 50px;
  width: calc(100% - 50px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    width: 60px;
  }

  .sidebar:not(.collapsed) {
    width: 250px;
    position: absolute;
    z-index: 1050;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  }

  .main-content {
    margin-left: 60px;
    width: calc(100% - 60px);
  }

  .main-content.expanded {
    margin-left: 0;
    width: 100%;
  }

  .sidebar.collapsed {
    width: 0;
    overflow: hidden;
    border-right: none;
  }

  .sidebar.collapsed+.main-content {
    margin-left: 0;
    width: 100%;
  }
}