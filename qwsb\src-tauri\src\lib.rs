mod quakeworld;
mod master;
mod database;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{Manager, State};
use tauri_plugin_http::reqwest;

use database::{Database, FavoriteServer, AppSettings};
use master::MasterServer;

// Application state that will be shared between commands
struct AppState {
    servers: Mutex<HashMap<String, quakeworld::ServerInfo>>,
    database: Arc<Database>,
    master_servers: Mutex<Vec<MasterServer>>,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[derive(Debug, Serialize, Deserialize)]
struct ServerAddress {
    address: String,
    port: u16,
}

// Command to query a server's status
#[tauri::command]
async fn query_server(address: String, port: u16, state: State<'_, Arc<AppState>>) -> Result<quakeworld::ServerInfo, String> {
    match quakeworld::query_server_status(&address, port).await {
        Ok(server_info) => {
            // Store the server info in our state
            let mut servers = state.servers.lock().unwrap();
            let key = format!("{}:{}", address, port);
            servers.insert(key, server_info.clone());
            Ok(server_info)
        }
        Err(e) => Err(format!("Failed to query server: {}", e)),
    }
}

// Command to get all servers
#[tauri::command]
fn get_servers(state: State<'_, Arc<AppState>>) -> Vec<quakeworld::ServerInfo> {
    let servers = state.servers.lock().unwrap();
    servers.values().cloned().collect()
}

// Command to save servers to disk
#[tauri::command]
fn save_servers(state: State<'_, Arc<AppState>>) -> Result<(), String> {
    let servers = state.servers.lock().unwrap();
    let server_list: Vec<quakeworld::ServerInfo> = servers.values().cloned().collect();

    match state.database.save_servers(&server_list) {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("Failed to save servers: {}", e)),
    }
}

// Command to remove a server
#[tauri::command]
fn remove_server(address: String, port: u16, state: State<'_, Arc<AppState>>) -> bool {
    let mut servers = state.servers.lock().unwrap();
    let key = format!("{}:{}", address, port);
    servers.remove(&key).is_some()
}

// Command to ping a server
#[tauri::command]
async fn ping_server(address: String, port: u16) -> Result<i32, String> {
    match quakeworld::ping_server(&address, port).await {
        Ok(ping) => Ok(ping),
        Err(e) => Err(format!("Failed to ping server: {}", e)),
    }
}

// Command to query master servers
#[tauri::command]
async fn query_master_servers(state: State<'_, Arc<AppState>>) -> Result<Vec<String>, String> {
    let master_servers = state.master_servers.lock().unwrap().clone();

    match master::query_all_master_servers(&master_servers).await {
        Ok(servers) => {
            // Convert SocketAddr to strings
            let server_strings = servers.iter()
                .map(|addr| addr.to_string())
                .collect();

            Ok(server_strings)
        }
        Err(e) => Err(format!("Failed to query master servers: {}", e)),
    }
}

// Command to get master servers
#[tauri::command]
fn get_master_servers(state: State<'_, Arc<AppState>>) -> Vec<MasterServer> {
    state.master_servers.lock().unwrap().clone()
}

// Command to add a master server
#[tauri::command]
fn add_master_server(address: String, port: u16, state: State<'_, Arc<AppState>>) -> Result<(), String> {
    let server = MasterServer::new(address, port);

    // Add to database
    if let Err(e) = state.database.add_master_server(&server) {
        return Err(format!("Failed to add master server to database: {}", e));
    }

    // Add to state
    let mut master_servers = state.master_servers.lock().unwrap();
    master_servers.push(server);

    Ok(())
}

// Command to update a master server
#[tauri::command]
fn update_master_server(address: String, port: u16, enabled: bool, state: State<'_, Arc<AppState>>) -> Result<(), String> {
    let server = MasterServer {
        address: address.clone(),
        port,
        enabled,
    };

    // Update in database
    if let Err(e) = state.database.update_master_server(&server) {
        return Err(format!("Failed to update master server in database: {}", e));
    }

    // Update in state
    let mut master_servers = state.master_servers.lock().unwrap();
    if let Some(idx) = master_servers.iter().position(|s| s.address == address && s.port == port) {
        master_servers[idx] = server;
    }

    Ok(())
}

// Command to delete a master server
#[tauri::command]
fn delete_master_server(address: String, port: u16, state: State<'_, Arc<AppState>>) -> Result<(), String> {
    // Delete from database
    if let Err(e) = state.database.delete_master_server(&address, port) {
        return Err(format!("Failed to delete master server from database: {}", e));
    }

    // Delete from state
    let mut master_servers = state.master_servers.lock().unwrap();
    master_servers.retain(|s| !(s.address == address && s.port == port));

    Ok(())
}

// Command to add a favorite server
#[tauri::command]
fn add_favorite_server(address: String, port: u16, name: Option<String>, notes: Option<String>, state: State<'_, Arc<AppState>>) -> Result<i64, String> {
    let server = FavoriteServer {
        id: None,
        address,
        port,
        name,
        notes,
        added_at: chrono::Utc::now().timestamp(),
    };

    match state.database.add_favorite_server(&server) {
        Ok(id) => Ok(id),
        Err(e) => Err(format!("Failed to add favorite server: {}", e)),
    }
}

// Command to get favorite servers
#[tauri::command]
fn get_favorite_servers(state: State<'_, Arc<AppState>>) -> Result<Vec<FavoriteServer>, String> {
    match state.database.get_favorite_servers() {
        Ok(servers) => Ok(servers),
        Err(e) => Err(format!("Failed to get favorite servers: {}", e)),
    }
}

// Command to check if a server is a favorite
#[tauri::command]
fn is_favorite_server(address: String, port: u16, state: State<'_, Arc<AppState>>) -> Result<bool, String> {
    match state.database.is_favorite_server(&address, port) {
        Ok(is_favorite) => Ok(is_favorite),
        Err(e) => Err(format!("Failed to check if server is a favorite: {}", e)),
    }
}

// Command to remove a favorite server
#[tauri::command]
fn remove_favorite_server(address: String, port: u16, state: State<'_, Arc<AppState>>) -> Result<(), String> {
    match state.database.remove_favorite_server(&address, port) {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("Failed to remove favorite server: {}", e)),
    }
}

// Command to get application settings
#[tauri::command]
fn get_settings(state: State<'_, Arc<AppState>>) -> Result<AppSettings, String> {
    match state.database.get_settings() {
        Ok(settings) => Ok(settings),
        Err(e) => Err(format!("Failed to get settings: {}", e)),
    }
}

// Command to save application settings
#[tauri::command]
fn save_settings(settings: AppSettings, state: State<'_, Arc<AppState>>) -> Result<(), String> {
    match state.database.save_settings(&settings) {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("Failed to save settings: {}", e)),
    }
}

// Command to fetch server list from URL
#[tauri::command]
async fn fetch_server_list(url: String) -> Result<Vec<String>, String> {
    // Create a reqwest client
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

    // Fetch the server list
    let response = client.get(&url)
        .send()
        .await
        .map_err(|e| format!("Failed to fetch server list: {}", e))?;

    // Check if the request was successful
    if !response.status().is_success() {
        return Err(format!("Failed to fetch server list: HTTP {}", response.status()));
    }

    // Get the response text
    let text = response.text()
        .await
        .map_err(|e| format!("Failed to read response: {}", e))?;

    // Parse the server list (one server per line)
    let servers = text.lines()
        .filter(|line| !line.trim().is_empty() && !line.trim().starts_with('#'))
        .map(|line| line.trim().to_string())
        .collect();

    Ok(servers)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_sql::Builder::default().build())
        .plugin(tauri_plugin_http::init())
        .setup(|app| {
            // Initialize database
            let app_handle = app.handle();

            // Initialize database
            let database = Database::new(app_handle.clone()).expect("Failed to initialize database");

            // Add default master servers if none exist
            database.add_default_master_servers().expect("Failed to add default master servers");

            // Get master servers from database
            let master_servers = database.get_master_servers().expect("Failed to get master servers");

            // Load saved servers
            let saved_servers = match database.load_servers() {
                Ok(servers) => {
                    println!("Loaded {} servers from disk", servers.len());
                    servers
                },
                Err(e) => {
                    println!("Failed to load servers: {}", e);
                    Vec::new()
                }
            };

            // Convert servers to HashMap
            let mut servers_map = HashMap::new();
            for server in saved_servers {
                let key = format!("{}:{}", server.address, server.port);
                servers_map.insert(key, server);
            }

            // Create app state
            let app_state = Arc::new(AppState {
                servers: Mutex::new(servers_map),
                database: Arc::new(database),
                master_servers: Mutex::new(master_servers),
            });

            // Manage app state
            app.manage(app_state);

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            query_server,
            get_servers,
            save_servers,
            remove_server,
            ping_server,
            query_master_servers,
            get_master_servers,
            add_master_server,
            update_master_server,
            delete_master_server,
            add_favorite_server,
            get_favorite_servers,
            is_favorite_server,
            remove_favorite_server,
            get_settings,
            save_settings,
            fetch_server_list
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
