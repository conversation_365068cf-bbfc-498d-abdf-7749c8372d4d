import quakeworld from 'quakeworld';
import { serverDb } from './database.js';

// Query a single server for status
export const queryServer = (address, port) => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    // Query server status with detailed info (flag 31 = 1+2+4+8+16)
    quakeworld(address, port, 'status', [31], (err, data) => {
      if (err) {
        resolve({
          success: false,
          error: err.error || 'Unknown error',
          ping: null,
          isOnline: false
        });
        return;
      }

      const ping = Date.now() - startTime;
      
      try {
        const result = {
          success: true,
          isOnline: true,
          ping: ping,
          serverInfo: data,
          playerCount: data.players ? data.players.length : 0,
          maxPlayers: data.maxclients || 0,
          mapName: data.map || 'Unknown',
          gameMode: data.gamedir || 'qw',
          hostname: data.hostname || 'Unknown Server',
          players: data.players || []
        };
        
        resolve(result);
      } catch (parseError) {
        resolve({
          success: false,
          error: 'Failed to parse server response',
          ping: ping,
          isOnline: false
        });
      }
    });
  });
};

// Query server ping only
export const pingServer = (address, port) => {
  return new Promise((resolve, reject) => {
    quakeworld(address, port, 'ping', [], (err, pingTime) => {
      if (err) {
        resolve({
          success: false,
          error: err.error || 'Unknown error',
          ping: null
        });
        return;
      }

      resolve({
        success: true,
        ping: pingTime
      });
    });
  });
};

// Update server status in database
export const updateServerInDatabase = async (serverId, queryResult) => {
  try {
    // Update server online status
    serverDb.updateServerStatus.run(queryResult.isOnline ? 1 : 0, serverId);
    
    // Add history entry
    serverDb.addServerHistory.run(
      serverId,
      queryResult.isOnline ? 1 : 0,
      queryResult.ping,
      queryResult.playerCount || 0,
      queryResult.maxPlayers || 0,
      queryResult.mapName || null,
      queryResult.gameMode || null,
      queryResult.success ? JSON.stringify(queryResult.serverInfo) : null
    );
    
    return true;
  } catch (error) {
    console.error('Database update error:', error);
    return false;
  }
};

// Query all servers and update database
export const queryAllServers = async () => {
  const servers = serverDb.getAllServers.all();
  const results = [];
  
  for (const server of servers) {
    try {
      console.log(`Querying server: ${server.name} (${server.address}:${server.port})`);
      const result = await queryServer(server.address, server.port);
      
      // Update database with result
      await updateServerInDatabase(server.id, result);
      
      results.push({
        server: server,
        result: result
      });
      
      // Small delay between queries to avoid overwhelming servers
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.error(`Error querying server ${server.name}:`, error);
      results.push({
        server: server,
        result: {
          success: false,
          error: error.message,
          isOnline: false
        }
      });
    }
  }
  
  return results;
};

// Validate server address and port
export const validateServerAddress = (address, port) => {
  if (!address || typeof address !== 'string') {
    return { valid: false, error: 'Invalid address' };
  }
  
  if (!port || isNaN(port) || port < 1 || port > 65535) {
    return { valid: false, error: 'Invalid port (must be 1-65535)' };
  }
  
  // Basic hostname/IP validation
  const hostnameRegex = /^[a-zA-Z0-9.-]+$/;
  if (!hostnameRegex.test(address)) {
    return { valid: false, error: 'Invalid hostname format' };
  }
  
  return { valid: true };
};
