import { useState, useEffect, useRef } from 'react';
import { collection, getDocs, query, orderBy, doc, updateDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import { useAuth } from '../contexts/AuthContext';
import { getUserData } from '../services/dataAccessService';
import Modal from '../components/UI/Modal';
import ChecklistForm from '../components/Forms/ChecklistForm';
import 'bootstrap/dist/css/bootstrap.min.css';

const ChecklistsPage = () => {
  const { currentUser, organization } = useAuth();
  const [checklists, setChecklists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentChecklist, setCurrentChecklist] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCompleted, setFilterCompleted] = useState('all'); // 'all', 'completed', 'incomplete'
  const [showArchived, setShowArchived] = useState(false);
  const [editingItem, setEditingItem] = useState({ checklistId: null, itemId: null });
  const [editText, setEditText] = useState('');
  const editInputRef = useRef(null);

  // Fetch checklists
  const fetchChecklists = async () => {
    setLoading(true);
    setError('');

    try {
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get checklists based on organization context
      const checklistsData = await getUserData('checklists', currentUser.uid, organization?.id);

      // Sort by creation date (newest first)
      const sortedChecklists = checklistsData.sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
        return dateB - dateA;
      });

      setChecklists(sortedChecklists);
    } catch (error) {
      console.error('Error fetching checklists:', error);
      setError('Failed to load checklists');
    } finally {
      setLoading(false);
    }
  };

  // Load checklists on component mount
  useEffect(() => {
    fetchChecklists();
  }, [currentUser, organization]);

  // Handle editing a checklist
  const handleEdit = (checklist) => {
    setCurrentChecklist(checklist);
    setShowEditModal(true);
  };

  // Handle archiving a checklist
  const handleArchive = async (checklistId) => {
    if (!confirm('Are you sure you want to archive this checklist?')) {
      return;
    }

    try {
      const checklistRef = doc(db, 'checklists', checklistId);
      await updateDoc(checklistRef, {
        archived: true,
        archivedAt: new Date()
      });

      // Refresh the list
      fetchChecklists();
    } catch (error) {
      console.error('Error archiving checklist:', error);
      setError('Failed to archive checklist');
    }
  };

  // Handle toggling a checklist item
  const handleToggleItem = async (checklistId, itemId) => {
    try {
      // Find the checklist
      const checklist = checklists.find(c => c.id === checklistId);
      if (!checklist) return;

      // Find and toggle the item
      const updatedItems = checklist.items.map(item => {
        if (item.id === itemId) {
          return { ...item, completed: !item.completed };
        }
        return item;
      });

      // Update the document in Firestore
      const checklistRef = doc(db, 'checklists', checklistId);
      await updateDoc(checklistRef, { items: updatedItems });

      // Update local state
      setChecklists(checklists.map(c => {
        if (c.id === checklistId) {
          return { ...c, items: updatedItems };
        }
        return c;
      }));
    } catch (error) {
      console.error('Error toggling checklist item:', error);
      setError('Failed to update item');
    }
  };

  // Handle editing a checklist item
  const handleEditItem = (checklistId, itemId, currentText) => {
    setEditingItem({ checklistId, itemId });
    setEditText(currentText);
    // Focus the input after it renders
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 50);
  };

  const handleSaveEdit = async (checklistId, itemId) => {
    if (!editText.trim()) {
      return handleCancelEdit();
    }

    try {
      // Find the checklist
      const checklist = checklists.find(c => c.id === checklistId);
      if (!checklist) return;

      // Find and update the item
      const updatedItems = checklist.items.map(item => {
        if (item.id === itemId) {
          return { ...item, text: editText.trim() };
        }
        return item;
      });

      // Update the document in Firestore
      const checklistRef = doc(db, 'checklists', checklistId);
      await updateDoc(checklistRef, { items: updatedItems });

      // Update local state
      setChecklists(checklists.map(c => {
        if (c.id === checklistId) {
          return { ...c, items: updatedItems };
        }
        return c;
      }));

      // Reset editing state
      handleCancelEdit();
    } catch (error) {
      console.error('Error updating checklist item:', error);
      setError('Failed to update item');
    }
  };

  const handleCancelEdit = () => {
    setEditingItem({ checklistId: null, itemId: null });
    setEditText('');
  };

  // Handle deleting a checklist item
  const handleDeleteItem = async (checklistId, itemId) => {
    try {
      // Find the checklist
      const checklist = checklists.find(c => c.id === checklistId);
      if (!checklist) return;

      // Filter out the item to delete
      const updatedItems = checklist.items.filter(item => item.id !== itemId);

      // Update the document in Firestore
      const checklistRef = doc(db, 'checklists', checklistId);
      await updateDoc(checklistRef, { items: updatedItems });

      // Update local state
      setChecklists(checklists.map(c => {
        if (c.id === checklistId) {
          return { ...c, items: updatedItems };
        }
        return c;
      }));
    } catch (error) {
      console.error('Error deleting checklist item:', error);
      setError('Failed to delete item');
    }
  };

  // Handle unarchiving a checklist
  const handleUnarchive = async (checklistId) => {
    try {
      const checklistRef = doc(db, 'checklists', checklistId);
      await updateDoc(checklistRef, {
        archived: false,
        archivedAt: null
      });

      // Refresh the list
      fetchChecklists();
    } catch (error) {
      console.error('Error unarchiving checklist:', error);
      setError('Failed to unarchive checklist');
    }
  };

  // Filter checklists based on search term, completion status, and archived status
  const filteredChecklists = checklists
    .filter(checklist => showArchived ? checklist.archived : !checklist.archived) // Filter based on archived status
    .filter(checklist => {
      // Filter by search term
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          checklist.title.toLowerCase().includes(searchLower) ||
          (checklist.description && checklist.description.toLowerCase().includes(searchLower))
        );
      }
      return true;
    })
    .filter(checklist => {
      // Filter by completion status
      if (filterCompleted === 'all') return true;

      const totalItems = checklist.items?.length || 0;
      if (totalItems === 0) return filterCompleted === 'incomplete';

      const completedItems = checklist.items?.filter(item => item.completed)?.length || 0;

      if (filterCompleted === 'completed') {
        return totalItems > 0 && completedItems === totalItems;
      } else if (filterCompleted === 'incomplete') {
        return completedItems < totalItems;
      }

      return true;
    });

  // Calculate completion percentage
  const getCompletionPercentage = (checklist) => {
    const totalItems = checklist.items?.length || 0;
    if (totalItems === 0) return 0;

    const completedItems = checklist.items?.filter(item => item.completed)?.length || 0;
    return Math.round((completedItems / totalItems) * 100);
  };

  return (
    <div className="container-fluid py-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Checklists {showArchived && <span className="text-secondary">(Archived)</span>}</h1>
        <button
          className="btn btn-primary"
          onClick={() => setShowAddModal(true)}
        >
          <i className="bi bi-plus-lg me-1"></i> New Checklist
        </button>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-8">
              <div className="input-group">
                <span className="input-group-text">
                  <i className="bi bi-search"></i>
                </span>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Search checklists..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && (
                  <button
                    className="btn btn-outline-secondary"
                    type="button"
                    onClick={() => setSearchTerm('')}
                  >
                    <i className="bi bi-x"></i>
                  </button>
                )}
              </div>
            </div>
            <div className="col-md-4">
              <div className="d-flex gap-2">
                <select
                  className="form-select"
                  value={filterCompleted}
                  onChange={(e) => setFilterCompleted(e.target.value)}
                >
                  <option value="all">All Checklists</option>
                  <option value="completed">Completed</option>
                  <option value="incomplete">Incomplete</option>
                </select>
                <div className="form-check form-switch d-flex align-items-center">
                  <input
                    className="form-check-input me-2"
                    type="checkbox"
                    id="showArchivedSwitch"
                    checked={showArchived}
                    onChange={() => setShowArchived(!showArchived)}
                  />
                  <label className="form-check-label" htmlFor="showArchivedSwitch">
                    {showArchived ? 'Archived' : 'Active'}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="d-flex justify-content-center my-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : filteredChecklists.length === 0 ? (
        <div className="text-center my-5">
          <div className="display-6 text-muted mb-3">
            <i className="bi bi-clipboard-check"></i>
          </div>
          <h3>No checklists found</h3>
          <p className="text-muted">
            {searchTerm || filterCompleted !== 'all' || showArchived
              ? 'Try adjusting your search or filters'
              : 'Create your first checklist to get started'}
          </p>
          <button
            className="btn btn-primary mt-2"
            onClick={() => setShowAddModal(true)}
          >
            <i className="bi bi-plus-lg me-1"></i> Create Checklist
          </button>
        </div>
      ) : (
        <div className="row">
          {filteredChecklists.map(checklist => {
            const completionPercentage = getCompletionPercentage(checklist);
            const isComplete = completionPercentage === 100;

            return (
              <div key={checklist.id} className="col-md-6 col-lg-4 mb-4">
                <div className={`card h-100 ${isComplete ? 'border-success' : ''} ${checklist.archived ? 'bg-light' : ''}`}>
                  <div className="card-header d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">
                      {checklist.title}
                      {checklist.archived && (
                        <span className="badge bg-secondary ms-2" style={{ fontSize: '0.6rem' }}>
                          Archived
                        </span>
                      )}
                    </h5>
                    <div className="dropdown">
                      <button
                        className="btn btn-sm btn-link text-dark"
                        type="button"
                        id={`dropdown-${checklist.id}`}
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                      >
                        <i className="bi bi-three-dots-vertical"></i>
                      </button>
                      <ul className="dropdown-menu dropdown-menu-end" aria-labelledby={`dropdown-${checklist.id}`}>
                        <li>
                          <button
                            className="dropdown-item"
                            onClick={() => handleEdit(checklist)}
                          >
                            <i className="bi bi-pencil me-2"></i> Edit
                          </button>
                        </li>
                        <li>
                          {checklist.archived ? (
                            <button
                              className="dropdown-item text-success"
                              onClick={() => handleUnarchive(checklist.id)}
                            >
                              <i className="bi bi-arrow-counterclockwise me-2"></i> Unarchive
                            </button>
                          ) : (
                            <button
                              className="dropdown-item text-danger"
                              onClick={() => handleArchive(checklist.id)}
                            >
                              <i className="bi bi-archive me-2"></i> Archive
                            </button>
                          )}
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="card-body">
                    {checklist.description && (
                      <p className="card-text text-muted mb-3">{checklist.description}</p>
                    )}

                    <div className="progress mb-3" style={{ height: '8px' }}>
                      <div
                        className={`progress-bar ${isComplete ? 'bg-success' : 'bg-info'}`}
                        role="progressbar"
                        style={{ width: `${completionPercentage}%` }}
                        aria-valuenow={completionPercentage}
                        aria-valuemin="0"
                        aria-valuemax="100"
                      ></div>
                    </div>

                    <p className="small text-end mb-3">
                      {checklist.items?.filter(item => item.completed).length || 0}/{checklist.items?.length || 0} completed
                    </p>

                    <div className="checklist-items" style={{ maxHeight: '200px', overflowY: 'auto' }}>
                      {checklist.items && checklist.items.length > 0 ? (
                        <ul className="list-group list-group-flush">
                          {checklist.items.map(item => (
                            <li key={item.id} className="list-group-item px-0 py-2 d-flex align-items-center">
                              {editingItem.checklistId === checklist.id && editingItem.itemId === item.id ? (
                                <div className="input-group input-group-sm flex-grow-1">
                                  <input
                                    ref={editInputRef}
                                    type="text"
                                    className="form-control"
                                    value={editText}
                                    onChange={(e) => setEditText(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit(checklist.id, item.id)}
                                  />
                                  <button
                                    className="btn btn-outline-success"
                                    onClick={() => handleSaveEdit(checklist.id, item.id)}
                                  >
                                    <i className="bi bi-check"></i>
                                  </button>
                                  <button
                                    className="btn btn-outline-secondary"
                                    onClick={handleCancelEdit}
                                  >
                                    <i className="bi bi-x"></i>
                                  </button>
                                </div>
                              ) : (
                                <div className="d-flex w-100 align-items-center">
                                  <div className="form-check mb-0 flex-grow-1">
                                    <input
                                      className="form-check-input"
                                      type="checkbox"
                                      checked={item.completed}
                                      onChange={() => handleToggleItem(checklist.id, item.id)}
                                      id={`item-${checklist.id}-${item.id}`}
                                    />
                                    <label
                                      className="form-check-label"
                                      htmlFor={`item-${checklist.id}-${item.id}`}
                                      style={{
                                        textDecoration: item.completed ? 'line-through' : 'none',
                                        color: item.completed ? '#6c757d' : 'inherit'
                                      }}
                                    >
                                      {item.text}
                                    </label>
                                  </div>
                                  <div className="btn-group btn-group-sm ms-2">
                                    <button
                                      className="btn btn-sm btn-outline-primary"
                                      onClick={() => handleEditItem(checklist.id, item.id, item.text)}
                                      title="Edit item"
                                    >
                                      <i className="bi bi-pencil"></i>
                                    </button>
                                    <button
                                      className="btn btn-sm btn-outline-danger"
                                      onClick={() => handleDeleteItem(checklist.id, item.id)}
                                      title="Delete item"
                                    >
                                      <i className="bi bi-trash"></i>
                                    </button>
                                  </div>
                                </div>
                              )}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-muted small">No items in this checklist</p>
                      )}
                    </div>
                  </div>
                  <div className="card-footer text-muted">
                    <small>
                      Created: {checklist.createdAt ? new Date(checklist.createdAt).toLocaleDateString() : 'Unknown'}
                      {checklist.archived && checklist.archivedAt && (
                        <span className="ms-2">
                          | Archived: {new Date(checklist.archivedAt).toLocaleDateString()}
                        </span>
                      )}
                    </small>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Add Checklist Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="lg"
      >
        <ChecklistForm
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            fetchChecklists();
          }}
        />
      </Modal>

      {/* Edit Checklist Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="lg"
      >
        {currentChecklist && (
          <ChecklistForm
            onClose={() => setShowEditModal(false)}
            onSuccess={() => {
              setShowEditModal(false);
              fetchChecklists();
            }}
            checklistData={currentChecklist}
            isEditing={true}
          />
        )}
      </Modal>
    </div>
  );
};

export default ChecklistsPage;
