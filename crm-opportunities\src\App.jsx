import React, { useEffect } from 'react'
import { GoogleOAuthProvider } from '@react-oauth/google'
import ErrorBoundary from './components/ErrorBoundary'
import CRMDashboard from './components/CRMDashboard'
import { initializeDatabase } from './database/schema'
import './App.css'

// Google OAuth Client ID - can be set via environment variable or use demo mode
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || '726669738642-soda7gkgg1hoak8akmedt6in21tjn8ci.apps.googleusercontent.com'

function App() {
  useEffect(() => {
    // Initialize database on app start
    try {
      initializeDatabase();
      // Database initialized successfully
    } catch (error) {
      console.error('Failed to initialize database:', error);
    }
  }, []);

  return (
    <ErrorBoundary>
      <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
        <div className="App">
          <CRMDashboard />
        </div>
      </GoogleOAuthProvider>
    </ErrorBoundary>
  )
}

export default App
