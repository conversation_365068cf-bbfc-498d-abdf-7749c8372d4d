import React, { useEffect } from 'react'
import CRMDashboard from './components/CRMDashboard'
import { initializeDatabase } from './database/schema'
import './App.css'

function App() {
  useEffect(() => {
    // Initialize database on app start
    try {
      initializeDatabase();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
    }
  }, []);

  return (
    <div className="App">
      <CRMDashboard />
    </div>
  )
}

export default App
