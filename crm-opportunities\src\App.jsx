import React, { useEffect } from 'react'
import { GoogleOAuthProvider } from '@react-oauth/google'
import CRMDashboard from './components/CRMDashboard'
import { initializeDatabase } from './database/schema'
import './App.css'

// Google OAuth Client ID - can be set via environment variable or use demo mode
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || 'YOUR_GOOGLE_CLIENT_ID'

function App() {
  useEffect(() => {
    // Initialize database on app start
    try {
      initializeDatabase();
      // Database initialized successfully
    } catch (error) {
      console.error('Failed to initialize database:', error);
    }
  }, []);

  return (
    <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
      <div className="App">
        <CRMDashboard />
      </div>
    </GoogleOAuthProvider>
  )
}

export default App
