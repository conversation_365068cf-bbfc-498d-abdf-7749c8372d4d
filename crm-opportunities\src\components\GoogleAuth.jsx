import React, { useState, useEffect } from 'react';
import { LogIn, LogOut, User } from 'lucide-react';

// Simple Google OAuth implementation using Google Identity Services
const GoogleAuth = ({ onAuthChange }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in (from localStorage)
    const savedUser = localStorage.getItem('crm_user');
    if (savedUser) {
      const userData = JSON.parse(savedUser);
      setUser(userData);
      onAuthChange?.(userData);
    }
    setIsLoading(false);

    // Load Google Identity Services
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;
    script.onload = initializeGoogleAuth;
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [onAuthChange]);

  const initializeGoogleAuth = () => {
    if (window.google) {
      window.google.accounts.id.initialize({
        client_id: 'YOUR_GOOGLE_CLIENT_ID', // Replace with your actual client ID
        callback: handleCredentialResponse,
        auto_select: false,
        cancel_on_tap_outside: true,
      });
    }
  };

  const handleCredentialResponse = (response) => {
    try {
      // Decode the JWT token (in production, verify this server-side)
      const payload = JSON.parse(atob(response.credential.split('.')[1]));
      
      const userData = {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
        picture: payload.picture,
        loginTime: new Date().toISOString()
      };

      setUser(userData);
      localStorage.setItem('crm_user', JSON.stringify(userData));
      onAuthChange?.(userData);
    } catch (error) {
      console.error('Error processing Google auth response:', error);
    }
  };

  const handleGoogleLogin = () => {
    if (window.google) {
      window.google.accounts.id.prompt();
    } else {
      // Fallback: simulate login for demo purposes
      const demoUser = {
        id: 'demo_user',
        email: '<EMAIL>',
        name: 'Demo User',
        picture: null,
        loginTime: new Date().toISOString()
      };
      setUser(demoUser);
      localStorage.setItem('crm_user', JSON.stringify(demoUser));
      onAuthChange?.(demoUser);
    }
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('crm_user');
    onAuthChange?.(null);
    
    if (window.google) {
      window.google.accounts.id.disableAutoSelect();
    }
  };

  if (isLoading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ 
          width: '20px', 
          height: '20px', 
          border: '2px solid #e5e7eb', 
          borderTop: '2px solid #3b82f6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <span style={{ fontSize: '14px', color: '#6b7280' }}>Loading...</span>
      </div>
    );
  }

  if (user) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {user.picture ? (
            <img 
              src={user.picture} 
              alt={user.name}
              style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                border: '2px solid #e5e7eb'
              }}
            />
          ) : (
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: '#3b82f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '14px',
              fontWeight: 'bold'
            }}>
              {user.name?.charAt(0) || <User size={16} />}
            </div>
          )}
          <div>
            <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>
              {user.name}
            </div>
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              {user.email}
            </div>
          </div>
        </div>
        
        <button
          onClick={handleLogout}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            padding: '6px 12px',
            background: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: '500'
          }}
        >
          <LogOut size={14} />
          Logout
        </button>
      </div>
    );
  }

  return (
    <button
      onClick={handleGoogleLogin}
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: '8px 16px',
        background: '#4285f4',
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500'
      }}
    >
      <LogIn size={16} />
      Sign in with Google
    </button>
  );
};

export default GoogleAuth;
