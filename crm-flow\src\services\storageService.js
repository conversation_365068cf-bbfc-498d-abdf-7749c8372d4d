import { 
  getStorage, 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject, 
  listAll 
} from 'firebase/storage';
import { v4 as uuidv4 } from 'uuid';
import { db } from './firebase';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';

// Initialize Firebase Storage
const storage = getStorage();

// Collection name for file metadata
const FILES_COLLECTION = 'files';

/**
 * Upload a file to Firebase Storage
 * @param {File} file - The file to upload
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID
 * @param {string} nodeId - The node ID this file belongs to
 * @returns {Promise<Object>} - The result of the operation
 */
export const uploadFile = async (file, userId, organizationId, nodeId) => {
  try {
    // Generate a unique ID for the file
    const fileId = uuidv4();
    
    // Get file extension
    const fileExt = file.name.split('.').pop().toLowerCase();
    
    // Create a new filename with the unique ID
    const newFilename = `${fileId}.${fileExt}`;
    
    // Create a reference to the file in Firebase Storage
    const storageRef = ref(storage, `organizations/${organizationId}/files/${newFilename}`);
    
    // Upload the file
    const snapshot = await uploadBytes(storageRef, file);
    
    // Get the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    // Store file metadata in Firestore
    const fileData = {
      id: fileId,
      originalName: file.name,
      fileName: newFilename,
      fileType: file.type,
      fileSize: file.size,
      fileExt: fileExt,
      downloadURL: downloadURL,
      storagePath: snapshot.ref.fullPath,
      nodeId: nodeId,
      organizationId: organizationId,
      uploadedBy: userId,
      uploadedAt: new Date(),
      isDeleted: false
    };
    
    // Add to Firestore
    const docRef = await addDoc(collection(db, FILES_COLLECTION), fileData);
    
    return {
      success: true,
      fileId: fileId,
      docId: docRef.id,
      fileData: {
        ...fileData,
        docId: docRef.id
      }
    };
  } catch (error) {
    console.error('Error uploading file:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get all files for a node
 * @param {string} nodeId - The node ID
 * @returns {Promise<Array>} - Array of file metadata
 */
export const getNodeFiles = async (nodeId) => {
  try {
    const filesQuery = query(
      collection(db, FILES_COLLECTION),
      where('nodeId', '==', nodeId),
      where('isDeleted', '==', false)
    );
    
    const snapshot = await getDocs(filesQuery);
    
    const files = [];
    snapshot.forEach(doc => {
      files.push({
        ...doc.data(),
        docId: doc.id
      });
    });
    
    return files;
  } catch (error) {
    console.error('Error getting node files:', error);
    return [];
  }
};

/**
 * Delete a file from storage and update its metadata
 * @param {string} docId - The Firestore document ID
 * @param {string} storagePath - The storage path
 * @returns {Promise<Object>} - The result of the operation
 */
export const deleteFile = async (docId, storagePath) => {
  try {
    // Create a reference to the file
    const fileRef = ref(storage, storagePath);
    
    // Delete the file from storage
    await deleteObject(fileRef);
    
    // Update the file metadata in Firestore
    const fileDocRef = doc(db, FILES_COLLECTION, docId);
    await updateDoc(fileDocRef, {
      isDeleted: true,
      deletedAt: new Date()
    });
    
    return {
      success: true,
      message: 'File deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting file:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Permanently delete a file and its metadata
 * @param {string} docId - The Firestore document ID
 * @param {string} storagePath - The storage path
 * @returns {Promise<Object>} - The result of the operation
 */
export const permanentlyDeleteFile = async (docId, storagePath) => {
  try {
    // Create a reference to the file
    const fileRef = ref(storage, storagePath);
    
    // Delete the file from storage
    await deleteObject(fileRef);
    
    // Delete the file metadata from Firestore
    const fileDocRef = doc(db, FILES_COLLECTION, docId);
    await deleteDoc(fileDocRef);
    
    return {
      success: true,
      message: 'File permanently deleted'
    };
  } catch (error) {
    console.error('Error permanently deleting file:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
