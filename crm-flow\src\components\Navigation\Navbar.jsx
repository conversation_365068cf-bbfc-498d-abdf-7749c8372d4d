import { signOut } from 'firebase/auth';
import { auth } from '../../services/firebase';
import { useNavigate, NavLink, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import 'bootstrap/dist/css/bootstrap.min.css';

const Navbar = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      navigate('/signin');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-dark" style={{ backgroundColor: '#1e1e1e', borderBottom: '1px solid #333' }}>
      <div className="container-fluid">
        <NavLink className="navbar-brand" to="/dashboard">CRM Flow</NavLink>
        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>
        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav me-auto">
            <li className="nav-item">
              <NavLink
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
                to="/dashboard"
              >
                <i className="bi bi-speedometer2 me-1"></i> Dashboard
              </NavLink>
            </li>
            <li className="nav-item">
              <NavLink
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
                to="/contacts"
              >
                <i className="bi bi-person me-1"></i> Contacts
              </NavLink>
            </li>
            <li className="nav-item">
              <NavLink
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
                to="/companies"
              >
                <i className="bi bi-building me-1"></i> Companies
              </NavLink>
            </li>
            <li className="nav-item">
              <NavLink
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
                to="/opportunities"
              >
                <i className="bi bi-graph-up me-1"></i> Opportunities
              </NavLink>
            </li>
            <li className="nav-item">
              <NavLink
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
                to="/tasks"
              >
                <i className="bi bi-check2-square me-1"></i> Tasks
              </NavLink>
            </li>
            <li className="nav-item">
              <NavLink
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
                to="/flow"
              >
                <i className="bi bi-diagram-3 me-1"></i> Flow View
              </NavLink>
            </li>
            {/* Chat tab removed */}
            <li className="nav-item">
              <NavLink
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
                to="/organization"
              >
                <i className="bi bi-people me-1"></i> Organization
              </NavLink>
            </li>
          </ul>
          {currentUser && (
            <div className="d-flex align-items-center">
              <span className="navbar-text me-3">
                {currentUser.email}
              </span>
              {currentUser.photoURL && (
                <img
                  src={currentUser.photoURL}
                  alt="User Avatar"
                  className="rounded-circle me-2"
                  style={{ width: '32px', height: '32px', objectFit: 'cover', border: '2px solid #fff' }}
                />
              )}
              <button
                className="btn btn-outline-light"
                onClick={handleSignOut}
              >
                Sign Out
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
