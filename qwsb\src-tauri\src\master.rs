use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use std::time::Duration;
use tokio::net::UdpSocket;
use tokio::time::timeout;

const UDP_TIMEOUT: Duration = Duration::from_secs(10);

// Default QuakeWorld master servers
pub const DEFAULT_MASTER_SERVERS: [&str; 8] = [
    // Original master servers
    "master.quakeworld.nu:27000",
    "qwmaster.ocrana.de:27000",
    "qwmaster.fodquake.net:27000",

    // Additional master servers from quakeservers.net
    "master.quakeservers.net:27000",
    "asgaard.morphos-team.net:27000",
    "qwmaster.qtv.eu:27000",
    "qwmaster.qw-dev.net:27000",
    "qwmaster.quakeworld.ru:27000",
];

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct MasterServer {
    pub address: String,
    pub port: u16,
    pub enabled: bool,
}

impl MasterServer {
    pub fn new(address: String, port: u16) -> Self {
        MasterServer {
            address,
            port,
            enabled: true,
        }
    }

    pub fn from_address_string(address_string: &str) -> Option<Self> {
        let parts: Vec<&str> = address_string.split(':').collect();
        if parts.len() != 2 {
            return None;
        }

        let address = parts[0].to_string();
        let port = parts[1].parse::<u16>().ok()?;

        Some(MasterServer::new(address, port))
    }
}

// Parse a QuakeWorld master server response
// The response format is a series of 6-byte entries, where each entry is:
// - 4 bytes: IP address (network byte order)
// - 2 bytes: Port (network byte order)
fn parse_master_response(data: &[u8]) -> Vec<SocketAddr> {
    let mut servers = Vec::new();

    // Check if the response is empty or too short
    if data.len() < 8 {
        return servers;
    }

    // Check for the standard QuakeWorld response format
    // Response starts with \xff\xff\xff\xffd\n or \xff\xff\xff\xffc\n
    if data.len() >= 6 &&
       data[0] == 0xff && data[1] == 0xff && data[2] == 0xff && data[3] == 0xff &&
       (data[4] == b'd' || data[4] == b'c') && data[5] == b'\n' {

        // Skip the header (usually 8 bytes)
        let mut i = 8;

        while i + 6 <= data.len() {
            // Extract IP address (4 bytes)
            let ip = Ipv4Addr::new(data[i], data[i+1], data[i+2], data[i+3]);

            // Extract port (2 bytes, big-endian)
            let port = ((data[i+4] as u16) << 8) | (data[i+5] as u16);

            // Create socket address
            let addr = SocketAddr::new(IpAddr::V4(ip), port);
            servers.push(addr);

            i += 6;
        }
    }
    // Check for alternative response format (getserversResponse)
    else if data.len() >= 18 &&
            data[0] == 0xff && data[1] == 0xff && data[2] == 0xff && data[3] == 0xff &&
            &data[4..18] == b"getserversResponse" {

        // Skip the header
        let mut i = 18;

        while i + 7 <= data.len() {
            // Check for the separator byte (usually 0x5c or '\')
            if data[i] == 0x5c {
                // Extract IP address (4 bytes)
                let ip = Ipv4Addr::new(data[i+1], data[i+2], data[i+3], data[i+4]);

                // Extract port (2 bytes, big-endian)
                let port = ((data[i+5] as u16) << 8) | (data[i+6] as u16);

                // Create socket address
                let addr = SocketAddr::new(IpAddr::V4(ip), port);
                servers.push(addr);

                i += 7;
            } else {
                i += 1;
            }
        }
    }
    // Try to parse as a generic response by looking for IP address patterns
    else {
        // Look for patterns that might be IP addresses
        let mut i = 0;

        while i + 6 <= data.len() {
            // Check if port is in a reasonable range
            let port = ((data[i+4] as u16) << 8) | (data[i+5] as u16);
            if port > 1024 && port < 65535 {

                let ip = Ipv4Addr::new(data[i], data[i+1], data[i+2], data[i+3]);
                let addr = SocketAddr::new(IpAddr::V4(ip), port);
                servers.push(addr);
            }

            i += 1;
        }
    }

    // Remove any obviously invalid servers (like 0.0.0.0 or local addresses)
    servers.retain(|addr| {
        if let IpAddr::V4(ip) = addr.ip() {
            !ip.is_unspecified() && !ip.is_loopback() && !ip.is_private() && !ip.is_link_local()
        } else {
            true
        }
    });

    servers
}

// Query a master server for the list of active servers
pub async fn query_master_server(address: &str, port: u16) -> Result<Vec<SocketAddr>> {
    let socket = UdpSocket::bind("0.0.0.0:0").await?;

    // Try different query formats for different master servers
    // First try the standard QuakeWorld format
    let command1 = b"\xff\xff\xff\xffc\n\0";
    // Then try the alternative format used by some servers
    let command2 = b"\xff\xff\xff\xffstatus\0";
    // Also try the QW protocol specific command
    let command3 = b"\xff\xff\xff\xffgetservers\0";

    let addr = format!("{}:{}", address, port);

    // Try the first command
    socket.send_to(command1, &addr).await?;

    // Wait for response with timeout
    let mut response = vec![0u8; 8192]; // Increased buffer size
    let result = timeout(UDP_TIMEOUT, socket.recv_from(&mut response)).await;

    match result {
        Ok(Ok((size, _))) => {
            response.truncate(size);
            let servers = parse_master_response(&response);
            if !servers.is_empty() {
                return Ok(servers);
            }

            // If no servers found, try the second command
            socket.send_to(command2, &addr).await?;
            let result2 = timeout(UDP_TIMEOUT, socket.recv_from(&mut response)).await;

            match result2 {
                Ok(Ok((size, _))) => {
                    response.truncate(size);
                    let servers = parse_master_response(&response);
                    if !servers.is_empty() {
                        return Ok(servers);
                    }

                    // If still no servers, try the third command
                    socket.send_to(command3, &addr).await?;
                    let result3 = timeout(UDP_TIMEOUT, socket.recv_from(&mut response)).await;

                    match result3 {
                        Ok(Ok((size, _))) => {
                            response.truncate(size);
                            let servers = parse_master_response(&response);
                            Ok(servers)
                        }
                        _ => Ok(servers), // Return empty list if third attempt also fails
                    }
                }
                _ => Ok(servers), // Return empty list if second attempt fails
            }
        }
        Ok(Err(e)) => Err(anyhow::anyhow!("UDP error: {}", e)),
        Err(_) => Err(anyhow::anyhow!("UDP timeout")),
    }
}

// Query multiple master servers and combine the results
pub async fn query_all_master_servers(master_servers: &[MasterServer]) -> Result<Vec<SocketAddr>> {
    let mut all_servers = Vec::new();
    let mut errors = Vec::new();
    let mut success_count = 0;

    println!("Starting to query {} master servers",
        master_servers.iter().filter(|m| m.enabled).count());

    for master in master_servers {
        if !master.enabled {
            println!("Skipping disabled master server {}:{}", master.address, master.port);
            continue;
        }

        println!("Querying master server {}:{}", master.address, master.port);

        match query_master_server(&master.address, master.port).await {
            Ok(servers) => {
                println!("Successfully queried master server {}:{} - found {} servers",
                    master.address, master.port, servers.len());
                all_servers.extend(servers);
                success_count += 1;
            }
            Err(e) => {
                let error_msg = format!("Failed to query master server {}:{}: {}",
                    master.address, master.port, e);
                println!("{}", error_msg);
                errors.push(error_msg);
            }
        }
    }

    // Remove duplicates
    all_servers.sort_unstable();
    all_servers.dedup();

    println!("Found {} unique servers from {} successful master server queries",
        all_servers.len(), success_count);

    if all_servers.is_empty() {
        if !errors.is_empty() {
            println!("No servers found and encountered errors: {}", errors.join(", "));
            return Err(anyhow::anyhow!("Failed to query any master servers: {}",
                errors.join(", ")));
        } else {
            println!("No servers found but no errors encountered");
            return Err(anyhow::anyhow!("No servers found from master servers"));
        }
    }

    Ok(all_servers)
}
