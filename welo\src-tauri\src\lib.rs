mod commands;
mod database;
mod quakeworld;

use commands::DatabaseState;
use database::Database;
use std::sync::Mutex;
use tauri::Manager;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }

            // Initialize database
            let app_data_dir = app
                .path()
                .app_data_dir()
                .expect("Failed to get app data directory");

            // Create the directory if it doesn't exist
            std::fs::create_dir_all(&app_data_dir).expect("Failed to create app data directory");

            let db_path = app_data_dir.join("quakeworld.db");
            let database = Database::new(db_path).expect("Failed to initialize database");

            // Store database in app state
            app.manage(DatabaseState::new(database));

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            commands::get_servers,
            commands::add_server,
            commands::delete_server,
            commands::update_server_name,
            commands::query_server,
            commands::query_all_servers,
            commands::get_server_history,
            commands::test_server,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
