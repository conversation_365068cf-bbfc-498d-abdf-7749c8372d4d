import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CurrencyProvider, useCurrency, AVAILABLE_CURRENCIES } from '../contexts/CurrencyContext';
import { AuthProvider } from '../contexts/AuthContext';
import { BrowserRouter } from 'react-router-dom';
import React from 'react';

// Mock component to test the currency context
const TestComponent = () => {
  const { 
    currency, 
    setCurrency, 
    formatCurrency, 
    getCurrencySymbol, 
    availableCurrencies 
  } = useCurrency();

  return (
    <div>
      <div data-testid="current-currency">{currency}</div>
      <div data-testid="currency-symbol">{getCurrencySymbol()}</div>
      <div data-testid="formatted-value">{formatCurrency(1234.56)}</div>
      <select 
        data-testid="currency-select"
        value={currency}
        onChange={(e) => setCurrency(e.target.value)}
      >
        {availableCurrencies.map(curr => (
          <option key={curr.code} value={curr.code}>
            {curr.code} - {curr.name}
          </option>
        ))}
      </select>
    </div>
  );
};

// Wrapper component with all required providers
const TestWrapper = ({ children }) => {
  return (
    <BrowserRouter>
      <AuthProvider>
        <CurrencyProvider>
          {children}
        </CurrencyProvider>
      </AuthProvider>
    </BrowserRouter>
  );
};

describe('CurrencyContext', () => {
  test('provides default currency (USD)', async () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Wait for the context to initialize
    await waitFor(() => {
      expect(screen.getByTestId('current-currency')).toHaveTextContent('USD');
    });
  });

  test('formats currency correctly', async () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Wait for the context to initialize and check formatting
    await waitFor(() => {
      const formattedValue = screen.getByTestId('formatted-value').textContent;
      expect(formattedValue).toMatch(/\$1,234\.56/);
    });
  });

  test('provides currency symbol', async () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Wait for the context to initialize
    await waitFor(() => {
      expect(screen.getByTestId('currency-symbol')).toHaveTextContent('$');
    });
  });

  test('has all required currencies available', () => {
    // Check that we have the essential currencies
    const requiredCurrencies = ['USD', 'EUR', 'GBP', 'JPY'];
    
    requiredCurrencies.forEach(code => {
      const currency = AVAILABLE_CURRENCIES.find(c => c.code === code);
      expect(currency).toBeDefined();
      expect(currency.symbol).toBeDefined();
      expect(currency.name).toBeDefined();
    });
  });
});
