import { collection, doc, setDoc, getDoc, updateDoc, query, where, getDocs } from 'firebase/firestore';
import { db } from './firebase';
import { getNodePositionsFromSession, setUnsavedChanges } from './sessionStorageService';
import { cacheService } from './cacheService';

// Collection name
const NODE_POSITIONS_COLLECTION = 'nodePositions';

/**
 * Save node positions to Firestore
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID
 * @param {Array} nodes - The array of nodes with positions (optional, if not provided, uses session storage)
 */
export const saveNodePositions = async (userId, organizationId, nodes = null) => {
  try {
    // Get positions either from nodes parameter or from session storage
    let positions;

    if (nodes) {
      // Extract positions from nodes array
      positions = {};
      nodes.forEach(node => {
        // Save position for all nodes
        positions[node.id] = {
          x: node.position.x,
          y: node.position.y
        };
      });
    } else {
      // Get positions from session storage
      positions = getNodePositionsFromSession(organizationId);
    }

    if (!positions) {
      console.warn('No positions to save');
      return { success: false, error: 'No positions to save' };
    }

    // Create a document reference for this organization's node positions
    const docRef = doc(db, NODE_POSITIONS_COLLECTION, organizationId);

    // Check if the document exists
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      // Update existing document
      await updateDoc(docRef, {
        positions,
        updatedAt: new Date(),
        lastUpdatedBy: userId
      });
    } else {
      // Create new document
      await setDoc(docRef, {
        organizationId,
        positions,
        updatedAt: new Date(),
        lastUpdatedBy: userId
      });
    }

    // Reset the unsaved changes flag
    setUnsavedChanges(false);

    // Invalidate cache
    cacheService.invalidateDocument(NODE_POSITIONS_COLLECTION, organizationId);

    return { success: true };
  } catch (error) {
    console.error('Error saving node positions:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Load node positions from Firestore
 * @param {string} organizationId - The organization ID
 * @param {boolean} bypassCache - Whether to bypass the cache
 * @returns {Object|null} - The node positions or null if not found
 */
export const loadNodePositions = async (organizationId, bypassCache = false) => {
  try {
    // Check cache first if not bypassing
    if (!bypassCache) {
      const cachedPositions = cacheService.getDocument(NODE_POSITIONS_COLLECTION, organizationId);
      if (cachedPositions && cachedPositions.positions) {
        return cachedPositions.positions;
      }
    }

    // Get the document reference
    const docRef = doc(db, NODE_POSITIONS_COLLECTION, organizationId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();

      // Cache the result
      cacheService.setDocument(NODE_POSITIONS_COLLECTION, organizationId, data);

      return data.positions;
    }

    return null;
  } catch (error) {
    console.error('Error loading node positions:', error);
    return null;
  }
};

/**
 * Update a single node's position directly in Firebase
 * This is a legacy method and should only be used when immediate Firebase updates are needed
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID
 * @param {string} nodeId - The node ID
 * @param {Object} position - The new position {x, y}
 */
export const updateNodePosition = async (userId, organizationId, nodeId, position) => {
  try {
    // Get the document reference
    const docRef = doc(db, NODE_POSITIONS_COLLECTION, organizationId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      // Get current positions
      const data = docSnap.data();
      const positions = data.positions || {};

      // Update the position for this node
      positions[nodeId] = {
        x: position.x,
        y: position.y
      };

      // Update the document
      await updateDoc(docRef, {
        positions,
        updatedAt: new Date(),
        lastUpdatedBy: userId
      });

      // Invalidate cache
      cacheService.invalidateDocument(NODE_POSITIONS_COLLECTION, organizationId);

      return { success: true };
    } else {
      // Create new document with this node position
      const positions = {};
      positions[nodeId] = {
        x: position.x,
        y: position.y
      };

      await setDoc(docRef, {
        organizationId,
        positions,
        updatedAt: new Date(),
        lastUpdatedBy: userId
      });

      // Invalidate cache
      cacheService.invalidateDocument(NODE_POSITIONS_COLLECTION, organizationId);

      return { success: true };
    }
  } catch (error) {
    console.error('Error updating node position:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Check if there are any remote changes since the last sync
 * @param {string} organizationId - The organization ID
 * @param {number} lastSyncTime - The timestamp of the last sync
 * @returns {Promise<boolean>} - Whether there are remote changes
 */
export const checkForRemoteChanges = async (organizationId, lastSyncTime) => {
  try {
    if (!organizationId || !lastSyncTime) return false;

    // Create a query for positions updated after the last sync
    const q = query(
      collection(db, NODE_POSITIONS_COLLECTION),
      where('organizationId', '==', organizationId),
      where('updatedAt', '>', new Date(lastSyncTime))
    );

    const snapshot = await getDocs(q);
    return !snapshot.empty;
  } catch (error) {
    console.error('Error checking for remote changes:', error);
    return false;
  }
};

/**
 * Get node positions from session storage or Firebase
 * @param {string} organizationId - The organization ID
 * @returns {Promise<Object>} - The node positions
 */
export const getNodePositions = async (organizationId) => {
  try {
    if (!organizationId) return {};

    // First try to get positions from session storage
    const sessionPositions = getNodePositionsFromSession(organizationId);
    if (sessionPositions) {
      return sessionPositions;
    }

    // If not in session, load from Firebase
    const firestorePositions = await loadNodePositions(organizationId) || {};

    // Check if we need to load from the new nodePositions collection format
    if (Object.keys(firestorePositions).length === 0) {
      try {
        // Query the new nodePositions collection format
        const nodePositionsQuery = query(
          collection(db, NODE_POSITIONS_COLLECTION),
          where('organizationId', '==', organizationId)
        );
        const snapshot = await getDocs(nodePositionsQuery);

        if (!snapshot.empty) {
          const positions = {};
          snapshot.forEach(doc => {
            const data = doc.data();
            if (data.id && data.position) {
              positions[data.id] = data.position;
            }
          });

          return positions;
        }
      } catch (error) {
        console.error('Error loading from new nodePositions format:', error);
      }
    }

    return firestorePositions;
  } catch (error) {
    console.error('Error getting node positions:', error);
    return {};
  }
};
