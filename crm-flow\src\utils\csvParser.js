/**
 * Parses a CSV or table-formatted string into an array of objects
 * @param {string} text - The CSV or table-formatted string to parse
 * @param {string} delimiter - The delimiter used in the CSV (default: ',')
 * @returns {Array} An array of objects representing the parsed data
 */
export const parseCSV = (text, delimiter = ',') => {
  // Split the text into lines
  const lines = text.trim().split('\n');

  // Remove any table formatting characters like |, -, and +
  const cleanLines = lines.map(line => {
    // Remove leading and trailing | characters
    let cleanLine = line.trim().replace(/^\||\|$/g, '');

    // If the line contains only dashes, plus signs, or spaces (table separator), skip it
    if (/^[\-\+\s]+$/.test(cleanLine)) {
      return null;
    }

    // Split by | for table format or by delimiter for CSV
    const cells = cleanLine.split('|').map(cell => cell.trim());

    return cells;
  }).filter(line => line !== null);

  // The first line contains the headers
  const headers = cleanLines[0];

  // Map the rest of the lines to objects using the headers
  const data = cleanLines.slice(1).map(line => {
    const obj = {};
    headers.forEach((header, index) => {
      // Clean up the header (remove spaces, lowercase)
      const cleanHeader = header.toLowerCase().replace(/\s+/g, '_');
      obj[cleanHeader] = line[index] || '';
    });
    return obj;
  });

  return data;
};

/**
 * Parses a markdown table string into an array of objects
 * @param {string} tableText - The markdown table string to parse
 * @returns {Array} An array of objects representing the parsed data
 */
export const parseMarkdownTable = (tableText) => {
  // Split the text into lines
  const lines = tableText.trim().split('\n');

  // Remove header separator line (the line with |---|---|...)
  const dataLines = lines.filter(line => !line.match(/^\s*\|[\s\-\|]+\|\s*$/));

  // Process each line
  const parsedLines = dataLines.map(line => {
    // Remove leading and trailing | and split by |
    return line.trim().replace(/^\||\|$/g, '').split('|').map(cell => cell.trim());
  });

  // The first line contains the headers
  const headers = parsedLines[0].map(header => header.toLowerCase().replace(/\s+/g, '_'));

  // Map the rest of the lines to objects using the headers
  const data = parsedLines.slice(1).map(line => {
    const obj = {};
    headers.forEach((header, index) => {
      obj[header] = line[index] || '';
    });
    return obj;
  });

  return data;
};

/**
 * Parses a JSON string into an array of objects
 * @param {string} jsonText - The JSON string to parse
 * @returns {Array} An array of objects representing the parsed data
 */
export const parseJSON = (jsonText) => {
  try {
    // Parse the JSON string
    const data = JSON.parse(jsonText);

    // Ensure the result is an array
    if (!Array.isArray(data)) {
      throw new Error('JSON input must be an array of objects');
    }

    // Convert property names to lowercase with underscores for consistency
    return data.map(item => {
      const normalizedItem = {};
      Object.keys(item).forEach(key => {
        const normalizedKey = key.toLowerCase().replace(/\s+/g, '_');
        normalizedItem[normalizedKey] = item[key];
      });
      return normalizedItem;
    });
  } catch (error) {
    console.error('Error parsing JSON:', error);
    throw new Error(`Failed to parse JSON: ${error.message}`);
  }
};

/**
 * Detects if the input text is a markdown table
 * @param {string} text - The text to check
 * @returns {boolean} True if the text appears to be a markdown table
 */
export const isMarkdownTable = (text) => {
  const lines = text.trim().split('\n');
  // Check if it has the characteristic markdown table separator line
  return lines.some(line => line.match(/^\s*\|[\s\-\|]+\|\s*$/));
};

/**
 * Detects if the input text is JSON
 * @param {string} text - The text to check
 * @returns {boolean} True if the text appears to be JSON
 */
export const isJSON = (text) => {
  const trimmedText = text.trim();
  return (trimmedText.startsWith('[') && trimmedText.endsWith(']')) ||
    (trimmedText.startsWith('{') && trimmedText.endsWith('}'));
};

/**
 * Parse input text, automatically detecting the format (JSON, CSV, or markdown table)
 * @param {string} text - The text to parse
 * @returns {Array} An array of objects representing the parsed data
 */
export const parseInputText = (text) => {
  if (isJSON(text)) {
    return parseJSON(text);
  } else if (isMarkdownTable(text)) {
    return parseMarkdownTable(text);
  } else {
    return parseCSV(text);
  }
};
