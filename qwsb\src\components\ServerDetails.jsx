import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { invoke } from "@tauri-apps/api/core";
import {
  Container,
  Title,
  Paper,
  Text,
  Group,
  Button,
  Table,
  Badge,
  Grid,
  Card,
  LoadingOverlay,
  Box,
  Tooltip
} from '@mantine/core';
import { IconRefresh, IconArrowLeft, IconStar, IconStarFilled } from '@tabler/icons-react';

function ServerDetails({ refreshServer, addFavorite, removeFavorite, isFavorite }) {
  const { address, port } = useParams();
  const navigate = useNavigate();
  const [server, setServer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFavorited, setIsFavorited] = useState(false);
  const [favoriteLoading, setFavoriteLoading] = useState(false);

  const numericPort = parseInt(port, 10);

  useEffect(() => {
    loadServerDetails();
    checkFavoriteStatus();
  }, [address, port]);

  // Check if server is a favorite
  const checkFavoriteStatus = async () => {
    try {
      const result = await isFavorite(address, numericPort);
      setIsFavorited(result);
    } catch (error) {
      console.error("Failed to check favorite status:", error);
    }
  };

  const loadServerDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get all servers and find the one we're looking for
      const servers = await invoke("get_servers");
      const serverInfo = servers.find(s =>
        s.address === address && s.port === numericPort
      );

      if (serverInfo) {
        setServer(serverInfo);
      } else {
        // If not found, try to query it directly
        try {
          const freshInfo = await invoke("query_server", { address, port: numericPort });
          setServer(freshInfo);
        } catch (err) {
          setError("Server not found or could not be reached");
        }
      }
    } catch (err) {
      setError(`Error loading server details: ${err.message || err}`);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setLoading(true);
      await refreshServer(address, numericPort);
      await loadServerDetails();
    } catch (err) {
      setError(`Error refreshing server: ${err.message || err}`);
    } finally {
      setLoading(false);
    }
  };

  if (error) {
    return (
      <Container size="xl" py="md">
        <Paper p="md" withBorder>
          <Title order={3} color="red">Error</Title>
          <Text>{error}</Text>
          <Group mt="md">
            <Button leftSection={<IconArrowLeft size={16} />} onClick={() => navigate('/')}>
              Back to Server List
            </Button>
            <Button leftSection={<IconRefresh size={16} />} onClick={loadServerDetails}>
              Try Again
            </Button>
          </Group>
        </Paper>
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <Box pos="relative">
        <LoadingOverlay visible={loading} overlayBlur={2} />

        <Group mb="md">
          <Button leftSection={<IconArrowLeft size={16} />} onClick={() => navigate('/')}>
            Back to Server List
          </Button>
          <Button leftSection={<IconRefresh size={16} />} onClick={handleRefresh}>
            Refresh
          </Button>
          <Button
            leftSection={isFavorited ? <IconStarFilled size={16} /> : <IconStar size={16} />}
            color={isFavorited ? "yellow" : "gray"}
            loading={favoriteLoading}
            onClick={async () => {
              setFavoriteLoading(true);
              try {
                if (isFavorited) {
                  await removeFavorite(address, numericPort);
                  setIsFavorited(false);
                } else {
                  await addFavorite(address, numericPort, server?.hostname);
                  setIsFavorited(true);
                }
              } catch (error) {
                console.error("Failed to toggle favorite:", error);
              } finally {
                setFavoriteLoading(false);
              }
            }}
          >
            {isFavorited ? "Remove from Favorites" : "Add to Favorites"}
          </Button>
        </Group>

        {server ? (
          <>
            <Title order={2} mb="md">{server.hostname || address}</Title>

            <Grid>
              <Grid.Col span={12} md={6}>
                <Card withBorder p="md" radius="md">
                  <Title order={3} mb="md">Server Information</Title>
                  <Table>
                    <Table.Tbody>
                      <Table.Tr>
                        <Table.Td><Text fw={700}>Address</Text></Table.Td>
                        <Table.Td>{server.address}:{server.port}</Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td><Text fw={700}>Hostname</Text></Table.Td>
                        <Table.Td>{server.hostname || 'Unknown'}</Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td><Text fw={700}>Map</Text></Table.Td>
                        <Table.Td>
                          {(() => {
                            // Clean up map name if it contains player data
                            const mapString = server.map || 'Unknown';
                            // Check if the map string contains player data (common format: mapname num num num num "name" etc.)
                            const mapParts = mapString.split(' ');
                            if (mapParts.length > 1 && !mapParts[0].includes('/')) {
                              // If it's likely a map name followed by player data, just return the first part
                              return mapParts[0];
                            }
                            return mapString;
                          })()}
                        </Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td><Text fw={700}>Players</Text></Table.Td>
                        <Table.Td>
                          <Badge>
                            {server.players ? server.players.length : 0} / {server.maxclients || '?'}
                          </Badge>
                        </Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td><Text fw={700}>Ping</Text></Table.Td>
                        <Table.Td>
                          <Badge
                            color={server.ping < 100 ? 'green' : server.ping < 200 ? 'yellow' : 'red'}
                          >
                            {server.ping || '?'} ms
                          </Badge>
                        </Table.Td>
                      </Table.Tr>
                      <Table.Tr>
                        <Table.Td><Text fw={700}>Last Updated</Text></Table.Td>
                        <Table.Td>
                          {server.last_updated
                            ? new Date(server.last_updated).toLocaleString()
                            : 'Unknown'}
                        </Table.Td>
                      </Table.Tr>
                    </Table.Tbody>
                  </Table>
                </Card>
              </Grid.Col>

              <Grid.Col span={12} md={6}>
                <Card withBorder p="md" radius="md">
                  <Title order={3} mb="md">Additional Information</Title>
                  <Table>
                    <Table.Tbody>
                      {Object.entries(server.other_info || {}).map(([key, value]) => (
                        <Table.Tr key={key}>
                          <Table.Td><Text fw={700}>{key}</Text></Table.Td>
                          <Table.Td>{value}</Table.Td>
                        </Table.Tr>
                      ))}
                    </Table.Tbody>
                  </Table>
                </Card>
              </Grid.Col>
            </Grid>

            <Card withBorder p="md" radius="md" mt="md">
              <Title order={3} mb="md">Players ({server.players ? server.players.length : 0})</Title>
              {server.players && server.players.length > 0 ? (
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Name</Table.Th>
                      <Table.Th>Frags</Table.Th>
                      <Table.Th>Time</Table.Th>
                      <Table.Th>Ping</Table.Th>
                      <Table.Th>Team</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {server.players.map((player, index) => (
                      <Table.Tr key={index}>
                        <Table.Td>{player.name}</Table.Td>
                        <Table.Td>{player.frags}</Table.Td>
                        <Table.Td>{formatTime(player.time)}</Table.Td>
                        <Table.Td>
                          <Badge
                            color={player.ping < 50 ? 'green' : player.ping < 100 ? 'yellow' : 'red'}
                          >
                            {player.ping} ms
                          </Badge>
                        </Table.Td>
                        <Table.Td>{player.team || '-'}</Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              ) : (
                <Text>No players currently on the server.</Text>
              )}
            </Card>
          </>
        ) : (
          <Paper p="md" withBorder>
            <Text>Loading server details...</Text>
          </Paper>
        )}
      </Box>
    </Container>
  );
}

// Helper function to format time in minutes:seconds
function formatTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

export default ServerDetails;
