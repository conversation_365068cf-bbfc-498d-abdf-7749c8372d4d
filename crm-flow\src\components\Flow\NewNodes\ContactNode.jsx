import { memo } from 'react';
import { Handle, Position } from '@xyflow/react';
import NodeMenu from './NodeMenu';

const ContactNode = ({ data, selected }) => {
  const formatDate = (date) => {
    if (!date) return 'Unknown';
    return typeof date === 'object' && date.toDate
      ? date.toDate().toLocaleDateString()
      : new Date(date).toLocaleDateString();
  };

  // Handle node actions
  const handleEdit = () => {
    if (data.onEdit) data.onEdit(data.id);
  };

  const handleDelete = () => {
    if (data.onDelete) data.onDelete(data.id);
  };

  const handleArchive = () => {
    if (data.onArchive) data.onArchive(data.id);
  };

  const handleRestore = () => {
    if (data.onRestore) data.onRestore(data.id);
  };

  return (
    <div className={`node-card node-contact ${data.archived ? 'node-archived' : ''}`}>
      <NodeMenu
        onEdit={handleEdit}
        onDelete={handleDelete}
        onArchive={handleArchive}
        isArchived={data.archived}
        onRestore={handleRestore}
        nodeType="contact"
      />
      <div className="node-header">
        <h6 className="node-title">Contact</h6>
      </div>
      <div className="node-body">
        <h5 className="node-name">{`${data.firstName || ''} ${data.lastName || ''}`.trim() || 'Unnamed Contact'}</h5>
        <p className="node-detail">Title: {data.title || 'N/A'}</p>
        <p className="node-detail">Email: {data.email || 'N/A'}</p>
        <p className="node-detail">Phone: {data.phone || 'N/A'}</p>
        <p className="node-date">Created: {formatDate(data.createdAt)}</p>
      </div>
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#555' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: '#555' }}
      />
    </div>
  );
};

export default memo(ContactNode);
