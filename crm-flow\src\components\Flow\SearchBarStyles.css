.flow-search-container {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 500px;
  max-width: 90%;
  z-index: 10;
}

.flow-search-bar {
  display: flex;
  align-items: center;
  background-color: rgba(40, 40, 40, 0.9);
  border-radius: 24px;
  padding: 8px 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.flow-search-bar.active {
  background-color: rgba(50, 50, 50, 0.95);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.2);
}

.flow-search-icon {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 10px;
  font-size: 16px;
}

.flow-search-input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-size: 16px;
  outline: none;
  padding: 6px 0;
}

.flow-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.flow-search-clear {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.flow-search-clear:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.flow-search-results {
  margin-top: 8px;
  background-color: rgba(40, 40, 40, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.flow-search-result {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.flow-search-result:last-child {
  border-bottom: none;
}

.flow-search-result:hover,
.flow-search-result.selected {
  background-color: rgba(59, 130, 246, 0.3);
}

.flow-search-result-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flow-search-result-content {
  flex: 1;
  overflow: hidden;
}

.flow-search-result-title {
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flow-search-result-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Scrollbar styling */
.flow-search-results::-webkit-scrollbar {
  width: 6px;
}

.flow-search-results::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.flow-search-results::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.flow-search-results::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .flow-search-container {
    width: 90%;
  }
  
  .flow-search-bar {
    padding: 6px 12px;
  }
  
  .flow-search-input {
    font-size: 14px;
  }
}
