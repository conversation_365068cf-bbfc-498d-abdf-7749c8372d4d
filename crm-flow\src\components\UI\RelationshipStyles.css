/* Custom styles for relationships */

/* Breadcrumb container */
.relationship-badge .breadcrumb-container {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 0.5rem;
  padding: 0.25rem 0.5rem;
  margin-bottom: 0.25rem;
}

/* Relationship type */
.relationship-badge .relationship-type {
  font-size: 0.85rem;
  white-space: nowrap;
}

/* Arrow between type and entity */
.relationship-badge .relationship-arrow {
  margin: 0 0.5rem;
  font-size: 0.7rem;
}

/* Entity badge */
.relationship-badge .relationship-entity {
  display: flex;
  align-items: center;
}

/* Make sure the link is clearly clickable */
.relationship-badge .relationship-entity .badge {
  cursor: pointer;
  transition: all 0.2s ease;
}

.relationship-badge .relationship-entity .badge:hover {
  filter: brightness(110%);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.relationships-list h6 {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.relationships-list .badge {
  font-weight: normal;
  padding: 0.35em 0.65em;
}

/* Dark theme adjustments */
body.dark-theme .relationship-badge .breadcrumb-container {
  background-color: rgba(255, 255, 255, 0.05);
}

body.dark-theme .relationship-badge .relationship-type small {
  color: #adb5bd !important;
}

body.dark-theme .relationship-badge .relationship-arrow i {
  color: #6c757d !important;
}

body.dark-theme .relationship-badge .relationship-entity .badge:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body.dark-theme .relationships-list h6 {
  color: #adb5bd;
}