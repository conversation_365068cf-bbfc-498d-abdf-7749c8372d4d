import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/core";
import { MantineProvider, createTheme, AppShell } from "@mantine/core";
import { Notifications, notifications } from '@mantine/notifications';
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "@mantine/core/styles.css";
import "@mantine/notifications/styles.css";
import "./App.css";

// Import components
import ServerList from "./components/ServerList";
import ServerDetails from "./components/ServerDetails";
import AddServer from "./components/AddServer";
import AppHeader from "./components/AppHeader";
import Favorites from "./components/Favorites";
import Settings from "./components/Settings";

// Create theme
const theme = createTheme({
  primaryColor: "blue",
  defaultRadius: "md",
  colorScheme: "dark",
});

function App() {
  const [servers, setServers] = useState([]);
  const [favorites, setFavorites] = useState([]);
  const [masterServers, setMasterServers] = useState([]);
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);
  const [refreshProgress, setRefreshProgress] = useState(0);
  const [totalServersToRefresh, setTotalServersToRefresh] = useState(0);
  const [cancelRefreshFlag, setCancelRefreshFlag] = useState(false);

  // Load data on startup
  useEffect(() => {
    loadServers();
    loadFavorites();
    loadMasterServers();
    loadSettings();
  }, []);

  // Function to load servers from backend
  const loadServers = async () => {
    try {
      setLoading(true);
      const serverList = await invoke("get_servers");
      setServers(serverList);
    } catch (error) {
      console.error("Failed to load servers:", error);
    } finally {
      setLoading(false);
    }
  };

  // Function to save servers to disk
  const saveServers = async () => {
    try {
      await invoke("save_servers");
      console.log("Servers saved successfully");
    } catch (error) {
      console.error("Failed to save servers:", error);
    }
  };

  // Function to load favorite servers
  const loadFavorites = async () => {
    try {
      const favoritesList = await invoke("get_favorite_servers");
      setFavorites(favoritesList);
    } catch (error) {
      console.error("Failed to load favorites:", error);
    }
  };

  // Function to load master servers
  const loadMasterServers = async () => {
    try {
      const masterServersList = await invoke("get_master_servers");
      setMasterServers(masterServersList);
    } catch (error) {
      console.error("Failed to load master servers:", error);
    }
  };

  // Function to load settings
  const loadSettings = async () => {
    try {
      const appSettings = await invoke("get_settings");
      setSettings(appSettings);
    } catch (error) {
      console.error("Failed to load settings:", error);
    }
  };

  // Function to add a server
  const addServer = async (address, port) => {
    try {
      setLoading(true);
      const serverInfo = await invoke("query_server", { address, port });
      await loadServers();
      await saveServers(); // Save servers after adding a new one
      return serverInfo;
    } catch (error) {
      console.error("Failed to add server:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Function to remove a server
  const removeServer = async (address, port) => {
    try {
      setLoading(true);
      await invoke("remove_server", { address, port });
      await loadServers();
      await saveServers(); // Save servers after removing one
    } catch (error) {
      console.error("Failed to remove server:", error);
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh a server
  const refreshServer = async (address, port) => {
    try {
      setLoading(true);
      await invoke("query_server", { address, port });
      await loadServers();
      await saveServers(); // Save servers after refreshing one
    } catch (error) {
      console.error("Failed to refresh server:", error);
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh all favorite servers
  const refreshFavorites = async () => {
    try {
      setLoading(true);
      setRefreshProgress(0);
      const serversToRefresh = favorites.length;
      setTotalServersToRefresh(serversToRefresh);

      let completedServers = 0;

      // Process servers sequentially to track progress
      for (const server of favorites) {
        try {
          await invoke("query_server", { address: server.address, port: server.port });
          completedServers++;
          setRefreshProgress(Math.floor((completedServers / serversToRefresh) * 100));
        } catch (error) {
          console.error(`Failed to refresh server ${server.address}:${server.port}:`, error);
          completedServers++;
          setRefreshProgress(Math.floor((completedServers / serversToRefresh) * 100));
        }
      }

      await loadServers();
    } catch (error) {
      console.error("Failed to refresh favorite servers:", error);
    } finally {
      setLoading(false);
      setRefreshProgress(0);
      setTotalServersToRefresh(0);
    }
  };

  // Function to refresh all servers
  const refreshAllServers = async () => {
    try {
      setLoading(true);
      setRefreshProgress(0);
      setCancelRefreshFlag(false);
      const serversToRefresh = servers.length;
      setTotalServersToRefresh(serversToRefresh);

      let completedServers = 0;

      // Process servers sequentially to track progress
      for (const server of servers) {
        // Check if cancellation was requested
        if (cancelRefreshFlag) {
          console.log("Refresh operation cancelled by user");
          break;
        }

        try {
          await invoke("query_server", { address: server.address, port: server.port });
          completedServers++;
          setRefreshProgress(Math.floor((completedServers / serversToRefresh) * 100));
        } catch (error) {
          console.error(`Failed to refresh server ${server.address}:${server.port}:`, error);
          completedServers++;
          setRefreshProgress(Math.floor((completedServers / serversToRefresh) * 100));
        }
      }

      await loadServers();
    } catch (error) {
      console.error("Failed to refresh all servers:", error);
    } finally {
      setLoading(false);
      setRefreshProgress(0);
      setTotalServersToRefresh(0);
      setCancelRefreshFlag(false);
    }
  };

  // Function to cancel refresh
  const cancelRefresh = () => {
    setCancelRefreshFlag(true);
    notifications.show({
      title: 'Refresh Cancelled',
      message: 'Server refresh operation has been cancelled',
      color: 'blue',
      autoClose: 3000
    });
  };

  // Function to refresh servers from master servers
  const refreshMasterServers = async () => {
    try {
      setLoading(true);
      setRefreshProgress(0);
      setCancelRefreshFlag(false);

      // Query master servers for server list
      const serverAddresses = await invoke("query_master_servers");
      setTotalServersToRefresh(serverAddresses.length);

      let completedServers = 0;

      // Process each server address
      for (const addressString of serverAddresses) {
        // Check if cancellation was requested
        if (cancelRefreshFlag) {
          console.log("Refresh operation cancelled by user");
          break;
        }

        try {
          // Parse address string (format: "ip:port")
          const [address, portStr] = addressString.split(':');
          const port = parseInt(portStr, 10);

          // Query each server
          await invoke("query_server", { address, port });
        } catch (error) {
          console.error(`Failed to query server ${addressString}:`, error);
        } finally {
          completedServers++;
          setRefreshProgress(Math.floor((completedServers / serverAddresses.length) * 100));
        }
      }

      // Reload server list
      await loadServers();
      await saveServers(); // Save servers after refreshing from master servers
    } catch (error) {
      console.error("Failed to refresh from master servers:", error);
    } finally {
      setLoading(false);
      setRefreshProgress(0);
      setTotalServersToRefresh(0);
      setCancelRefreshFlag(false);
    }
  };

  // Function to add a favorite server
  const addFavorite = async (address, port, name, notes) => {
    try {
      await invoke("add_favorite_server", { address, port, name, notes });
      await loadFavorites();
    } catch (error) {
      console.error("Failed to add favorite:", error);
      throw error;
    }
  };

  // Function to remove a favorite server
  const removeFavorite = async (address, port) => {
    try {
      await invoke("remove_favorite_server", { address, port });
      await loadFavorites();
    } catch (error) {
      console.error("Failed to remove favorite:", error);
      throw error;
    }
  };

  // Function to check if a server is a favorite
  const isFavorite = async (address, port) => {
    try {
      return await invoke("is_favorite_server", { address, port });
    } catch (error) {
      console.error("Failed to check if server is a favorite:", error);
      return false;
    }
  };

  // Function to save settings
  const saveSettings = async (newSettings) => {
    try {
      await invoke("save_settings", { settings: newSettings });
      setSettings(newSettings);
    } catch (error) {
      console.error("Failed to save settings:", error);
      throw error;
    }
  };

  // Function to import servers from a URL
  const importServersFromUrl = async (url) => {
    try {
      setLoading(true);
      setRefreshProgress(0);
      setCancelRefreshFlag(false);

      // Use our backend command to fetch the server list
      const serverList = await invoke("fetch_server_list", { url });

      // Set total servers to refresh
      const serversToRefresh = serverList.length;
      setTotalServersToRefresh(serversToRefresh);

      let completedServers = 0;
      let successCount = 0;

      // Process each server
      for (const serverAddress of serverList) {
        // Check if cancellation was requested
        if (cancelRefreshFlag) {
          console.log("Import operation cancelled by user");
          break;
        }

        try {
          // Extract address and port
          const [address, portStr] = serverAddress.split(':');
          const port = parseInt(portStr, 10);

          if (!address || isNaN(port)) {
            console.error(`Invalid server address format: ${serverAddress}`);
            completedServers++;
            setRefreshProgress(Math.floor((completedServers / serversToRefresh) * 100));
            continue;
          }

          // Query the server
          await invoke("query_server", { address, port });
          successCount++;
        } catch (error) {
          console.error(`Failed to query server ${serverAddress}:`, error);
        } finally {
          completedServers++;
          setRefreshProgress(Math.floor((completedServers / serversToRefresh) * 100));
        }
      }

      // Reload server list
      await loadServers();
      await saveServers(); // Save servers after importing from URL

      // Show notification
      if (!cancelRefreshFlag) {
        notifications.show({
          title: 'Server Import Complete',
          message: `Successfully imported ${successCount} out of ${serversToRefresh} servers`,
          color: successCount > 0 ? 'green' : 'red',
          autoClose: 5000
        });
      }

      return successCount;
    } catch (error) {
      console.error("Failed to import servers from URL:", error);

      // Show error notification
      notifications.show({
        title: 'Import Failed',
        message: `Failed to import servers: ${error.message || error}`,
        color: 'red',
        autoClose: 5000
      });

      throw error;
    } finally {
      setLoading(false);
      setRefreshProgress(0);
      setTotalServersToRefresh(0);
      setCancelRefreshFlag(false);
    }
  };

  return (
    <MantineProvider theme={theme}>
      <Notifications position="top-right" />
      <Router>
        <AppShell
          header={{ height: 50 }}
          padding={0}
        >
          <AppHeader
            refreshAllServers={refreshAllServers}
            refreshMasterServers={refreshMasterServers}
            loading={loading}
            cancelRefresh={cancelRefresh}
          />
          <AppShell.Main>
            <Routes>
              <Route
                path="/"
                element={
                  <ServerList
                    servers={servers}
                    removeServer={removeServer}
                    refreshServer={refreshServer}
                    addFavorite={addFavorite}
                    removeFavorite={removeFavorite}
                    isFavorite={isFavorite}
                    loading={loading}
                    refreshProgress={refreshProgress}
                    totalServersToRefresh={totalServersToRefresh}
                  />
                }
              />
              <Route
                path="/server/:address/:port"
                element={
                  <ServerDetails
                    refreshServer={refreshServer}
                    addFavorite={addFavorite}
                    removeFavorite={removeFavorite}
                    isFavorite={isFavorite}
                  />
                }
              />
              <Route
                path="/add"
                element={
                  <AddServer
                    addServer={addServer}
                    loading={loading}
                    importServersFromUrl={importServersFromUrl}
                    refreshProgress={refreshProgress}
                    totalServersToRefresh={totalServersToRefresh}
                  />
                }
              />
              <Route
                path="/favorites"
                element={
                  <Favorites
                    favorites={favorites}
                    refreshServer={refreshServer}
                    refreshFavorites={refreshFavorites}
                    removeFavorite={removeFavorite}
                    loading={loading}
                    refreshProgress={refreshProgress}
                    totalServersToRefresh={totalServersToRefresh}
                  />
                }
              />
              <Route
                path="/settings"
                element={
                  <Settings
                    settings={settings}
                    saveSettings={saveSettings}
                    masterServers={masterServers}
                    addMasterServer={(address, port) => invoke("add_master_server", { address, port })}
                    updateMasterServer={(address, port, enabled) => invoke("update_master_server", { address, port, enabled })}
                    deleteMasterServer={(address, port) => invoke("delete_master_server", { address, port })}
                    loadMasterServers={loadMasterServers}
                  />
                }
              />
            </Routes>
          </AppShell.Main>
        </AppShell>
      </Router>
    </MantineProvider>
  );
}

export default App;
