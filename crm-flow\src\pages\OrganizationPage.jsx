import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import Modal from '../components/UI/Modal';
import MemberManagement from '../components/Organization/MemberManagement';
import {
  createOrganization,
  getOrganization,
  updateOrganization,
  getAllUserOrganizations,
  deleteOrganization
} from '../services/organizationService';
import {
  SUBSCRIPTION_PLANS,
  PLAN_LIMITS,
  updateSubscriptionPlan
} from '../services/subscriptionService';
import 'bootstrap/dist/css/bootstrap.min.css';
import '../components/UI/AlertStyles.css';

// For debugging
const DEBUG = true;

const OrganizationPage = () => {
  const { currentUser, userProfile, organization, subscription, refreshUserProfile, switchOrganization } = useAuth();
  const [loading, setLoading] = useState(true);
  const [allOrganizations, setAllOrganizations] = useState([]);
  const [showCreateOrgModal, setShowCreateOrgModal] = useState(false);
  const [showEditOrgModal, setShowEditOrgModal] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [orgToDelete, setOrgToDelete] = useState(null);
  const [orgFormData, setOrgFormData] = useState({ name: '', industry: '', size: 'Small' });
  const [statusMessage, setStatusMessage] = useState('');
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch all organizations the user is a member of
  useEffect(() => {
    const fetchUserOrganizations = async () => {
      if (currentUser) {
        try {
          if (DEBUG) console.log('Fetching all organizations for user:', currentUser.uid);
          const organizations = await getAllUserOrganizations(currentUser.uid);
          if (DEBUG) console.log('Fetched organizations:', organizations);
          setAllOrganizations(organizations);
        } catch (error) {
          console.error('Error fetching user organizations:', error);
        }
      }
    };

    fetchUserOrganizations();
  }, [currentUser]);

  useEffect(() => {
    // Set loading to false when organization data is loaded
    if (DEBUG) console.log('OrganizationPage useEffect - organization:', organization, 'currentUser:', currentUser?.uid);
    if ((organization || !currentUser) && allOrganizations !== null) {
      if (DEBUG) console.log('Setting loading to false');
      setLoading(false);

      // Initialize form data with organization data if available
      if (organization) {
        setOrgFormData({
          name: organization.name || '',
          industry: organization.industry || '',
          size: organization.size || 'Small'
        });
      }
    }
  }, [organization, currentUser, allOrganizations]);

  const handleCreateOrganization = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Validate organization name
      if (!orgFormData.name || orgFormData.name.trim() === '') {
        setError('Organization name is required');
        setLoading(false);
        return;
      }

      console.log('Creating organization with data:', orgFormData);
      console.log('Current user ID:', currentUser?.uid);

      const result = await createOrganization(currentUser.uid, orgFormData);
      console.log('Organization creation result:', result);

      if (result.success) {
        setStatusMessage('Organization created successfully!');
        setShowCreateOrgModal(false);
        // Reset form data
        setOrgFormData({ name: '', industry: '', size: 'Small' });
        // Refresh user profile to get updated organization data
        await refreshUserProfile();
        // Reload the page to show the new organization
        window.location.reload();
      } else {
        setError(result.error || 'Failed to create organization');
      }
    } catch (error) {
      console.error('Error creating organization:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateOrganization = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Validate organization name
      if (!orgFormData.name || orgFormData.name.trim() === '') {
        setError('Organization name is required');
        setLoading(false);
        return;
      }

      if (!organization || !organization.id) {
        setError('No organization found to update');
        setLoading(false);
        return;
      }

      console.log('Updating organization with data:', orgFormData);
      const result = await updateOrganization(organization.id, orgFormData);
      console.log('Organization update result:', result);

      if (result.success) {
        setStatusMessage('Organization updated successfully!');
        setShowEditOrgModal(false);
        // Refresh user profile to get updated organization data
        await refreshUserProfile();
        // Reload the page to show the updated organization
        window.location.reload();
      } else {
        setError(result.error || 'Failed to update organization');
      }
    } catch (error) {
      console.error('Error updating organization:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddMember = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Check if we've reached the member limit
      if (subscription && members.length >= subscription.limits.maxMembers) {
        setError(`You've reached the maximum number of members (${subscription.limits.maxMembers}) for your current plan. Please upgrade to add more members.`);
        setLoading(false);
        return;
      }

      // In a real app, you would need to look up the user by email
      // For demo purposes, we'll create a mock user with a persistent ID
      const mockUserId = `demo-user-${Date.now()}`;
      const mockUser = {
        id: mockUserId,
        name: memberFormData.email.split('@')[0], // Use part of email as name
        email: memberFormData.email,
        role: memberFormData.role,
        photoURL: `https://ui-avatars.com/api/?name=${encodeURIComponent(memberFormData.email.split('@')[0])}&background=random`,
        createdAt: new Date()
      };

      // Create a user document in Firestore for the mock user
      const { collection, doc, setDoc } = await import('firebase/firestore');
      const { db } = await import('../services/firebase');

      // Add the user to Firestore
      await setDoc(doc(db, 'users', mockUserId), {
        name: mockUser.name,
        email: mockUser.email,
        photoURL: mockUser.photoURL,
        role: mockUser.role,
        organizationId: organization.id,
        createdAt: new Date()
      });

      // Add the user to the organization
      const result = await addOrganizationMember(organization.id, mockUserId, mockUser.role);

      if (result.success) {
        // Add the mock user to the members list
        setMembers([...members, mockUser]);

        // Show success message
        setStatusMessage(`Team member ${mockUser.name} added successfully!`);
        setShowAddMemberModal(false);

        // Reset form
        setMemberFormData({ email: '', role: 'member' });
      } else {
        setError(result.error || 'Failed to add member');
      }
    } catch (error) {
      console.error('Error adding member:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveMember = async (memberId) => {
    if (!confirm('Are you sure you want to remove this member?')) {
      return;
    }

    setError('');
    setLoading(true);

    try {
      // Don't allow removing yourself
      if (memberId === currentUser.uid) {
        setError('You cannot remove yourself from the organization. Please contact another organization owner.');
        setLoading(false);
        return;
      }

      // Don't allow non-owners to remove members
      if (userProfile?.role !== 'owner') {
        setError('Only organization owners can remove members.');
        setLoading(false);
        return;
      }

      const result = await removeOrganizationMember(organization.id, memberId);

      if (result.success) {
        setStatusMessage('Member removed successfully!');
        // Update the members list
        setMembers(members.filter(member => member.id !== memberId));
      } else {
        setError(result.error || 'Failed to remove member');
      }
    } catch (error) {
      console.error('Error removing member:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeSubscription = async (plan) => {
    setError('');
    setLoading(true);

    try {
      const result = await updateSubscriptionPlan(organization.id, plan);

      if (result.success) {
        setStatusMessage(`Subscription upgraded to ${plan} successfully!`);
        setShowUpgradeModal(false);
        // Refresh user profile to get updated subscription data
        await refreshUserProfile();
      } else {
        setError(result.error || 'Failed to upgrade subscription');
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle switching to a different organization
  const handleSwitchOrganization = async (orgId) => {
    setError('');
    setLoading(true);

    try {
      if (DEBUG) console.log('Switching to organization:', orgId);
      const success = await switchOrganization(orgId);

      if (success) {
        setStatusMessage('Successfully switched organization');
        // Refresh the page to update all components with the new organization context
        window.location.reload();
      } else {
        setError('Failed to switch organization');
        setLoading(false);
      }
    } catch (error) {
      console.error('Error switching organization:', error);
      setError(error.message);
      setLoading(false);
    }
  };

  // Handle organization deletion confirmation
  const handleDeleteOrganizationConfirm = (org) => {
    setOrgToDelete(org);
    setShowDeleteConfirmModal(true);
  };

  // Handle organization deletion
  const handleDeleteOrganization = async () => {
    if (!orgToDelete || !currentUser) return;

    setIsDeleting(true);
    setError('');
    setStatusMessage('');

    try {
      const result = await deleteOrganization(orgToDelete.id, currentUser.uid);

      if (result.success) {
        setStatusMessage('Organization deleted successfully!');
        setShowDeleteConfirmModal(false);
        setOrgToDelete(null);

        // Refresh user profile to get updated organization data
        await refreshUserProfile();

        // Remove the deleted organization from the list
        setAllOrganizations(allOrganizations.filter(org => org.id !== orgToDelete.id));

        // If the deleted organization was the current one, reload the page
        if (organization && organization.id === orgToDelete.id) {
          window.location.reload();
        }
      } else {
        setError(result.error || 'Failed to delete organization');
      }
    } catch (error) {
      console.error('Error deleting organization:', error);
      setError(error.message);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div style={{ height: '100%', width: '100%', overflow: 'auto', padding: '20px' }}>
      <div className="container-fluid">
        <h2 className="mb-4">Organization Management</h2>

        {statusMessage && (
          <div className="alert alert-success alert-dismissible fade show" role="alert">
            <i className="bi bi-check-circle-fill me-2"></i>
            {statusMessage}
            <button type="button" className="btn-close" onClick={() => setStatusMessage('')} aria-label="Close"></button>
          </div>
        )}

        {error && (
          <div className="alert alert-danger alert-dismissible fade show" role="alert">
            <i className="bi bi-exclamation-triangle-fill me-2"></i>
            {error}
            <button type="button" className="btn-close" onClick={() => setError('')} aria-label="Close"></button>
          </div>
        )}

        {loading ? (
          <div className="d-flex justify-content-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : allOrganizations.length === 0 ? (
          <div className="card">
            <div className="card-body">
              <h5 className="card-title">You don't have an organization yet</h5>
              <p className="card-text">Create an organization to collaborate with your team and manage your CRM data.</p>
              <button
                className="btn btn-primary"
                onClick={() => setShowCreateOrgModal(true)}
              >
                Create Organization
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h3>Current Organization: {organization?.name}</h3>
              <button
                className="btn btn-primary"
                onClick={() => {
                  setOrgFormData({ name: '', industry: '', size: 'Small' });
                  setShowCreateOrgModal(true);
                }}
              >
                <i className="bi bi-plus-circle me-2"></i>
                Add Another Organization
              </button>
            </div>

            {/* All Organizations Section */}
            <div className="row mb-4">
              <div className="col-12">
                <div className="card">
                  <div className="card-header">
                    <h5 className="mb-0">Your Organizations</h5>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      {allOrganizations.map(org => (
                        <div key={org.id} className="col-md-6 col-lg-4 mb-3">
                          <div className={`card h-100 ${organization?.id === org.id ? 'border-primary' : ''}`}>
                            <div className="card-body">
                              <div className="d-flex justify-content-between align-items-start">
                                <h5 className="card-title">{org.name}</h5>
                                {org.isOwner && (
                                  <span className="badge bg-danger">Owner</span>
                                )}
                                {!org.isOwner && (
                                  <span className="badge bg-info">Member</span>
                                )}
                              </div>
                              <p className="card-text text-muted small mb-2">
                                {org.industry || 'No industry specified'}
                              </p>
                              <p className="card-text text-muted small mb-3">
                                Size: {org.size || 'Not specified'}
                              </p>
                              <p className="card-text text-muted small mb-3">
                                Members: {org.members?.length || 0}
                              </p>
                              {organization?.id === org.id ? (
                                <div className="d-grid">
                                  <button className="btn btn-outline-primary" disabled>
                                    <i className="bi bi-check-circle-fill me-2"></i>
                                    Current Organization
                                  </button>
                                </div>
                              ) : (
                                <div className="d-grid">
                                  <button
                                    className="btn btn-outline-secondary"
                                    onClick={() => handleSwitchOrganization(org.id)}
                                  >
                                    <i className="bi bi-arrow-right-circle me-2"></i>
                                    Switch to this Organization
                                  </button>
                                </div>
                              )}

                              {/* Delete button for organization owners */}
                              {org.isOwner && (
                                <div className="mt-2">
                                  <button
                                    className="btn btn-outline-danger btn-sm w-100"
                                    onClick={() => handleDeleteOrganizationConfirm(org)}
                                  >
                                    <i className="bi bi-trash me-2"></i>
                                    Delete Organization
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="row">
              <div className="col-md-6">
                <div className="card mb-4">
                  <div className="card-header d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">Organization Details</h5>
                    {userProfile?.role === 'owner' && (
                      <button
                        className="btn btn-sm btn-outline-primary"
                        onClick={() => setShowEditOrgModal(true)}
                      >
                        Edit
                      </button>
                    )}
                  </div>
                  <div className="card-body">
                    <h3>{organization.name}</h3>
                    <p><strong>Industry:</strong> {organization.industry}</p>
                    <p><strong>Size:</strong> {organization.size}</p>
                    <p><strong>Owner:</strong> {organization.ownerId === currentUser?.uid ? userProfile?.name : 'Unknown'}</p>

                    <div className="mt-4">
                      <h6 className="mb-2">Organization UUID</h6>
                      <div className="input-group">
                        <input
                          type="text"
                          className="form-control"
                          value={organization.id}
                          readOnly
                        />
                        <button
                          className="btn btn-outline-secondary"
                          type="button"
                          onClick={() => {
                            navigator.clipboard.writeText(organization.id);
                            setStatusMessage('Organization UUID copied to clipboard!');
                          }}
                        >
                          <i className="bi bi-clipboard"></i>
                        </button>
                      </div>
                      <small className="text-muted mt-2 d-block">
                        Share this UUID with team members so they can join your organization.
                        They can paste this in their User Settings page.
                      </small>
                    </div>
                  </div>
                </div>

                <div className="card mb-4">
                  <div className="card-header">
                    <h5 className="mb-0">Subscription</h5>
                  </div>
                  <div className="card-body">
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <div>
                        <h4 className="mb-0">{subscription?.plan.toUpperCase() || 'Free'} Plan</h4>
                        <p className="text-muted mb-0">
                          {subscription?.status === 'active' ? 'Active' : 'Inactive'}
                        </p>
                      </div>
                      {userProfile?.role === 'owner' && (
                        <button
                          className="btn btn-success"
                          onClick={() => setShowUpgradeModal(true)}
                        >
                          Upgrade
                        </button>
                      )}
                    </div>

                    <h6>Plan Limits:</h6>
                    <ul className="list-group">
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Members
                        <span className="badge bg-primary rounded-pill">
                          {organization?.members?.length || 0} / {subscription?.limits.maxMembers || 1}
                        </span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Contacts
                        <span className="badge bg-primary rounded-pill">
                          {/* TODO: Add actual counts */}
                          0 / {subscription?.limits.maxContacts || 50}
                        </span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Companies
                        <span className="badge bg-primary rounded-pill">
                          {/* TODO: Add actual counts */}
                          0 / {subscription?.limits.maxCompanies || 20}
                        </span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Opportunities
                        <span className="badge bg-primary rounded-pill">
                          {/* TODO: Add actual counts */}
                          0 / {subscription?.limits.maxOpportunities || 20}
                        </span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Tasks
                        <span className="badge bg-primary rounded-pill">
                          {/* TODO: Add actual counts */}
                          0 / {subscription?.limits.maxTasks || 50}
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="col-md-6">
                <div className="card">
                  <div className="card-header">
                    <h5 className="mb-0">Team Members</h5>
                  </div>
                  <div className="card-body">
                    <MemberManagement />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Create Organization Modal */}
      <Modal
        show={showCreateOrgModal}
        onClose={() => setShowCreateOrgModal(false)}
        title="Create Organization"
      >
        <form onSubmit={handleCreateOrganization}>
          <div className="mb-3">
            <label htmlFor="orgName" className="form-label">Organization Name</label>
            <input
              type="text"
              className="form-control"
              id="orgName"
              value={orgFormData.name}
              onChange={(e) => setOrgFormData({ ...orgFormData, name: e.target.value })}
              required
            />
          </div>
          <div className="mb-3">
            <label htmlFor="orgIndustry" className="form-label">Industry</label>
            <input
              type="text"
              className="form-control"
              id="orgIndustry"
              value={orgFormData.industry}
              onChange={(e) => setOrgFormData({ ...orgFormData, industry: e.target.value })}
            />
          </div>
          <div className="mb-3">
            <label htmlFor="orgSize" className="form-label">Size</label>
            <select
              className="form-select"
              id="orgSize"
              value={orgFormData.size}
              onChange={(e) => setOrgFormData({ ...orgFormData, size: e.target.value })}
            >
              <option value="Small">Small (1-50 employees)</option>
              <option value="Medium">Medium (51-200 employees)</option>
              <option value="Large">Large (201-1000 employees)</option>
              <option value="Enterprise">Enterprise (1000+ employees)</option>
            </select>
          </div>
          <div className="d-grid">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Creating...
                </>
              ) : 'Create Organization'}
            </button>

          </div>
        </form>
      </Modal>

      {/* Edit Organization Modal */}
      <Modal
        show={showEditOrgModal}
        onClose={() => setShowEditOrgModal(false)}
        title="Edit Organization"
      >
        <form onSubmit={handleUpdateOrganization}>
          <div className="mb-3">
            <label htmlFor="editOrgName" className="form-label">Organization Name</label>
            <input
              type="text"
              className="form-control"
              id="editOrgName"
              value={orgFormData.name}
              onChange={(e) => setOrgFormData({ ...orgFormData, name: e.target.value })}
              required
            />
          </div>
          <div className="mb-3">
            <label htmlFor="editOrgIndustry" className="form-label">Industry</label>
            <input
              type="text"
              className="form-control"
              id="editOrgIndustry"
              value={orgFormData.industry}
              onChange={(e) => setOrgFormData({ ...orgFormData, industry: e.target.value })}
            />
          </div>
          <div className="mb-3">
            <label htmlFor="editOrgSize" className="form-label">Size</label>
            <select
              className="form-select"
              id="editOrgSize"
              value={orgFormData.size}
              onChange={(e) => setOrgFormData({ ...orgFormData, size: e.target.value })}
            >
              <option value="Small">Small (1-50 employees)</option>
              <option value="Medium">Medium (51-200 employees)</option>
              <option value="Large">Large (201-1000 employees)</option>
              <option value="Enterprise">Enterprise (1000+ employees)</option>
            </select>
          </div>
          <div className="d-grid">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Updating...
                </>
              ) : 'Update Organization'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Upgrade Subscription Modal */}
      <Modal
        show={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        title="Upgrade Subscription"
        size="lg"
      >
        <div className="row">
          {Object.entries(SUBSCRIPTION_PLANS).map(([key, plan]) => (
            <div key={plan} className="col-md-3 mb-3">
              <div className={`card h-100 ${subscription?.plan === plan ? 'border-primary' : ''}`}>
                <div className="card-header text-center">
                  <h5 className="mb-0">{key}</h5>
                </div>
                <div className="card-body d-flex flex-column">
                  <h3 className="text-center mb-3">
                    {plan === SUBSCRIPTION_PLANS.FREE ? 'Free' : (
                      <>
                        ${plan === SUBSCRIPTION_PLANS.BASIC ? '9.99' :
                          plan === SUBSCRIPTION_PLANS.PROFESSIONAL ? '29.99' :
                            '99.99'}<small>/month</small>
                      </>
                    )}
                  </h3>
                  {plan === SUBSCRIPTION_PLANS.FREE && (
                    <div className="alert alert-info py-1 text-center mb-2">
                      <small>Now with 5 team members!</small>
                    </div>
                  )}
                  <ul className="list-group list-group-flush mb-3">
                    <li className="list-group-item">
                      <strong>{PLAN_LIMITS[plan].maxMembers}</strong> team members
                    </li>
                    <li className="list-group-item">
                      <strong>{PLAN_LIMITS[plan].maxContacts}</strong> contacts
                    </li>
                    <li className="list-group-item">
                      <strong>{PLAN_LIMITS[plan].maxCompanies}</strong> companies
                    </li>
                  </ul>
                  <div className="mt-auto">
                    <button
                      className={`btn btn-${subscription?.plan === plan ? 'outline-primary' : 'primary'} w-100`}
                      onClick={() => handleUpgradeSubscription(plan)}
                      disabled={subscription?.plan === plan || loading}
                    >
                      {subscription?.plan === plan ? 'Current Plan' : 'Select Plan'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="mt-3 text-center text-muted">
          <small>This is a demo. No actual payment will be processed.</small>
        </div>
      </Modal>

      {/* Delete Organization Confirmation Modal */}
      <Modal
        show={showDeleteConfirmModal}
        onClose={() => {
          if (!isDeleting) {
            setShowDeleteConfirmModal(false);
            setOrgToDelete(null);
          }
        }}
        title="Delete Organization"
      >
        <div className="alert alert-danger">
          <i className="bi bi-exclamation-triangle-fill me-2"></i>
          <strong>Warning:</strong> This action cannot be undone!
        </div>

        <p>
          Are you sure you want to delete the organization <strong>{orgToDelete?.name}</strong>?
        </p>

        <p>
          This will permanently delete:
        </p>

        <ul>
          <li>The organization itself</li>
          <li>All opportunities associated with this organization</li>
          <li>All organization memberships</li>
        </ul>

        <p>
          Members will lose access to this organization, but their user accounts will remain intact.
        </p>

        <div className="d-flex justify-content-end mt-4">
          <button
            className="btn btn-secondary me-2"
            onClick={() => {
              setShowDeleteConfirmModal(false);
              setOrgToDelete(null);
            }}
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            className="btn btn-danger"
            onClick={handleDeleteOrganization}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Deleting...
              </>
            ) : (
              'Delete Organization'
            )}
          </button>
        </div>
      </Modal>
    </div>
  );
};

export default OrganizationPage;
