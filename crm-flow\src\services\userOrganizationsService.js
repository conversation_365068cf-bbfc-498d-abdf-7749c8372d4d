import { doc, getDoc, updateDoc, arrayUnion, arrayRemove, serverTimestamp } from 'firebase/firestore';
import { db } from './firebase';
import { getUserMemberOrganizations } from './userOrganizationService';
import { isOrganizationMember } from './organizationService';

// Collection name
const USERS_COLLECTION = 'users';

/**
 * Add an organization to a user's organizations list
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID to add
 * @returns {Promise<Object>} - The result of the operation
 */
export const addOrganizationToUser = async (userId, organizationId) => {
  try {
    console.log(`Adding organization ${organizationId} to user ${userId}`);

    // Get the user document
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    // Get the user data
    const userData = userDoc.data();

    // Check if user already has organizations array
    if (!userData.organizations) {
      // Create organizations array with this organization
      await updateDoc(userRef, {
        organizations: [organizationId],
        updatedAt: serverTimestamp()
      });
    } else {
      // Check if organization is already in the list
      if (userData.organizations.includes(organizationId)) {
        return {
          success: true,
          message: 'Organization is already in user\'s list'
        };
      }

      // Add organization to the list
      await updateDoc(userRef, {
        organizations: arrayUnion(organizationId),
        updatedAt: serverTimestamp()
      });
    }

    // If user doesn't have a current organization, set this one as current
    if (!userData.currentOrganizationId) {
      await updateDoc(userRef, {
        currentOrganizationId: organizationId,
        updatedAt: serverTimestamp()
      });
    }

    return {
      success: true,
      message: 'Organization added to user\'s list'
    };
  } catch (error) {
    console.error('Error adding organization to user:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Remove an organization from a user's organizations list
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID to remove
 * @returns {Promise<Object>} - The result of the operation
 */
export const removeOrganizationFromUser = async (userId, organizationId) => {
  try {
    // Get the user document
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    // Get the user data
    const userData = userDoc.data();

    // Check if user has organizations array
    if (!userData.organizations || !userData.organizations.includes(organizationId)) {
      return {
        success: true,
        message: 'Organization is not in user\'s list'
      };
    }

    // Remove organization from the list
    await updateDoc(userRef, {
      organizations: arrayRemove(organizationId),
      updatedAt: serverTimestamp()
    });

    // If this was the current organization, set a new current organization
    if (userData.currentOrganizationId === organizationId) {
      // Get remaining organizations
      const remainingOrgs = userData.organizations.filter(id => id !== organizationId);

      if (remainingOrgs.length > 0) {
        // Set the first remaining organization as current
        await updateDoc(userRef, {
          currentOrganizationId: remainingOrgs[0],
          updatedAt: serverTimestamp()
        });
      } else {
        // No organizations left, clear current organization
        await updateDoc(userRef, {
          currentOrganizationId: null,
          updatedAt: serverTimestamp()
        });
      }
    }

    return {
      success: true,
      message: 'Organization removed from user\'s list'
    };
  } catch (error) {
    console.error('Error removing organization from user:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get all organizations a user belongs to
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - Array of organization IDs
 */
export const getUserOrganizations = async (userId) => {
  try {
    // Get the user document
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return [];
    }

    // Get the user data
    const userData = userDoc.data();

    // Check if user has organizations array
    if (!userData.organizations) {
      // If not, try to get organizations from the old way (where user is a member)
      const memberOrgs = await getUserMemberOrganizations(userId);

      // If found, update the user document with the organizations array
      if (memberOrgs.length > 0) {
        const orgIds = memberOrgs.map(org => org.id);

        await updateDoc(userRef, {
          organizations: orgIds,
          updatedAt: serverTimestamp()
        });

        return orgIds;
      }

      return [];
    }

    // Return the user's organizations
    return userData.organizations;
  } catch (error) {
    console.error('Error getting user organizations:', error);
    return [];
  }
};

/**
 * Sync user's organizations with actual memberships
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - Array of valid organization IDs
 */
export const syncUserOrganizations = async (userId) => {
  try {
    // Get the user document
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return [];
    }

    // Get the user data
    const userData = userDoc.data();

    // If user has no organizations, return empty array
    if (!userData.organizations || userData.organizations.length === 0) {
      return [];
    }

    // Check if the user is still a member of each organization
    const membershipChecks = await Promise.all(
      userData.organizations.map(async (orgId) => {
        const isMember = await isOrganizationMember(userId, orgId);
        return { orgId, isMember };
      })
    );

    // Filter out organizations where the user is no longer a member
    const validOrgIds = membershipChecks
      .filter(check => check.isMember)
      .map(check => check.orgId);

    // If any organizations were filtered out, update the user's organizations list
    if (validOrgIds.length < userData.organizations.length) {
      console.log(`Removing ${userData.organizations.length - validOrgIds.length} organizations where user is no longer a member`);

      await updateDoc(userRef, {
        organizations: validOrgIds,
        updatedAt: serverTimestamp()
      });

      // If current organization is one that was removed, update it
      if (userData.currentOrganizationId && !validOrgIds.includes(userData.currentOrganizationId)) {
        if (validOrgIds.length > 0) {
          // Set first valid organization as current
          await updateDoc(userRef, {
            currentOrganizationId: validOrgIds[0],
            updatedAt: serverTimestamp()
          });
        } else {
          // No valid organizations, clear current organization
          await updateDoc(userRef, {
            currentOrganizationId: null,
            role: null,
            updatedAt: serverTimestamp()
          });
        }
      }
    }

    return validOrgIds;
  } catch (error) {
    console.error('Error syncing user organizations:', error);
    return [];
  }
};
