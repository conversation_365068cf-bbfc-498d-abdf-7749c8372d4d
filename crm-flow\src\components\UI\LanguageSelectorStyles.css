.language-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.language-buttons .btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.language-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.language-buttons .btn.btn-primary {
  background-color: #2563eb;
  border-color: #2563eb;
}

.language-buttons .btn.btn-outline-secondary {
  color: #64748b;
  border-color: #e2e8f0;
}

.language-buttons .btn.btn-outline-secondary:hover {
  background-color: #f8fafc;
  color: #334155;
}

.language-flag {
  margin-right: 8px;
  font-size: 1.2em;
}

.language-buttons-container {
  padding: 10px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

@media (max-width: 768px) {
  .language-buttons .btn {
    flex: 1 0 calc(50% - 10px);
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .language-buttons .btn {
    flex: 1 0 100%;
  }
}
