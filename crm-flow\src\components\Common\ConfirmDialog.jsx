import { useEffect } from 'react';
import PropTypes from 'prop-types';

const ConfirmDialog = ({ show, title, message, onConfirm, onCancel, confirmText = 'Confirm', cancelText = 'Cancel', confirmButtonClass = 'btn-danger' }) => {
  // Prevent scrolling when modal is open
  useEffect(() => {
    if (show) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [show]);

  if (!show) return null;

  return (
    <div className="modal-backdrop" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1050
    }}>
      <div className="modal-dialog" style={{
        backgroundColor: '#222',
        borderRadius: '8px',
        maxWidth: '500px',
        width: '100%',
        boxShadow: '0 5px 15px rgba(0, 0, 0, 0.5)',
        border: '1px solid #444'
      }}>
        <div className="modal-content" style={{ backgroundColor: '#222', color: '#fff' }}>
          <div className="modal-header" style={{ borderBottom: '1px solid #444', padding: '1rem' }}>
            <h5 className="modal-title">{title}</h5>
            <button type="button" className="btn-close btn-close-white" onClick={onCancel} aria-label="Close"></button>
          </div>
          <div className="modal-body" style={{ padding: '1rem' }}>
            <p>{message}</p>
          </div>
          <div className="modal-footer" style={{ borderTop: '1px solid #444', padding: '1rem' }}>
            <button type="button" className="btn btn-secondary" onClick={onCancel}>
              {cancelText}
            </button>
            <button type="button" className={`btn ${confirmButtonClass}`} onClick={onConfirm}>
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

ConfirmDialog.propTypes = {
  show: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  message: PropTypes.string.isRequired,
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  confirmButtonClass: PropTypes.string
};

export default ConfirmDialog;
