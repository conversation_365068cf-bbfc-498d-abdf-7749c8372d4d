import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy, deleteDoc, doc, updateDoc, writeBatch, where } from 'firebase/firestore';
import { db } from '../services/firebase';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { getUserData } from '../services/dataAccessService';
import Modal from '../components/UI/Modal';
import OpportunityForm from '../components/Forms/OpportunityForm';
import 'bootstrap/dist/css/bootstrap.min.css';

const OpportunitiesPage = () => {
  const { currentUser, organization } = useAuth();
  const [opportunities, setOpportunities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentOpportunity, setCurrentOpportunity] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showArchived, setShowArchived] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);

  // Fetch opportunities
  const fetchOpportunities = async () => {
    try {
      setLoading(true);

      // Get organization ID if available
      const orgId = organization?.id || null;

      // Use the getUserData function to get opportunities
      const opportunitiesList = await getUserData('opportunities', currentUser.uid, orgId);

      // Sort opportunities by createdAt in descending order
      const sortedOpportunities = opportunitiesList.sort((a, b) => {
        const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt);
        const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt);
        return dateB - dateA;
      });

      console.log('Fetched opportunities:', sortedOpportunities);
      setOpportunities(sortedOpportunities);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchOpportunities();
    }
  }, [currentUser, organization]);

  // Handle opportunity deletion
  const handleDeleteOpportunity = async (id) => {
    if (window.confirm('Are you sure you want to delete this opportunity?')) {
      try {
        await deleteDoc(doc(db, 'opportunities', id));
        // Refresh the opportunities list
        fetchOpportunities();
      } catch (error) {
        console.error('Error deleting opportunity:', error);
      }
    }
  };

  // Handle opportunity edit
  const handleEditOpportunity = (opportunity) => {
    setCurrentOpportunity(opportunity);
    setShowEditModal(true);
  };

  // Handle unarchiving an opportunity
  const handleUnarchiveOpportunity = async (id) => {
    try {
      await updateDoc(doc(db, 'opportunities', id), {
        archived: false,
        archivedAt: null
      });
      // Refresh the opportunities list
      fetchOpportunities();
    } catch (error) {
      console.error('Error unarchiving opportunity:', error);
    }
  };

  // Handle deleting all opportunities
  const handleDeleteAllOpportunities = async () => {
    if (!organization?.id) return;

    try {
      setIsDeleting(true);

      // Create a query to get all opportunities for this organization
      const opportunitiesQuery = query(
        collection(db, 'opportunities'),
        where('organizationId', '==', organization.id),
        where('archived', '==', showArchived)
      );

      const snapshot = await getDocs(opportunitiesQuery);

      if (snapshot.empty) {
        setIsDeleting(false);
        setShowDeleteConfirmModal(false);
        return;
      }

      // Use a batch to delete all opportunities
      const batch = writeBatch(db);

      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      // Commit the batch
      await batch.commit();

      // Also delete related edges in the flow view
      const edgesQuery = query(
        collection(db, 'edges'),
        where('organizationId', '==', organization.id)
      );

      const edgesSnapshot = await getDocs(edgesQuery);

      if (!edgesSnapshot.empty) {
        const edgesBatch = writeBatch(db);

        edgesSnapshot.docs.forEach(doc => {
          const edgeData = doc.data();
          // Only delete edges that are connected to opportunities
          if (edgeData.source.startsWith('opportunity-') || edgeData.target.startsWith('opportunity-')) {
            edgesBatch.delete(doc.ref);
          }
        });

        await edgesBatch.commit();
      }

      // Refresh the opportunities list
      fetchOpportunities();

      setIsDeleting(false);
      setShowDeleteConfirmModal(false);
    } catch (error) {
      console.error('Error deleting all opportunities:', error);
      setIsDeleting(false);
      setShowDeleteConfirmModal(false);
    }
  };

  // Filter opportunities based on search term and archived status
  const filteredOpportunities = opportunities.filter(opportunity => {
    // First filter by archived status
    if (showArchived) {
      if (!opportunity.archived) return false;
    } else {
      if (opportunity.archived) return false;
    }

    // Then filter by search term
    return opportunity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (opportunity.company && opportunity.company.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (opportunity.status && opportunity.status.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'New':
        return 'bg-primary';
      case 'Qualified':
        return 'bg-info';
      case 'Proposal':
        return 'bg-warning';
      case 'Negotiation':
        return 'bg-danger';
      case 'Closed Won':
        return 'bg-success';
      case 'Closed Lost':
        return 'bg-secondary';
      case 'Archived':
        return 'bg-dark';
      default:
        return 'bg-primary';
    }
  };

  // Import useCurrency hook at the top of the file
  const { formatCurrency, availableCurrencies } = useCurrency();

  // Format currency with opportunity's specific currency if available
  const formatOpportunityCurrency = (value, currencyCode) => {
    if (currencyCode) {
      // Special case for JPY and other currencies that don't typically use decimal places
      const fractionDigits = ['JPY', 'KRW', 'VND', 'IDR'].includes(currencyCode) ? 0 : 2;

      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: fractionDigits,
        maximumFractionDigits: fractionDigits
      }).format(value);
    }

    // Fall back to user's default currency
    return formatCurrency(value);
  };

  return (
    <div style={{ height: '100%', width: '100%', overflow: 'auto', padding: '20px' }}>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <div className="d-flex align-items-center">
            <h2 className="me-3">{showArchived ? 'Archived Opportunities' : 'Opportunities'}</h2>
            <div className="form-check form-switch">
              <input
                className="form-check-input"
                type="checkbox"
                id="showArchivedSwitch"
                checked={showArchived}
                onChange={() => setShowArchived(!showArchived)}
              />
              <label className="form-check-label" htmlFor="showArchivedSwitch">
                {showArchived ? 'Showing Archived' : 'Show Archived'}
              </label>
            </div>
          </div>
          {!showArchived && (
            <div className="d-flex gap-2">
              <button
                className="btn btn-primary"
                onClick={() => setShowAddModal(true)}
              >
                <i className="bi bi-plus-circle me-2"></i>
                Add Opportunity
              </button>
              <a
                href="/import-opportunities"
                className="btn btn-outline-primary"
              >
                <i className="bi bi-upload me-2"></i>
                Import Opportunities
              </a>
            </div>
          )}
        </div>

        {/* Search Bar */}
        <div className="row mb-4">
          <div className="col-md-6">
            <div className="input-group">
              <span className="input-group-text">
                <i className="bi bi-search"></i>
              </span>
              <input
                type="text"
                className="form-control"
                placeholder="Search opportunities..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="btn btn-outline-secondary"
                  type="button"
                  onClick={() => setSearchTerm('')}
                >
                  <i className="bi bi-x"></i>
                </button>
              )}
            </div>
          </div>
          <div className="col-md-6 d-flex justify-content-end">
            {filteredOpportunities.length > 0 && (
              <button
                className="btn btn-outline-danger"
                onClick={() => setShowDeleteConfirmModal(true)}
              >
                <i className="bi bi-trash me-2"></i>
                Delete All {showArchived ? 'Archived ' : ''}Opportunities
              </button>
            )}
          </div>
        </div>

        {loading ? (
          <div className="d-flex justify-content-center mt-5">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : (
          <>
            {filteredOpportunities.length === 0 ? (
              <div className="alert alert-info">
                {searchTerm ? 'No opportunities match your search.' : 'No opportunities found. Add your first opportunity!'}
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead className="table-light">
                    <tr>
                      <th>Name</th>
                      <th>Company</th>
                      <th>Value</th>
                      <th>Status</th>
                      <th>Close Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredOpportunities.map(opportunity => (
                      <tr key={opportunity.id}>
                        <td>
                          <div>{opportunity.name}</div>
                        </td>
                        <td>{opportunity.company || '-'}</td>
                        <td>
                          {opportunity.value ? formatOpportunityCurrency(opportunity.value, opportunity.currency) : '-'}
                          {opportunity.currency && opportunity.currency !== 'USD' && (
                            <small className="text-muted ms-1">({opportunity.currency})</small>
                          )}
                        </td>
                        <td>
                          <span className={`badge ${getStatusBadgeColor(opportunity.status)}`}>
                            {opportunity.status}
                          </span>
                        </td>
                        <td>
                          {opportunity.closeDate ? new Date(opportunity.closeDate).toLocaleDateString() : '-'}
                        </td>
                        <td>
                          <div className="btn-group">
                            {opportunity.archived ? (
                              <button
                                className="btn btn-sm btn-outline-success"
                                onClick={() => handleUnarchiveOpportunity(opportunity.id)}
                                title="Unarchive"
                              >
                                <i className="bi bi-arrow-counterclockwise"></i>
                              </button>
                            ) : (
                              <>
                                <button
                                  className="btn btn-sm btn-outline-primary"
                                  onClick={() => handleEditOpportunity(opportunity)}
                                  title="Edit"
                                >
                                  <i className="bi bi-pencil"></i>
                                </button>
                                <button
                                  className="btn btn-sm btn-outline-danger"
                                  onClick={() => handleDeleteOpportunity(opportunity.id)}
                                  title="Delete"
                                >
                                  <i className="bi bi-trash"></i>
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </>
        )}
      </div>

      {/* Add Opportunity Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="lg"
      >
        <OpportunityForm
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            fetchOpportunities();
          }}
        />
      </Modal>

      {/* Edit Opportunity Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="lg"
      >
        {currentOpportunity && (
          <OpportunityForm
            onClose={() => setShowEditModal(false)}
            onSuccess={() => {
              setShowEditModal(false);
              fetchOpportunities();
            }}
            opportunityData={currentOpportunity}
            isEditing={true}
          />
        )}
      </Modal>

      {/* Delete All Confirmation Modal */}
      <Modal
        show={showDeleteConfirmModal}
        onClose={() => setShowDeleteConfirmModal(false)}
        size="md"
      >
        <div className="modal-header">
          <h5 className="modal-title">Confirm Delete All</h5>
          <button
            type="button"
            className="btn-close"
            onClick={() => setShowDeleteConfirmModal(false)}
          ></button>
        </div>
        <div className="modal-body">
          <div className="alert alert-danger">
            <i className="bi bi-exclamation-triangle-fill me-2"></i>
            Warning: This action cannot be undone!
          </div>
          <p>
            Are you sure you want to delete all {showArchived ? 'archived ' : ''}opportunities?
            This will also remove any connections in the Flow View.
          </p>
          <p>
            <strong>Total opportunities to delete: {filteredOpportunities.length}</strong>
          </p>
        </div>
        <div className="modal-footer">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => setShowDeleteConfirmModal(false)}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-danger"
            onClick={handleDeleteAllOpportunities}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Deleting...
              </>
            ) : (
              'Delete All'
            )}
          </button>
        </div>
      </Modal>
    </div>
  );
};

export default OpportunitiesPage;
