import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { getUserOrganizations, syncUserOrganizations } from '../../services/userOrganizationsService';
import { getOrganization, isOrganizationMember } from '../../services/organizationService';
import { joinOrganizationByUuid } from '../../services/invitationService';
import Modal from '../UI/Modal';
import 'bootstrap/dist/css/bootstrap.min.css';

const OrganizationSwitcher = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { currentUser, organization, switchOrganization } = useAuth();
  const [organizations, setOrganizations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showJoinForm, setShowJoinForm] = useState(false);
  const [joinOrgUuid, setJoinOrgUuid] = useState('');
  const [joiningOrg, setJoiningOrg] = useState(false);

  // Fetch organizations where the user is a member
  useEffect(() => {
    const fetchOrganizations = async () => {
      if (currentUser && isOpen) {
        setLoading(true);
        setError(''); // Clear any previous errors
        try {
          console.log('Fetching organizations for user:', currentUser.uid);

          // Get all organizations the user is a member of (both owned and joined)
          const allOrgs = await getAllUserOrganizations(currentUser.uid);
          console.log('Organizations fetched:', allOrgs);

          // Set organizations regardless of length
          setOrganizations(allOrgs || []);

          if (allOrgs.length > 0) {
            console.log('Found organizations:', allOrgs.length);
          } else {
            console.log('No organizations found for user');
          }
        } catch (error) {
          console.error('Error fetching organizations:', error);
          setError('Failed to load organizations. Please try again.');
        } finally {
          setLoading(false);
        }
      }
    };

    if (isOpen) {
      fetchOrganizations();
    }
  }, [currentUser, isOpen]);

  // Handle organization selection
  const handleSelectOrg = async (orgId) => {
    try {
      setLoading(true);
      const success = await switchOrganization(orgId);

      if (success) {
        // Close the modal without reloading the page
        onClose();

        // Optional: Show a success message
        // You could use a toast notification here if you have one
        console.log('Successfully switched organization');
      } else {
        setError('Failed to switch organization');
      }
    } catch (error) {
      console.error('Error switching organization:', error);
      setError('Failed to switch organization');
    } finally {
      setLoading(false);
    }
  };

  // Navigate to organization page to create a new one
  const handleCreateOrg = () => {
    onClose();
    navigate('/organization');
  };

  // Toggle join organization form
  const toggleJoinForm = () => {
    setShowJoinForm(!showJoinForm);
    setError('');
    setSuccessMessage('');
    setJoinOrgUuid('');
  };

  // Handle joining an organization by UUID
  const handleJoinOrg = async (e) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setJoiningOrg(true);

    try {
      if (!currentUser) {
        throw new Error('You must be logged in to join an organization');
      }

      if (!joinOrgUuid || joinOrgUuid.trim() === '') {
        setError('Please enter a valid organization UUID');
        setJoiningOrg(false);
        return;
      }

      const result = await joinOrganizationByUuid(currentUser.uid, joinOrgUuid.trim());

      if (result.success) {
        setSuccessMessage(result.message || 'Successfully joined organization!');
        setJoinOrgUuid('');

        // Sync user's organizations with actual memberships
        const validOrgIds = await syncUserOrganizations(currentUser.uid);

        // Fetch full organization details for each valid ID
        const orgsPromises = validOrgIds.map(id => getOrganization(id));
        const orgsData = await Promise.all(orgsPromises);
        const validOrgs = orgsData.filter(org => org !== null);
        setOrganizations(validOrgs);
      } else {
        setError(result.error || 'Failed to join organization');
      }
    } catch (error) {
      console.error('Error joining organization:', error);
      setError(error.message);
    } finally {
      setJoiningOrg(false);
    }
  };

  return (
    <Modal show={isOpen} onClose={onClose} title="Organizations" size="sm">
      <div className="p-2">

        {loading ? (
          <div className="text-center py-4">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2">Loading your organizations...</p>
          </div>
        ) : organizations.length === 0 ? (
          <div className="py-4">
            {error && <div className="alert alert-danger">{error}</div>}
            {successMessage && (
              <div className="alert alert-success alert-dismissible fade show mb-3" role="alert">
                <i className="bi bi-check-circle-fill me-2"></i>
                {successMessage}
                <button type="button" className="btn-close" onClick={() => setSuccessMessage('')} aria-label="Close"></button>
              </div>
            )}

            <div className="text-center mb-4">
              <div className="display-6 text-muted mb-3">
                <i className="bi bi-building"></i>
              </div>
              <h5>No Organizations Found</h5>
              <p className="text-muted">You don't belong to any organizations yet.</p>
            </div>

            {showJoinForm ? (
              <div className="card mb-3">
                <div className="card-body">
                  <h6 className="card-title">Join Organization</h6>
                  <form onSubmit={handleJoinOrg}>
                    <div className="mb-3">
                      <label htmlFor="orgUuid" className="form-label">Organization UUID</label>
                      <input
                        type="text"
                        className="form-control"
                        id="orgUuid"
                        value={joinOrgUuid}
                        onChange={(e) => setJoinOrgUuid(e.target.value)}
                        placeholder="Enter organization UUID"
                        required
                      />
                      <div className="form-text">Enter the UUID of the organization you want to join.</div>
                    </div>

                    <div className="d-flex gap-2">
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={joiningOrg}
                      >
                        {joiningOrg ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Joining...
                          </>
                        ) : 'Join Organization'}
                      </button>
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={toggleJoinForm}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            ) : (
              <div className="d-flex gap-2">
                <button
                  className="btn btn-outline-primary btn-sm flex-grow-1"
                  onClick={handleCreateOrg}
                >
                  <i className="bi bi-plus-circle me-1"></i>
                  Create
                </button>
                <button
                  className="btn btn-outline-secondary btn-sm flex-grow-1"
                  onClick={toggleJoinForm}
                >
                  <i className="bi bi-box-arrow-in-right me-1"></i>
                  Join
                </button>
              </div>
            )}
          </div>
        ) : (
          <>
            <div className="list-group mb-3">
              {organizations.map(org => (
                <button
                  key={org.id}
                  className={`list-group-item list-group-item-action d-flex justify-content-between align-items-center py-2 ${organization && organization.id === org.id ? 'active' : ''
                    }`}
                  onClick={() => handleSelectOrg(org.id)}
                >
                  <div className="d-flex align-items-center">
                    <div className="me-2">
                      <i className={`bi ${org.ownerId === currentUser?.uid ? 'bi-building-fill' : 'bi-building'} text-${organization && organization.id === org.id ? 'white' : 'primary'}`}></i>
                    </div>
                    <div>
                      <div className="fw-medium mb-0 lh-sm">{org.name}</div>
                      <small className="text-muted d-block lh-sm">{org.members?.length || 0} members</small>
                    </div>
                  </div>
                  {organization && organization.id === org.id && (
                    <span className="badge bg-primary rounded-pill">
                      <i className="bi bi-check-lg"></i>
                    </span>
                  )}
                </button>
              ))}
            </div>
            {successMessage && (
              <div className="alert alert-success alert-dismissible fade show mb-3" role="alert">
                <i className="bi bi-check-circle-fill me-2"></i>
                {successMessage}
                <button type="button" className="btn-close" onClick={() => setSuccessMessage('')} aria-label="Close"></button>
              </div>
            )}

            {showJoinForm ? (
              <div className="card mb-3">
                <div className="card-body">
                  <h6 className="card-title">Join Organization</h6>
                  <form onSubmit={handleJoinOrg}>
                    <div className="mb-3">
                      <label htmlFor="orgUuid" className="form-label">Organization UUID</label>
                      <input
                        type="text"
                        className="form-control"
                        id="orgUuid"
                        value={joinOrgUuid}
                        onChange={(e) => setJoinOrgUuid(e.target.value)}
                        placeholder="Enter organization UUID"
                        required
                      />
                      <div className="form-text">Enter the UUID of the organization you want to join.</div>
                    </div>

                    <div className="d-flex gap-2">
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={joiningOrg}
                      >
                        {joiningOrg ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Joining...
                          </>
                        ) : 'Join Organization'}
                      </button>
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={toggleJoinForm}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            ) : (
              <div className="d-flex gap-2 mb-3">
                <button
                  className="btn btn-outline-primary btn-sm flex-grow-1"
                  onClick={handleCreateOrg}
                >
                  <i className="bi bi-plus-circle me-1"></i>
                  Create
                </button>
                <button
                  className="btn btn-outline-secondary btn-sm flex-grow-1"
                  onClick={toggleJoinForm}
                >
                  <i className="bi bi-box-arrow-in-right me-1"></i>
                  Join
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </Modal>
  );
};

export default OrganizationSwitcher;
