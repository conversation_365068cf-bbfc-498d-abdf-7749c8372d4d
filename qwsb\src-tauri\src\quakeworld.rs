use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;
use tokio::net::UdpSocket;
use tokio::time::timeout;

const UDP_TIMEOUT: Duration = Duration::from_secs(3);

// Character mapping for QuakeWorld protocol
// We'll use a function to initialize the charset instead of a const with loops
fn init_charset() -> [u8; 256] {
    let mut charset = [32u8; 256]; // Default to space for all characters

    // Set up the specific mappings
    charset[0] = 46;  // .
    charset[1] = 35;  // #
    charset[2] = 35;  // #
    charset[3] = 35;  // #
    charset[4] = 35;  // #
    charset[5] = 46;  // .
    charset[6] = 35;  // #
    charset[7] = 35;  // #
    charset[8] = 35;  // #
    charset[9] = 35;  // #
    charset[13] = 62; // >
    charset[14] = 46; // .
    charset[15] = 46; // .
    charset[16] = 91; // [
    charset[17] = 93; // ]
    charset[18] = 48; // 0
    charset[19] = 49; // 1
    charset[20] = 50; // 2
    charset[21] = 51; // 3
    charset[22] = 52; // 4
    charset[23] = 53; // 5
    charset[24] = 54; // 6
    charset[25] = 55; // 7
    charset[26] = 56; // 8
    charset[27] = 57; // 9
    charset[28] = 46; // .
    charset[29] = 32; // Space
    charset[30] = 32; // Space
    charset[31] = 32; // Space
    charset[127] = 32; // Space
    charset[128] = 40; // (
    charset[129] = 61; // =
    charset[130] = 41; // )
    charset[131] = 35; // #
    charset[132] = 35; // #
    charset[133] = 46; // .
    charset[134] = 35; // #
    charset[135] = 35; // #
    charset[136] = 35; // #
    charset[137] = 35; // #
    charset[141] = 62; // >
    charset[142] = 46; // .
    charset[143] = 46; // .

    // Normal ASCII characters
    for i in 32..127 {
        charset[i] = i as u8;
    }

    // Secondary color (red) characters
    for i in 144..255 {
        charset[i] = charset[i - 128];
    }

    charset
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Player {
    pub id: i32,
    pub frags: String,
    pub time: i32,
    pub ping: i32,
    pub name: String,
    pub skin: String,
    pub topcolor: i32,
    pub bottomcolor: i32,
    pub team: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerInfo {
    pub address: String,
    pub port: u16,
    pub hostname: Option<String>,
    pub map: Option<String>,
    pub maxclients: Option<i32>,
    pub players: Vec<Player>,
    pub ping: Option<i32>,
    pub last_updated: Option<DateTime<Utc>>,
    #[serde(flatten)]
    pub other_info: HashMap<String, String>,
}

impl ServerInfo {
    pub fn new(address: String, port: u16) -> Self {
        ServerInfo {
            address,
            port,
            hostname: None,
            map: None,
            maxclients: None,
            players: Vec::new(),
            ping: None,
            last_updated: None,
            other_info: HashMap::new(),
        }
    }
}

// Convert QuakeWorld characters to ASCII
fn quake_chars(data: &mut [u8]) {
    // Initialize the charset
    let charset = init_charset();

    for i in 0..data.len() {
        data[i] = charset[data[i] as usize];
    }
}

// Send a UDP command to a QuakeWorld server
async fn udp_command(address: &str, port: u16, data: &str) -> Result<Vec<u8>> {
    let socket = UdpSocket::bind("0.0.0.0:0").await?;

    // Format the command with the QuakeWorld protocol header (4 bytes of 0xFF)
    let mut buf = vec![0xFF, 0xFF, 0xFF, 0xFF];
    buf.extend_from_slice(data.as_bytes());

    let addr = format!("{}:{}", address, port);
    socket.send_to(&buf, &addr).await?;

    // Wait for response with timeout
    let mut response = vec![0u8; 4096];
    let result = timeout(UDP_TIMEOUT, socket.recv_from(&mut response)).await;

    match result {
        Ok(Ok((size, _))) => {
            response.truncate(size);
            Ok(response)
        }
        Ok(Err(e)) => Err(anyhow::anyhow!("UDP error: {}", e)),
        Err(_) => Err(anyhow::anyhow!("UDP timeout")),
    }
}

// Query a server for its status
pub async fn query_server_status(address: &str, port: u16) -> Result<ServerInfo> {
    let start_time = std::time::Instant::now();
    let response = udp_command(address, port, "status 0\0").await?;
    let ping = start_time.elapsed().as_millis() as i32;

    // Skip the first 5 bytes (0xFF 0xFF 0xFF 0xFF n) and remove the last 2 bytes (newline)
    let mut data = response[5..response.len() - 2].to_vec();
    quake_chars(&mut data);

    let data_str = String::from_utf8_lossy(&data);
    let mut lines: Vec<&str> = data_str.split('\n').collect();

    // Parse server info
    let server_info_line = lines.remove(0);
    let server_info_parts: Vec<&str> = server_info_line.split('\\').collect();

    let mut server_info = ServerInfo::new(address.to_string(), port);
    server_info.ping = Some(ping);
    server_info.last_updated = Some(Utc::now());

    // Parse key-value pairs
    for i in (1..server_info_parts.len()).step_by(2) {
        if i + 1 >= server_info_parts.len() {
            break;
        }

        let key = server_info_parts[i];
        let value = server_info_parts[i + 1];

        match key {
            "hostname" => server_info.hostname = Some(value.to_string()),
            "map" => server_info.map = Some(value.to_string()),
            "maxclients" => {
                if let Ok(num) = value.parse::<i32>() {
                    server_info.maxclients = Some(num);
                }
            }
            _ => {
                server_info.other_info.insert(key.to_string(), value.to_string());
            }
        }
    }

    // Parse player info
    let mut players = Vec::new();

    // First, try to parse players from the standard format
    for line in &lines {
        if line.is_empty() {
            continue;
        }

        // Example player line: "-999 0 0 0 "Bot" "" 0 0 ""
        let parts: Vec<&str> = line.splitn(9, ' ').collect();
        if parts.len() < 9 {
            continue;
        }

        let id = parts[0].parse::<i32>().unwrap_or(0);
        let frags = parts[1].to_string();
        let time = parts[2].parse::<i32>().unwrap_or(0);
        let ping = parts[3].parse::<i32>().unwrap_or(0);

        // Extract name (remove quotes)
        let name = parts[4].trim_matches('"').to_string();
        let skin = parts[5].trim_matches('"').to_string();

        let topcolor = parts[6].parse::<i32>().unwrap_or(0);
        let bottomcolor = parts[7].parse::<i32>().unwrap_or(0);
        let team = parts[8].trim_matches('"').to_string();

        players.push(Player {
            id,
            frags,
            time,
            ping,
            name,
            skin,
            topcolor,
            bottomcolor,
            team,
        });
    }

    // If no players were found, try to extract player info from the map field
    // Some servers include player info in the map field
    if players.is_empty() && server_info.map.is_some() {
        // Clone the map string to avoid borrowing issues
        let map_string = server_info.map.as_ref().unwrap().clone();
        let map_parts: Vec<&str> = map_string.split(' ').collect();

        // Check if the map field might contain player info
        // Format is typically: mapname frags time ping "name" "skin" topcolor bottomcolor "team"
        if map_parts.len() >= 9 {
            // Extract the actual map name
            let map_name = map_parts[0].to_string();

            // Try to parse player info
            let mut i = 1;
            while i + 8 < map_parts.len() {
                let frags = map_parts[i].to_string();
                let time = map_parts[i+1].parse::<i32>().unwrap_or(0);
                let ping = map_parts[i+2].parse::<i32>().unwrap_or(0);

                // Find the name (it's in quotes)
                let mut name_idx = i + 3;
                while name_idx < map_parts.len() && !map_parts[name_idx].starts_with('"') {
                    name_idx += 1;
                }

                if name_idx < map_parts.len() {
                    let name = map_parts[name_idx].trim_matches('"').to_string();

                    // Add the player
                    players.push(Player {
                        id: 0, // We don't have an ID, so use 0
                        frags,
                        time,
                        ping,
                        name,
                        skin: String::new(),
                        topcolor: 0,
                        bottomcolor: 0,
                        team: String::new(),
                    });
                }

                // Move to the next potential player
                i += 9;
            }

            // Update the map name after we're done with map_parts
            server_info.map = Some(map_name);
        }
    }

    server_info.players = players;
    Ok(server_info)
}

// Ping a server to check if it's online
pub async fn ping_server(address: &str, port: u16) -> Result<i32> {
    let start_time = std::time::Instant::now();
    let _ = udp_command(address, port, "ping").await?;
    Ok(start_time.elapsed().as_millis() as i32)
}
