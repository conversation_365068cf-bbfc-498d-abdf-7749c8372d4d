.subscription-page {
  padding: 20px;
}

.subscription-card {
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.subscription-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.subscription-card-popular {
  border: 2px solid #4361ee;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.15);
}

.popular-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #4361ee;
  color: white;
  padding: 5px 15px;
  font-size: 0.8rem;
  font-weight: 600;
  border-bottom-left-radius: 8px;
}

.subscription-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 15px;
}

.subscription-card-popular .card-header {
  background-color: #eef1ff;
}

.subscription-card .list-unstyled li {
  padding: 8px 0;
  display: flex;
  align-items: center;
}

.subscription-card .list-unstyled li i {
  min-width: 20px;
}

@media (max-width: 768px) {
  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 100%;
  }
}
