import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import MainLayout from '../components/Layout/MainLayout';
import 'bootstrap/dist/css/bootstrap.min.css';
import './PluginsStyles.css';

const PluginsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  // Plugin cards data
  const plugins = [
    {
      id: 'import-opportunities',
      title: t('plugins.importOpportunities.title'),
      description: t('plugins.importOpportunities.description'),
      icon: 'bi-upload',
      color: '#3b82f6',
      route: '/import-opportunities',
      buttonText: t('plugins.importOpportunities.button')
    },
    {
      id: 'import-excel',
      title: t('plugins.importExcel.title'),
      description: t('plugins.importExcel.description'),
      icon: 'bi-file-earmark-excel',
      color: '#10b981',
      route: '/import-excel',
      buttonText: t('plugins.importExcel.button')
    }
  ];

  return (
    <MainLayout>
      <div className="plugins-container">
        <div className="plugins-header">
          <h1>{t('plugins.title')}</h1>
          <p className="text-muted">{t('plugins.description')}</p>
        </div>

        <div className="plugins-grid">
          {plugins.map(plugin => (
            <div key={plugin.id} className="plugin-card">
              <div className="plugin-icon" style={{ backgroundColor: plugin.color }}>
                <i className={`bi ${plugin.icon}`}></i>
              </div>
              <div className="plugin-content">
                <h3>{plugin.title}</h3>
                <p>{plugin.description}</p>
                {plugin.buttonAction ? (
                  <button
                    className="btn btn-primary"
                    onClick={plugin.buttonAction}
                  >
                    {plugin.buttonText}
                  </button>
                ) : (
                  <a
                    href={plugin.route}
                    className="btn btn-primary"
                  >
                    {plugin.buttonText}
                  </a>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </MainLayout>
  );
};

export default PluginsPage;
