.opportunity-importer {
  max-width: 100%;
}

.opportunity-importer .card {
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.opportunity-importer .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 1.5rem;
}

.opportunity-importer .card-body {
  padding: 1.5rem;
}

.opportunity-importer textarea {
  font-family: monospace;
  font-size: 0.9rem;
  resize: vertical;
}

.opportunity-importer .table {
  font-size: 0.9rem;
}

.opportunity-importer .table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.opportunity-importer .table td {
  vertical-align: middle;
}

.opportunity-importer .alert {
  margin-top: 1rem;
  border-radius: 6px;
}

.opportunity-importer .alert ul {
  padding-left: 1.5rem;
  margin-bottom: 0;
}

.opportunity-importer .form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.opportunity-importer .form-select,
.opportunity-importer .form-control {
  border-radius: 4px;
  border: 1px solid #ced4da;
  padding: 0.375rem 0.75rem;
}

.opportunity-importer .btn {
  border-radius: 4px;
  padding: 0.375rem 1rem;
  font-weight: 500;
}

.opportunity-importer .btn-primary {
  background-color: #2563eb;
  border-color: #2563eb;
}

.opportunity-importer .btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.opportunity-importer .btn-success {
  background-color: #10b981;
  border-color: #10b981;
}

.opportunity-importer .btn-success:hover {
  background-color: #059669;
  border-color: #059669;
}

.opportunity-importer .btn-secondary {
  background-color: #6b7280;
  border-color: #6b7280;
}

.opportunity-importer .btn-secondary:hover {
  background-color: #4b5563;
  border-color: #4b5563;
}

.opportunity-importer .btn-outline-secondary {
  color: #6b7280;
  border-color: #6b7280;
}

.opportunity-importer .btn-outline-secondary:hover {
  background-color: #6b7280;
  color: white;
}

.opportunity-importer .spinner-border {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}
