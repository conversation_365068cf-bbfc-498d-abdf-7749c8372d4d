import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { updateUserProfile } from '../services/userService';
import { updateProfile, signOut } from 'firebase/auth';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import LanguageSelector from '../components/UI/LanguageSelector';
import CurrencySelector from '../components/UI/CurrencySelector';
import { auth } from '../services/firebase';
import { getAllUserOrganizations } from '../services/organizationService';
import 'bootstrap/dist/css/bootstrap.min.css';
import '../components/UI/AlertStyles.css';

// Predefined avatars
const AVATARS = [
  '/avatars/984101_person_avatar_male_businessman_suit_user.png',
  '/avatars/984102_avatar_male_man_user_person_casual.png',
  '/avatars/984103_avatar_geek_male_man_user_person.png',
  '/avatars/984104_person_woman_user_girl_female_avatar.png',
  '/avatars/984106_avatar_person_user_glasses_girl_female.png',
  '/avatars/984108_man_user_young_person_avatar_male.png',
  '/avatars/984111_geek_person_user_man_male_avatar.png',
  '/avatars/984113_person_user_casual_girl_female_avatar.png',
  '/avatars/984116_woman_person_avatar_female_girl_user.png',
  '/avatars/984119_person_profile_avatar_man_user_male.png',
  '/avatars/984120_businessman_suit_avatar_man_user_male.png',
  '/avatars/984122_male_glasses_person_avatar_man_user.png',
  '/avatars/984123_guy_avatar_man_user_male_person.png',
  '/avatars/984125_girl_avatar_user_person_female_glasses.png',
  '/avatars/984126_male_user_man_avatar_person_suit.png',
  '/avatars/984127_male_user_man_avatar_profile_person.png',
];

const UserSettingsPage = () => {
  const { currentUser, userProfile, organization, refreshUserProfile, switchOrganization } = useAuth();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    photoURL: '',
  });
  const [selectedAvatar, setSelectedAvatar] = useState('');
  const [joinOrgUuid, setJoinOrgUuid] = useState('');
  const [organizations, setOrganizations] = useState([]);
  const [loadingOrgs, setLoadingOrgs] = useState(false);

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut(auth);
      navigate('/signin');
    } catch (error) {
      console.error('Error signing out:', error);
      setError('Failed to sign out. Please try again.');
    }
  };

  // Initialize form data with user profile
  useEffect(() => {
    if (userProfile) {
      setFormData({
        name: userProfile.name || '',
        photoURL: userProfile.photoURL || '',
      });
      setSelectedAvatar(userProfile.photoURL || '');
    }
  }, [userProfile]);

  // Fetch user organizations
  useEffect(() => {
    const fetchOrganizations = async () => {
      if (currentUser) {
        setLoadingOrgs(true);
        try {
          const allOrgs = await getAllUserOrganizations(currentUser.uid);
          setOrganizations(allOrgs || []);
        } catch (error) {
          console.error('Error fetching organizations:', error);
        } finally {
          setLoadingOrgs(false);
        }
      }
    };

    fetchOrganizations();
  }, [currentUser]);

  // Handle switching organization
  const handleSwitchOrganization = async (orgId) => {
    setError('');
    setLoading(true);

    try {
      const success = await switchOrganization(orgId);

      if (success) {
        setSuccessMessage(t('organizations.switchSuccess', 'Successfully switched organization'));
        // Refresh the page to update all components with the new organization context
        window.location.reload();
      } else {
        setError(t('organizations.switchError', 'Failed to switch organization'));
      }
    } catch (error) {
      console.error('Error switching organization:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setLoading(true);

    try {
      if (!currentUser) {
        throw new Error('You must be logged in to update your profile');
      }

      // Update user profile
      const result = await updateUserProfile(currentUser.uid, {
        name: formData.name,
        photoURL: selectedAvatar,
      });

      if (result.success) {
        // Also update photoURL in Firebase Auth if it was changed
        if (selectedAvatar && selectedAvatar !== currentUser.photoURL) {
          try {
            await updateProfile(currentUser, {
              photoURL: selectedAvatar
            });
            console.log('Updated user photoURL in Firebase Auth');
          } catch (error) {
            console.error('Error updating user photoURL in Firebase Auth:', error);
          }
        }

        setSuccessMessage('Profile updated successfully!');
        // Refresh user profile to get updated data
        await refreshUserProfile();
      } else {
        setError(result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle joining organization by UUID
  const handleJoinOrg = async (e) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setLoading(true);

    try {
      if (!currentUser) {
        throw new Error('You must be logged in to join an organization');
      }

      if (!joinOrgUuid || joinOrgUuid.trim() === '') {
        setError('Please enter a valid organization UUID');
        setLoading(false);
        return;
      }

      // Import and use the joinOrganizationByUuid function
      const { joinOrganizationByUuid } = await import('../services/invitationService');
      const result = await joinOrganizationByUuid(currentUser.uid, joinOrgUuid.trim());

      if (result.success) {
        setSuccessMessage('Successfully joined organization! You can now switch to it from the organization switcher.');
        setJoinOrgUuid('');
        // Refresh user profile to get updated data
        await refreshUserProfile();
      } else {
        setError(result.error || 'Failed to join organization');
      }
    } catch (error) {
      console.error('Error joining organization:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle avatar selection
  const handleAvatarSelect = (avatar) => {
    setSelectedAvatar(avatar);
    setFormData({ ...formData, photoURL: avatar });
  };

  return (
    <div className="container py-4">
      <h2 className="mb-4">{t('settings.title')}</h2>

      {successMessage && (
        <div className="alert alert-success alert-dismissible fade show" role="alert">
          <i className="bi bi-check-circle-fill me-2"></i>
          {successMessage}
          <button type="button" className="btn-close" onClick={() => setSuccessMessage('')} aria-label="Close"></button>
        </div>
      )}

      {error && (
        <div className="alert alert-danger alert-dismissible fade show" role="alert">
          <i className="bi bi-exclamation-triangle-fill me-2"></i>
          {error}
          <button type="button" className="btn-close" onClick={() => setError('')} aria-label="Close"></button>
        </div>
      )}

      <div className="row">
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">
              <h5 className="mb-0">{t('settings.profile')}</h5>
            </div>
            <div className="card-body">
              <form onSubmit={handleSubmit}>
                <div className="mb-3">
                  <label htmlFor="name" className="form-label">{t('auth.displayName')}</label>
                  <input
                    type="text"
                    className="form-control"
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                  />
                </div>



                <div className="mb-4">
                  <label className="form-label">{t('settings.avatar', 'Avatar')}</label>
                  <div className="d-flex flex-wrap gap-3 mb-3" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                    {AVATARS.map((avatar, index) => (
                      <div
                        key={index}
                        className={`avatar-option ${selectedAvatar === avatar ? 'selected' : ''}`}
                        onClick={() => handleAvatarSelect(avatar)}
                        style={{
                          width: '80px',
                          height: '80px',
                          borderRadius: '50%',
                          cursor: 'pointer',
                          border: selectedAvatar === avatar ? '3px solid #2563eb' : '3px solid transparent',
                          padding: '2px',
                          backgroundColor: '#f8fafc',
                          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                          transition: 'all 0.2s ease',
                          margin: '5px',
                        }}
                      >
                        <img
                          src={avatar}
                          alt={`Avatar ${index + 1}`}
                          style={{
                            width: '100%',
                            height: '100%',
                            borderRadius: '50%',
                            objectFit: 'cover',
                          }}
                        />
                      </div>
                    ))}
                  </div>
                  <div className="text-muted small mt-2">
                    {t('settings.avatarHelp', 'Click on an avatar to select it as your profile picture.')}
                  </div>
                </div>

                <div className="d-grid">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        {t('common.saving', 'Saving...')}
                      </>
                    ) : t('common.saveChanges', 'Save Changes')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">
              <h5 className="mb-0">{t('settings.language')}</h5>
            </div>
            <div className="card-body">
              <p className="mb-3">{t('settings.languages.selectLanguage', 'Select your preferred language:')}</p>
              <div className="language-buttons-container">
                <LanguageSelector variant="buttons" />
              </div>
              <div className="mt-3 text-muted small">
                <i className="bi bi-info-circle me-1"></i>
                {t('settings.languages.changeInfo', 'Your language preference will be saved to your profile and applied across all your devices.')}
              </div>
            </div>
          </div>

          <div className="card mb-4">
            <div className="card-header">
              <h5 className="mb-0">{t('settings.currency')}</h5>
            </div>
            <div className="card-body">
              <p className="mb-3">{t('settings.currencies.selectCurrency', 'Select your preferred currency:')}</p>
              <div className="currency-selector-container">
                <CurrencySelector variant="cards" />
              </div>
              <div className="mt-3 text-muted small">
                <i className="bi bi-info-circle me-1"></i>
                {t('settings.currencies.changeInfo', 'Your currency preference will be saved to your profile and applied across all your devices.')}
              </div>
            </div>
          </div>

          <div className="card mb-4">
            <div className="card-header">
              <h5 className="mb-0">{t('organizations.switchOrganization', 'Switch Organization')}</h5>
            </div>
            <div className="card-body">
              {loadingOrgs ? (
                <div className="text-center py-3">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <p className="mt-2">{t('organizations.loading', 'Loading your organizations...')}</p>
                </div>
              ) : organizations.length === 0 ? (
                <div className="text-center py-3">
                  <div className="display-6 text-muted mb-3">
                    <i className="bi bi-building"></i>
                  </div>
                  <h6>{t('organizations.noOrganizations', 'No Organizations Found')}</h6>
                  <p className="text-muted">{t('organizations.noOrganizationsDesc', 'You don\'t belong to any organizations yet.')}</p>
                </div>
              ) : (
                <div className="list-group mb-3">
                  {organizations.map(org => (
                    <button
                      key={org.id}
                      className={`list-group-item list-group-item-action d-flex justify-content-between align-items-center py-2 ${organization && organization.id === org.id ? 'active' : ''}`}
                      onClick={() => handleSwitchOrganization(org.id)}
                      disabled={organization && organization.id === org.id}
                    >
                      <div className="d-flex align-items-center">
                        <div className="me-2">
                          <i className={`bi ${org.ownerId === currentUser?.uid ? 'bi-building-fill' : 'bi-building'} text-${organization && organization.id === org.id ? 'white' : 'primary'}`}></i>
                        </div>
                        <div>
                          <div className="fw-medium mb-0 lh-sm">{org.name}</div>
                          <small className={`${organization && organization.id === org.id ? 'text-white-50' : 'text-muted'} d-block lh-sm`}>
                            {org.isOwner ? t('organizations.owner', 'Owner') : t('organizations.member', 'Member')} • {org.members?.length || 0} {t('organizations.members', 'members')}
                          </small>
                        </div>
                      </div>
                      {organization && organization.id === org.id && (
                        <span className="badge bg-light text-primary rounded-pill">
                          <i className="bi bi-check-lg"></i> {t('organizations.current', 'Current')}
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              )}

              <div className="mt-4">
                <h6 className="mb-3">{t('organizations.joinOrganization', 'Join Organization')}</h6>
                <form onSubmit={handleJoinOrg}>
                  <div className="mb-3">
                    <label htmlFor="orgUuid" className="form-label">{t('organizations.organizationUUID', 'Organization UUID')}</label>
                    <input
                      type="text"
                      className="form-control"
                      id="orgUuid"
                      value={joinOrgUuid}
                      onChange={(e) => setJoinOrgUuid(e.target.value)}
                      placeholder={t('organizations.enterOrganizationUUID', 'Enter organization UUID')}
                      required
                    />
                    <div className="form-text">{t('organizations.enterUUIDToJoin', 'Enter the UUID of the organization you want to join.')}</div>
                  </div>

                  <div className="d-grid">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          {t('common.joining', 'Joining...')}
                        </>
                      ) : t('organizations.joinOrganization', 'Join Organization')}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div className="card mb-4">
            <div className="card-header">
              <h5 className="mb-0">{t('subscription.title', 'Subscription')}</h5>
            </div>
            <div className="card-body">
              <p className="text-muted mb-3">{t('subscription.manageDescription', 'Manage your subscription plan and billing')}</p>
              <a
                href="/subscription"
                className="btn btn-primary w-100"
              >
                <i className="bi bi-credit-card me-2"></i>
                {t('subscription.managePlan', 'Manage Subscription')}
              </a>
            </div>
          </div>

          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">{t('auth.account', 'Account')}</h5>
            </div>
            <div className="card-body">
              <p className="text-muted mb-3">{t('auth.signOutDescription', 'Sign out from your account')}</p>
              <button
                className="btn btn-danger w-100"
                onClick={handleSignOut}
              >
                <i className="bi bi-box-arrow-right me-2"></i>
                {t('auth.signOut', 'Sign Out')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserSettingsPage;
