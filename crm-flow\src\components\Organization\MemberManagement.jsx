import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { getOrganizationMembers, removeOrganizationMember } from '../../services/organizationService';
import { inviteUserByEmail } from '../../services/invitationService';
import Modal from '../UI/Modal';
import './MemberManagementStyles.css';

// For debugging
const DEBUG = true;

const MemberManagement = () => {
  const { currentUser, organization, userProfile, refreshUserProfile } = useAuth();
  const [members, setMembers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteFormData, setInviteFormData] = useState({ email: '', role: 'member' });
  const [tempPassword, setTempPassword] = useState('');
  const [showTempPassword, setShowTempPassword] = useState(false);

  // Fetch organization members
  useEffect(() => {
    const fetchMembers = async () => {
      if (organization && organization.id) {
        if (DEBUG) console.log('Fetching members for organization:', organization.id);
        setLoading(true);
        try {
          const orgMembers = await getOrganizationMembers(organization.id);
          if (DEBUG) console.log('Fetched members:', orgMembers);
          setMembers(orgMembers);
        } catch (error) {
          console.error('Error fetching organization members:', error);
          setError('Failed to load organization members');
        } finally {
          setLoading(false);
        }
      } else {
        if (DEBUG) console.log('No organization available, setting loading to false');
        setLoading(false);
        setMembers([]);
      }
    };

    fetchMembers();
  }, [organization]);

  // Handle inviting a new member
  const handleInviteMember = async (e) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setLoading(true);

    try {
      // Validate email
      if (!inviteFormData.email || !inviteFormData.email.includes('@')) {
        setError('Please enter a valid email address');
        setLoading(false);
        return;
      }

      // Check if user is already a member
      const existingMember = members.find(member =>
        member.email.toLowerCase() === inviteFormData.email.toLowerCase()
      );

      if (existingMember) {
        setError('This user is already a member of your organization');
        setLoading(false);
        return;
      }

      // Invite the user
      const result = await inviteUserByEmail(
        organization.id,
        inviteFormData.email,
        inviteFormData.role,
        userProfile.name
      );

      if (result.success) {
        setSuccessMessage(`User ${inviteFormData.email} has been invited successfully!`);

        // If a temporary password was generated, show it
        if (result.password) {
          setTempPassword(result.password);
          setShowTempPassword(true);
        }

        // Refresh the members list
        const updatedMembers = await getOrganizationMembers(organization.id);
        setMembers(updatedMembers);

        // Reset form
        setInviteFormData({ email: '', role: 'member' });
      } else {
        setError(result.error || 'Failed to invite user');
      }
    } catch (error) {
      console.error('Error inviting user:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle removing a member
  const handleRemoveMember = async (memberId) => {
    if (!confirm('Are you sure you want to remove this member?')) {
      return;
    }

    setError('');
    setSuccessMessage('');
    setLoading(true);

    try {
      // Don't allow removing yourself
      if (memberId === currentUser.uid) {
        setError('You cannot remove yourself from the organization');
        setLoading(false);
        return;
      }

      // Don't allow non-owners to remove members
      if (userProfile?.role !== 'owner') {
        setError('Only organization owners can remove members');
        setLoading(false);
        return;
      }

      const result = await removeOrganizationMember(organization.id, memberId);

      if (result.success) {
        setSuccessMessage('Member removed successfully');
        // Update the members list
        setMembers(members.filter(member => member.id !== memberId));
      } else {
        setError(result.error || 'Failed to remove member');
      }
    } catch (error) {
      console.error('Error removing member:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Copy organization ID to clipboard
  const copyOrgIdToClipboard = () => {
    if (organization) {
      navigator.clipboard.writeText(organization.id)
        .then(() => {
          setSuccessMessage('Organization ID copied to clipboard!');
          setTimeout(() => setSuccessMessage(''), 3000);
        })
        .catch(err => {
          console.error('Failed to copy organization ID:', err);
          setError('Failed to copy organization ID');
        });
    }
  };

  return (
    <div className="member-management">
      <div className="card mb-4">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="mb-0">Organization Members</h5>
          <span className="badge bg-secondary">{members.length} {members.length === 1 ? 'Member' : 'Members'}</span>
        </div>
        <div className="card-body p-3">
          {error && <div className="alert alert-danger">{error}</div>}
          {successMessage && <div className="alert alert-success">{successMessage}</div>}



          {loading ? (
            <div className="d-flex justify-content-center">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : members.length === 0 ? (
            <p>No members found.</p>
          ) : (
            <div className="member-list">
              {members.map(member => (
                <div key={member.id} className="member-card">
                  <div className="member-avatar">
                    {member.photoURL ? (
                      <img
                        src={member.photoURL}
                        alt={member.name}
                        className="rounded-circle"
                      />
                    ) : (
                      <div className="avatar-placeholder">
                        {member.name.charAt(0).toUpperCase()}
                      </div>
                    )}
                  </div>
                  <div className="member-info">
                    <div className="member-name">
                      {member.name}
                      {member.id === organization.ownerId && (
                        <span className="badge bg-primary ms-2">Owner</span>
                      )}
                    </div>
                    <div className="member-email">{member.email}</div>
                    <div className="member-role">
                      <span className={`badge ${member.role === 'owner' ? 'bg-danger' : 'bg-info'}`}>
                        {member.role === 'owner' ? 'Admin' : 'Member'}
                      </span>
                    </div>
                  </div>
                  {userProfile?.role === 'owner' && member.id !== currentUser.uid && (
                    <div className="member-actions">
                      <button
                        className="btn btn-sm btn-outline-danger"
                        onClick={() => handleRemoveMember(member.id)}
                        disabled={loading}
                        title="Remove member"
                      >
                        <i className="bi bi-person-x"></i>
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>


    </div>
  );
};

export default MemberManagement;
