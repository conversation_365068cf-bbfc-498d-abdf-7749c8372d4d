import { collection, addDoc, getDocs, query, where } from 'firebase/firestore';
import { db } from './firebase';

// Function to check if sample data already exists
export const checkSampleDataExists = async () => {
  try {
    const opportunitiesSnapshot = await getDocs(collection(db, 'opportunities'));
    return opportunitiesSnapshot.size > 0;
  } catch (error) {
    console.error("Error checking sample data:", error);
    return false;
  }
};

// Function to populate the database with sample data
export const populateSampleData = async (userId) => {
  try {
    // Check if data already exists
    const exists = await checkSampleDataExists();
    if (exists) {
      return { success: true, message: "Sample data already exists" };
    }

    // Sample companies
    const companies = [
      {
        name: "Acme Corporation",
        industry: "Manufacturing",
        size: "Enterprise",
        website: "acme.com",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        name: "TechNova",
        industry: "Technology",
        size: "Mid-Market",
        website: "technova.io",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        name: "Global Finance",
        industry: "Financial Services",
        size: "Enterprise",
        website: "globalfinance.com",
        createdBy: userId,
        createdAt: new Date()
      }
    ];

    // Add companies to Firestore
    const companyIds = [];
    for (const company of companies) {
      const docRef = await addDoc(collection(db, 'companies'), company);
      companyIds.push(docRef.id);
    }

    // Sample contacts
    const contacts = [
      {
        name: "John Smith",
        title: "CEO",
        email: "<EMAIL>",
        phone: "************",
        companyId: companyIds[0],
        createdBy: userId,
        createdAt: new Date()
      },
      {
        name: "Sarah Johnson",
        title: "CTO",
        email: "<EMAIL>",
        phone: "************",
        companyId: companyIds[1],
        createdBy: userId,
        createdAt: new Date()
      },
      {
        name: "Michael Brown",
        title: "CFO",
        email: "<EMAIL>",
        phone: "************",
        companyId: companyIds[2],
        createdBy: userId,
        createdAt: new Date()
      }
    ];

    // Add contacts to Firestore
    const contactIds = [];
    for (const contact of contacts) {
      const docRef = await addDoc(collection(db, 'contacts'), contact);
      contactIds.push(docRef.id);
    }

    // Sample opportunities
    const opportunities = [
      {
        name: "Enterprise Software Deal",
        company: "Acme Corporation",
        companyId: companyIds[0],
        value: 150000,
        status: "Qualified",
        closeDate: new Date(new Date().setMonth(new Date().getMonth() + 2)),
        createdBy: userId,
        createdAt: new Date()
      },
      {
        name: "Cloud Migration Project",
        company: "TechNova",
        companyId: companyIds[1],
        value: 75000,
        status: "Proposal",
        closeDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
        createdBy: userId,
        createdAt: new Date()
      },
      {
        name: "Financial Software Implementation",
        company: "Global Finance",
        companyId: companyIds[2],
        value: 200000,
        status: "Negotiation",
        closeDate: new Date(new Date().setMonth(new Date().getMonth() + 3)),
        createdBy: userId,
        createdAt: new Date()
      }
    ];

    // Add opportunities to Firestore
    const opportunityIds = [];
    for (const opportunity of opportunities) {
      const docRef = await addDoc(collection(db, 'opportunities'), opportunity);
      opportunityIds.push(docRef.id);
    }

    // Sample tasks
    const tasks = [
      {
        title: "Follow up with John",
        dueDate: new Date(new Date().setDate(new Date().getDate() + 3)),
        status: "Not Started",
        priority: "High",
        assignedTo: userId,
        relatedTo: {
          type: "contact",
          id: contactIds[0]
        },
        createdBy: userId,
        createdAt: new Date()
      },
      {
        title: "Prepare proposal for TechNova",
        dueDate: new Date(new Date().setDate(new Date().getDate() + 5)),
        status: "In Progress",
        priority: "Medium",
        assignedTo: userId,
        relatedTo: {
          type: "opportunity",
          id: opportunityIds[1]
        },
        createdBy: userId,
        createdAt: new Date()
      },
      {
        title: "Schedule demo with Global Finance",
        dueDate: new Date(new Date().setDate(new Date().getDate() + 7)),
        status: "Not Started",
        priority: "Low",
        assignedTo: userId,
        relatedTo: {
          type: "opportunity",
          id: opportunityIds[2]
        },
        createdBy: userId,
        createdAt: new Date()
      }
    ];

    // Add tasks to Firestore
    const taskIds = [];
    for (const task of tasks) {
      const docRef = await addDoc(collection(db, 'tasks'), task);
      taskIds.push(docRef.id);
    }

    // Sample relationships
    const relationships = [
      {
        sourceType: "opportunity",
        sourceId: opportunityIds[0],
        targetType: "contact",
        targetId: contactIds[0],
        type: "Primary Contact",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        sourceType: "opportunity",
        sourceId: opportunityIds[1],
        targetType: "contact",
        targetId: contactIds[1],
        type: "Primary Contact",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        sourceType: "opportunity",
        sourceId: opportunityIds[2],
        targetType: "contact",
        targetId: contactIds[2],
        type: "Primary Contact",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        sourceType: "contact",
        sourceId: contactIds[0],
        targetType: "company",
        targetId: companyIds[0],
        type: "Works At",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        sourceType: "contact",
        sourceId: contactIds[1],
        targetType: "company",
        targetId: companyIds[1],
        type: "Works At",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        sourceType: "contact",
        sourceId: contactIds[2],
        targetType: "company",
        targetId: companyIds[2],
        type: "Works At",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        sourceType: "task",
        sourceId: taskIds[0],
        targetType: "contact",
        targetId: contactIds[0],
        type: "Related To",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        sourceType: "task",
        sourceId: taskIds[1],
        targetType: "opportunity",
        targetId: opportunityIds[1],
        type: "Related To",
        createdBy: userId,
        createdAt: new Date()
      },
      {
        sourceType: "task",
        sourceId: taskIds[2],
        targetType: "opportunity",
        targetId: opportunityIds[2],
        type: "Related To",
        createdBy: userId,
        createdAt: new Date()
      }
    ];

    // Add relationships to Firestore
    for (const relationship of relationships) {
      await addDoc(collection(db, 'relationships'), relationship);
    }

    return { success: true, message: "Sample data created successfully" };
  } catch (error) {
    console.error("Error creating sample data:", error);
    return { success: false, message: error.message };
  }
};
