import { collection, doc, setDoc, getDoc, query, where, getDocs, serverTimestamp, updateDoc, deleteDoc } from 'firebase/firestore';
import { createUserWithEmailAndPassword, sendPasswordResetEmail } from 'firebase/auth';
import { db, auth } from './firebase';
import { addOrganizationMember } from './organizationService';
import { addOrganizationToUser } from './userOrganizationsService';
import { v4 as uuidv4 } from 'uuid';

// Collection names
const INVITATIONS_COLLECTION = 'invitations';
const USERS_COLLECTION = 'users';

/**
 * Generate a random password
 * @returns {string} - Random password
 */
const generateRandomPassword = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
};

/**
 * Invite a user to an organization by email
 * @param {string} organizationId - The organization ID
 * @param {string} email - The email address of the user to invite
 * @param {string} role - The role to assign to the user (default: 'member')
 * @param {string} inviterName - The name of the person sending the invitation
 * @returns {Promise<Object>} - The result of the operation
 */
export const inviteUserByEmail = async (organizationId, email, role = 'member', inviterName) => {
  try {
    // Check if the email is already registered
    const usersQuery = query(
      collection(db, USERS_COLLECTION),
      where('email', '==', email)
    );

    const userSnapshot = await getDocs(usersQuery);

    if (!userSnapshot.empty) {
      // User already exists, add them to the organization
      const userDoc = userSnapshot.docs[0];
      const userId = userDoc.id;

      // Add the user to the organization
      const result = await addOrganizationMember(organizationId, userId, role);

      return {
        success: result.success,
        message: result.success ? 'User added to organization' : result.error,
        existingUser: true,
        userId
      };
    }

    // User doesn't exist, create an invitation
    const invitationId = uuidv4();
    const invitationRef = doc(db, INVITATIONS_COLLECTION, invitationId);

    // Generate a random password for the new user
    const password = generateRandomPassword();

    // Create the invitation document
    await setDoc(invitationRef, {
      email,
      organizationId,
      role,
      status: 'pending',
      createdAt: serverTimestamp(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      inviterName,
      tempPassword: password // Store the temporary password in the invitation
    });

    // Create the user account
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Create a user document in Firestore
    await setDoc(doc(db, USERS_COLLECTION, user.uid), {
      name: email.split('@')[0], // Use part of email as name
      email,
      createdAt: new Date(),
      organizationId,
      role,
      photoURL: `https://ui-avatars.com/api/?name=${encodeURIComponent(email.split('@')[0])}&background=random`
    });

    // Add the user to the organization
    await addOrganizationMember(organizationId, user.uid, role);

    // Send password reset email so the user can set their own password
    await sendPasswordResetEmail(auth, email);

    // Update the invitation status
    await updateDoc(invitationRef, {
      status: 'accepted',
      userId: user.uid,
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      message: 'User invited and account created successfully',
      existingUser: false,
      userId: user.uid,
      password // Return the temporary password
    };
  } catch (error) {
    console.error('Error inviting user:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Join an organization using its UUID
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID (UUID)
 * @returns {Promise<Object>} - The result of the operation
 */
export const joinOrganizationByUuid = async (userId, organizationId) => {
  try {
    console.log(`User ${userId} is trying to join organization ${organizationId}`);

    // Check if the organization exists
    const orgRef = doc(db, 'organizations', organizationId);
    const orgDoc = await getDoc(orgRef);

    if (!orgDoc.exists()) {
      return {
        success: false,
        error: 'Organization not found. Please check the UUID and try again.'
      };
    }

    const organization = orgDoc.data();
    console.log('Found organization:', organization.name);

    // Check if user is already a member
    if (organization.members && organization.members.includes(userId)) {
      return {
        success: false,
        error: 'You are already a member of this organization'
      };
    }

    // Add the user to the organization
    const result = await addOrganizationMember(organizationId, userId, 'member');

    if (result.success) {
      // Add organization to user's organizations list
      const addResult = await addOrganizationToUser(userId, organizationId);

      if (addResult.success) {
        return {
          success: true,
          message: `Successfully joined organization: ${organization.name}`
        };
      } else {
        return {
          success: false,
          error: addResult.error || 'Failed to add organization to user'
        };
      }
    } else {
      return {
        success: false,
        error: result.error || 'Failed to join organization'
      };
    }
  } catch (error) {
    console.error('Error joining organization:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get all pending invitations for an organization
 * @param {string} organizationId - The organization ID
 * @returns {Promise<Array>} - Array of invitation objects
 */
export const getOrganizationInvitations = async (organizationId) => {
  try {
    const invitationsQuery = query(
      collection(db, INVITATIONS_COLLECTION),
      where('organizationId', '==', organizationId),
      where('status', '==', 'pending')
    );

    const snapshot = await getDocs(invitationsQuery);

    if (snapshot.empty) {
      return [];
    }

    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting organization invitations:', error);
    return [];
  }
};

/**
 * Cancel an invitation
 * @param {string} invitationId - The invitation ID
 * @returns {Promise<Object>} - The result of the operation
 */
export const cancelInvitation = async (invitationId) => {
  try {
    const invitationRef = doc(db, INVITATIONS_COLLECTION, invitationId);
    const invitationDoc = await getDoc(invitationRef);

    if (!invitationDoc.exists()) {
      return {
        success: false,
        error: 'Invitation not found'
      };
    }

    // Delete the invitation
    await deleteDoc(invitationRef);

    return {
      success: true,
      message: 'Invitation cancelled successfully'
    };
  } catch (error) {
    console.error('Error cancelling invitation:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
