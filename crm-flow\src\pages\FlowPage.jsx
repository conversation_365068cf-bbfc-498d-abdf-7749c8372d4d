import { ReactFlowProvider } from '@xyflow/react';
import { useAuth } from '../contexts/AuthContext';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NewCRMFlow from '../components/Flow/NewCRMFlow';
import Modal from '../components/UI/Modal';
import OpportunityForm from '../components/Forms/OpportunityForm';
import ContactForm from '../components/Forms/ContactForm';
import CompanyForm from '../components/Forms/CompanyForm';
import TaskForm from '../components/Forms/TaskForm';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import 'bootstrap/dist/css/bootstrap.min.css';

const FlowPage = () => {
  const { currentUser, organization } = useAuth();
  const navigate = useNavigate();
  const [showEditModal, setShowEditModal] = useState(false);
  const [editNodeType, setEditNodeType] = useState(null);
  const [editNodeId, setEditNodeId] = useState(null);
  const [nodeData, setNodeData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Listen for editNode events
  useEffect(() => {
    const handleEditNode = async (event) => {
      const { type, id } = event.detail;
      console.log('Edit node event received:', type, id);

      setEditNodeType(type);
      setEditNodeId(id);
      setLoading(true);
      setError('');

      try {
        // Map node types to collection names
        const collectionMap = {
          'opportunity': 'opportunities',
          'contact': 'contacts',
          'company': 'companies',
          'task': 'tasks',
          'checklist': 'checklists',
          'note': 'notes',
          'organization': 'organizations'
        };

        // Get the correct collection name
        const collectionName = collectionMap[type] || `${type}s`;
        console.log(`Fetching document from collection: ${collectionName}, ID: ${id}`);

        // Fetch the node data from Firestore
        const docRef = doc(db, collectionName, id);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          const data = { id, ...docSnap.data() };
          console.log('Node data fetched:', data);
          setNodeData(data);
          setShowEditModal(true);
        } else {
          console.error('No such document!');
          setError(`Could not find ${type} with ID ${id} in collection ${collectionName}`);
        }
      } catch (error) {
        console.error('Error fetching node data:', error);
        setError(`Error fetching ${type} data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    // Add event listener
    window.addEventListener('editNode', handleEditNode);

    // Clean up
    return () => {
      window.removeEventListener('editNode', handleEditNode);
    };
  }, []);

  // Handle modal close
  const handleCloseModal = () => {
    setShowEditModal(false);
    setEditNodeType(null);
    setEditNodeId(null);
    setNodeData(null);
  };

  // Handle successful edit
  const handleEditSuccess = (docId, updatedData) => {
    setShowEditModal(false);

    // Emit a custom event to notify the flow view that a node has been updated
    const event = new CustomEvent('nodeUpdated', {
      detail: {
        type: editNodeType,
        id: docId,
        data: updatedData
      }
    });
    window.dispatchEvent(event);

    console.log(`Node updated event emitted: ${editNodeType}-${docId}`);
  };

  // Render the appropriate form based on node type
  const renderEditForm = () => {
    if (!nodeData) return null;

    switch (editNodeType) {
      case 'opportunity':
        return (
          <OpportunityForm
            opportunityData={nodeData}
            isEditing={true}
            onClose={handleCloseModal}
            onSuccess={handleEditSuccess}
          />
        );
      case 'contact':
        return (
          <ContactForm
            contactData={nodeData}
            isEditing={true}
            onClose={handleCloseModal}
            onSuccess={handleEditSuccess}
          />
        );
      case 'company':
        return (
          <CompanyForm
            companyData={nodeData}
            isEditing={true}
            onClose={handleCloseModal}
            onSuccess={handleEditSuccess}
          />
        );
      case 'task':
        return (
          <TaskForm
            taskData={nodeData}
            isEditing={true}
            onClose={handleCloseModal}
            onSuccess={handleEditSuccess}
          />
        );
      case 'checklist':
        // For now, checklist editing is handled directly in the node
        handleCloseModal();
        return null;
      default:
        return <div>Editing {editNodeType} is not supported yet.</div>;
    }
  };



  return (
    <div style={{
      height: '100vh',
      width: '100%',
      overflow: 'hidden',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      padding: 0,
      margin: 0
    }}>
      <ReactFlowProvider>
        <NewCRMFlow
          user={currentUser}
          organization={organization}
        />
      </ReactFlowProvider>

      {/* Edit Node Modal */}
      <Modal
        show={showEditModal}
        onClose={handleCloseModal}
        size="lg"
      >
        {loading ? (
          <div className="text-center p-4">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2">Loading {editNodeType} data...</p>
          </div>
        ) : error ? (
          <div className="alert alert-danger m-3" role="alert">
            {error}
          </div>
        ) : (
          renderEditForm()
        )}
      </Modal>
    </div>
  );
};

export default FlowPage;
