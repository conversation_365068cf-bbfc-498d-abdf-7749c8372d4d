// Simple audio player utility for handling sounds in the application

// Global audio element for task due sound
let taskDueAudio = null;

const audioPlayer = {
  // Play the task due sound in a loop
  playTaskDueSound() {
    console.log('Creating and playing task due sound');
    
    try {
      // Stop any existing sound first
      this.stopTaskDueSound();
      
      // Create a new audio element
      taskDueAudio = new Audio('/sounds/task_due.mp3');
      
      // Configure the audio element
      taskDueAudio.loop = true;
      taskDueAudio.volume = 1.0;
      
      // Add event listeners for debugging
      taskDueAudio.addEventListener('play', () => {
        console.log('Task due sound started playing');
      });
      
      taskDueAudio.addEventListener('pause', () => {
        console.log('Task due sound paused');
      });
      
      taskDueAudio.addEventListener('ended', () => {
        console.log('Task due sound ended (should not happen with loop=true)');
      });
      
      // Play the sound
      taskDueAudio.play().catch(error => {
        console.error('Error playing task due sound:', error);
      });
      
      return true;
    } catch (error) {
      console.error('Error setting up task due sound:', error);
      return false;
    }
  },
  
  // Stop the task due sound
  stopTaskDueSound() {
    console.log('Attempting to stop task due sound');
    
    try {
      if (taskDueAudio) {
        console.log('Found task due audio element, stopping it');
        
        // Pause and reset the audio
        taskDueAudio.pause();
        taskDueAudio.currentTime = 0;
        taskDueAudio.loop = false;
        
        // Remove event listeners
        taskDueAudio.onended = null;
        taskDueAudio.onpause = null;
        taskDueAudio.onplay = null;
        
        // Clear the reference
        taskDueAudio = null;
        
        console.log('Task due sound stopped and reference cleared');
        return true;
      } else {
        console.log('No task due audio element found to stop');
        return false;
      }
    } catch (error) {
      console.error('Error stopping task due sound:', error);
      return false;
    }
  },
  
  // Check if the task due sound is playing
  isTaskDueSoundPlaying() {
    return taskDueAudio !== null && !taskDueAudio.paused;
  }
};

// Add a global click handler to stop sounds when clicking outside nodes
document.addEventListener('click', (e) => {
  // Only stop if clicking outside a node
  if (!e.target.closest('.node-card') && !e.target.closest('.node-alarm-indicator')) {
    console.log('Document clicked outside nodes, stopping sounds');
    audioPlayer.stopTaskDueSound();
  }
}, { capture: true });

export default audioPlayer;
