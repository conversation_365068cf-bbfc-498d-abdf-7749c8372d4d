import React, { useEffect } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import './ModalStyles.css';

const Modal = ({ show, onClose, title, children, size = 'md' }) => {
  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (show) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [show]);

  if (!show) return null;

  // Handle click outside of modal content to close
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="modal fade show"
      style={{
        display: 'block',
        backgroundColor: 'rgba(0,0,0,0.5)',
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1050,
        overflow: 'hidden',
        outline: 0
      }}
      onClick={handleBackdropClick}
    >
      <div className={`modal-dialog modal-${size} modal-dialog-centered`}>
        <div className="modal-content custom-modal">
          {title && (
            <div className="modal-header">
              <h5 className="modal-title">{title}</h5>
              <button
                type="button"
                className="btn-close"
                onClick={onClose}
                aria-label="Close"
              ></button>
            </div>
          )}
          <div className="modal-body p-4">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
