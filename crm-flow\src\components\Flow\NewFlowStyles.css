/* New, consolidated styles for the Flow View */

/* Base styles for the flow container */
.flow-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #1a1a1a;
}

/* ReactFlow base styles */
.react-flow {
  background-color: #1a1a1a;
}

/* Background grid */
.react-flow__background {
  z-index: 0;
}

.react-flow__background-pattern.dots {
  color: rgba(255, 255, 255, 0.5);
  --pattern-size: 1.5px;
  --pattern-spacing: 20px;
}

/* Edge styles */
.react-flow__edge-path {
  stroke: #b1b1b7;
  stroke-width: 1.5;
}

.react-flow__edge.selected .react-flow__edge-path,
.react-flow__edge.animated .react-flow__edge-path {
  stroke: #22c55e;
  stroke-width: 2;
}

/* Edge delete button */
.edge-delete-button {
  width: 24px;
  height: 24px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.edge-delete-button:hover {
  background-color: #dc2626;
  transform: scale(1.1);
}

.edge-delete-button:active {
  transform: scale(0.95);
}

/* Edge labels */
.edge-label {
  background-color: #6c757d;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  pointer-events: all;
  white-space: nowrap;
  user-select: none;
}

.edge-label.selected,
.edge-label.animated {
  background-color: #22c55e;
}

/* Node styles */
.react-flow__node {
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: box-shadow 0.2s ease, opacity 0.3s ease;
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #3b82f6, 0 2px 10px rgba(0, 0, 0, 0.4);
}

/* Focus mode - when a node is focused, other nodes are dimmed */
.react-flow.focus-mode .react-flow__node:not(.focused) {
  opacity: 0.3;
}

.react-flow.focus-mode .react-flow__edge:not(.focused) {
  opacity: 0.2;
}

/* Node types */
.node-opportunity {
  border: 1px solid #ff6b6b;
  background-color: rgba(40, 40, 40, 0.95);
}

.node-contact {
  border: 1px solid #4ecdc4;
  background-color: rgba(40, 40, 40, 0.95);
}

.node-company {
  border: 1px solid #45b7d1;
  background-color: rgba(40, 40, 40, 0.95);
}

.node-task {
  border: 1px solid #ffd166;
  background-color: rgba(40, 40, 40, 0.95);
}

.node-checklist {
  border: 1px solid #a78bfa;
  background-color: rgba(40, 40, 40, 0.95);
}

/* Node content */
.node-card {
  width: 220px;
  color: #e0e0e0;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(0, 0, 0, 0.2);
}

.node-title {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
}

.node-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
}

.node-body {
  padding: 10px 12px;
}

.node-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.node-detail {
  font-size: 11px;
  margin-bottom: 4px;
  color: #b0b0b0;
}

.node-date {
  font-size: 10px;
  color: #888;
  margin-top: 8px;
}

/* Enhanced opportunity value styling */
.opportunity-value {
  background-color: rgba(255, 107, 107, 0.15);
  border-left: 3px solid #ff6b6b;
  padding: 8px 10px;
  margin: 10px 0;
  border-radius: 4px;
  display: flex;
  align-items: baseline;
  font-weight: 500;
}

.opportunity-currency {
  font-size: 14px;
  color: #ff6b6b;
  margin-right: 2px;
}

.opportunity-amount {
  font-size: 16px;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.opportunity-currency-code {
  font-size: 10px;
  color: #b0b0b0;
  margin-left: 4px;
}

/* Node menu */
.node-menu {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
  background-color: #333;
  border-radius: 4px;
  padding: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
  transform: translateX(-50%) translateY(10px);
}

.react-flow__node:hover .node-menu,
.node-card:hover .node-menu,
.note-node:hover .node-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.node-menu-btn {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 14px;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.node-menu-btn:hover {
  transform: scale(1.1);
}

.node-menu-btn:active {
  transform: scale(0.95);
}

.edit-btn {
  background-color: #3b82f6;
}

.edit-btn:hover {
  background-color: #2563eb;
}

.archive-btn {
  background-color: #f59e0b;
}

.archive-btn:hover {
  background-color: #d97706;
}

.delete-btn {
  background-color: #ef4444;
}

.delete-btn:hover {
  background-color: #dc2626;
}

.restore-btn {
  background-color: #10b981;
}

.restore-btn:hover {
  background-color: #059669;
}

/* Archived nodes */
.node-archived {
  opacity: 0.7;
}

.node-archived::after {
  content: 'ARCHIVED';
  position: absolute;
  top: 0;
  right: 0;
  background-color: #f59e0b;
  color: white;
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 0 4px 0 4px;
  font-weight: bold;
}

/* Due task nodes */
.node-due {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 1px #ef4444, 0 2px 10px rgba(239, 68, 68, 0.4) !important;
}

.node-playing {
  animation: pulse-border 1.5s infinite;
}

@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.7), 0 2px 10px rgba(239, 68, 68, 0.4);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.7), 0 2px 15px rgba(239, 68, 68, 0.6);
  }

  100% {
    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.7), 0 2px 10px rgba(239, 68, 68, 0.4);
  }
}

.node-alarm-indicator {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 24px;
  height: 24px;
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  animation: pulse 1.5s infinite;
  z-index: 5;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Control panel */
.control-panel {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  background-color: rgba(51, 51, 51, 0.9);
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.control-btn {
  background-color: #444;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  position: relative;
}

.control-btn:hover {
  background-color: #555;
}

.control-btn.active {
  background-color: #3b82f6;
}

.control-btn.active:hover {
  background-color: #2563eb;
}

.control-btn.warning {
  background-color: #f59e0b;
}

.notification-dot {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.notification-dot.red {
  background-color: #ef4444;
}

.notification-dot.green {
  background-color: #10b981;
}

/* Notification */
.flow-notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 90%;
  width: auto;
  pointer-events: none;
}

.flow-notification-content {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.flow-notification-warning {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  color: #b45309;
}

.flow-notification-info {
  background-color: #e0f2fe;
  border: 1px solid #3b82f6;
  color: #1d4ed8;
}

.flow-notification-success {
  background-color: #dcfce7;
  border: 1px solid #10b981;
  color: #047857;
}

.flow-notification-error {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  color: #b91c1c;
}

/* Archived opportunities panel */
.archived-panel {
  position: absolute;
  top: 70px;
  right: 20px;
  width: 350px;
  max-height: 500px;
  background-color: #222;
  color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #444;
}

.archived-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #333;
  border-bottom: 1px solid #444;
}

.archived-panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.archived-panel-close {
  background: none;
  border: none;
  font-size: 20px;
  line-height: 1;
  cursor: pointer;
  color: #fff;
}

.archived-panel-body {
  padding: 0;
  overflow-y: auto;
  flex: 1;
}

.archived-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.archived-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #444;
  transition: background-color 0.2s;
}

.archived-item:hover {
  background-color: #333;
}

.archived-item-content {
  flex: 1;
}

.archived-item-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.archived-item-details {
  display: flex;
  flex-wrap: wrap;
  font-size: 12px;
  color: #aaa;
}

.archived-item-restore {
  padding: 4px 8px;
  font-size: 12px;
  background-color: #444;
  color: #fff;
  border: 1px solid #666;
  border-radius: 4px;
  cursor: pointer;
}

.archived-item-restore:hover {
  background-color: #10b981;
  color: white;
  border-color: #10b981;
}

/* Checklist node styles */
.checklist-items {
  max-height: 120px;
  overflow-y: auto;
  margin-bottom: 8px;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.checklist-items::-webkit-scrollbar {
  width: 4px;
}

.checklist-items::-webkit-scrollbar-track {
  background: transparent;
}

.checklist-items::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.checklist-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.checklist-item:last-child {
  border-bottom: none;
}

.checklist-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 14px;
  height: 14px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  margin-right: 8px;
  position: relative;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.2);
}

.checklist-checkbox:checked {
  background-color: #a78bfa;
  border-color: #a78bfa;
}

.checklist-checkbox:checked::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 10px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.checklist-label {
  font-size: 11px;
  color: #e0e0e0;
  flex: 1;
  cursor: pointer;
  word-break: break-word;
}

.checklist-label.completed {
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.5);
}

.checklist-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checklist-item:hover .checklist-actions {
  opacity: 1;
}

.checklist-btn {
  background: none;
  border: none;
  color: #e0e0e0;
  font-size: 10px;
  padding: 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.checklist-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.checklist-btn.edit {
  color: #45b7d1;
}

.checklist-btn.delete {
  color: #ff6b6b;
}

.checklist-progress {
  height: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  margin: 8px 0;
  overflow: hidden;
}

.checklist-progress-bar {
  height: 100%;
  background-color: #a78bfa;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.checklist-form {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.checklist-input {
  flex: 1;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 11px;
  padding: 4px 8px;
  transition: border-color 0.2s ease;
}

.checklist-input:focus {
  outline: none;
  border-color: #a78bfa;
}

.checklist-add-btn {
  background-color: #a78bfa;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.checklist-add-btn:hover {
  background-color: #9061f9;
}

.checklist-add-btn:disabled {
  background-color: rgba(167, 139, 250, 0.5);
  cursor: not-allowed;
}

.checklist-edit-form {
  display: flex;
  flex: 1;
  gap: 4px;
}

.checklist-edit-input {
  flex: 1;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid #a78bfa;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 11px;
  padding: 4px 8px;
}

.checklist-save-btn {
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.checklist-save-btn:hover {
  background-color: #059669;
}

.checklist-empty {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
  padding: 8px 0;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -20px);
  }

  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}