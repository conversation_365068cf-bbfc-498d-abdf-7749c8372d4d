Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA650) msys-2.0.dll+0x1FEBA
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210285FF9, 0007FFFFB608, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB750  0002100690B4 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA30  00021006A49D (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA81830000 ntdll.dll
7FFA80680000 KERNEL32.DLL
7FFA7EEE0000 KERNELBASE.dll
7FFA7FC50000 USER32.dll
7FFA7F4F0000 win32u.dll
7FFA817B0000 GDI32.dll
7FFA7EB60000 gdi32full.dll
7FFA7F3D0000 msvcp_win.dll
7FFA7F2B0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA80A60000 advapi32.dll
7FFA81520000 msvcrt.dll
7FFA7F880000 sechost.dll
7FFA7EEB0000 bcrypt.dll
7FFA80B70000 RPCRT4.dll
7FFA7E240000 CRYPTBASE.DLL
7FFA7F470000 bcryptPrimitives.dll
7FFA7F520000 IMM32.DLL
