/* Consistent Table Styling */
.table-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 1.5rem;
  border: 2px solid #d1d5db;
}

.table {
  width: 100%;
  margin-bottom: 0;
  color: #333;
  vertical-align: middle;
  border-color: #d1d5db;
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid #d1d5db;
}

.table> :not(caption)>*>* {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #d1d5db;
  border-right: 1px solid #d1d5db;
}

.table> :not(caption)>*>*:last-child {
  border-right: 1px solid #d1d5db;
}

.table>thead {
  background-color: #f8f9fa;
  border-bottom: 2px solid #d1d5db;
}

.table>thead>tr>th {
  border-bottom: 2px solid #9ca3af;
  border-right: 1px solid #d1d5db;
}

.table>thead>tr>th:last-child {
  border-right: 1px solid #d1d5db;
}

.table>thead th {
  font-weight: 600;
  color: #4b5563;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  vertical-align: bottom;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.table>tbody>tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.table>tbody>tr:last-child>td {
  border-bottom: 1px solid #d1d5db;
}

.table>tbody>tr>td {
  border-right: 1px solid #d1d5db;
}

.table>tbody>tr>td:last-child {
  border-right: 1px solid #d1d5db;
}

.table .btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.table .btn-group-sm {
  gap: 0.25rem;
}

.table .badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
}

.table .badge-primary {
  background-color: #2563eb;
  color: white;
}

.table .badge-success {
  background-color: #10b981;
  color: white;
}

.table .badge-warning {
  background-color: #f59e0b;
  color: white;
}

.table .badge-danger {
  background-color: #ef4444;
  color: white;
}

.table .badge-info {
  background-color: #3b82f6;
  color: white;
}

.table .badge-secondary {
  background-color: #6b7280;
  color: white;
}

/* Table pagination */
.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.pagination {
  margin-bottom: 0;
}

.pagination .page-item .page-link {
  color: #2563eb;
  padding: 0.375rem 0.75rem;
  border-color: #e9ecef;
}

.pagination .page-item.active .page-link {
  background-color: #2563eb;
  border-color: #2563eb;
  color: white;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #f8f9fa;
  border-color: #e9ecef;
}

/* Table filters and search */
.table-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
}

.table-search {
  position: relative;
  max-width: 300px;
}

.table-search input {
  padding-left: 2.5rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.table-search input:focus {
  background-color: #fff;
  border-color: #2563eb;
  box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
}

.table-search i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

/* Empty state */
.table-empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: #6c757d;
}

.table-empty-state i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #e9ecef;
}

.table-empty-state h5 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.table-empty-state p {
  max-width: 400px;
  margin: 0 auto 1rem;
}

/* Responsive tables */
@media (max-width: 768px) {
  .table-responsive {
    border-radius: 8px;
  }

  .table> :not(caption)>*>* {
    padding: 0.5rem 0.75rem;
  }

  .table-filters {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .table-search {
    width: 100%;
    max-width: none;
  }
}