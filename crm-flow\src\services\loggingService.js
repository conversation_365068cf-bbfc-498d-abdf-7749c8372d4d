/**
 * Logging Service
 * 
 * This service provides centralized logging functionality with different log levels
 * and environment-specific behavior.
 */

import { environment } from '../config/environment';

// Log levels
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
};

// Get the current log level from environment
const getCurrentLogLevel = () => {
  const configLevel = environment.logging.logLevel.toLowerCase();
  
  switch (configLevel) {
    case 'debug': return LOG_LEVELS.DEBUG;
    case 'info': return LOG_LEVELS.INFO;
    case 'warn': return LOG_LEVELS.WARN;
    case 'error': return LOG_LEVELS.ERROR;
    case 'none': return LOG_LEVELS.NONE;
    default: return environment.isDevelopment ? LOG_LEVELS.DEBUG : LOG_LEVELS.WARN;
  }
};

// Current log level
const currentLogLevel = getCurrentLogLevel();

/**
 * Format a log message with timestamp and context
 * @param {string} level - The log level
 * @param {string} context - The context of the log
 * @param {string} message - The log message
 * @returns {string} - The formatted log message
 */
const formatLogMessage = (level, context, message) => {
  const timestamp = new Date().toISOString();
  return `[${timestamp}] [${level}] [${context}] ${message}`;
};

/**
 * Log a debug message
 * @param {string} context - The context of the log
 * @param {string} message - The log message
 * @param {any} data - Additional data to log
 */
export const debug = (context, message, data = null) => {
  if (currentLogLevel <= LOG_LEVELS.DEBUG) {
    const formattedMessage = formatLogMessage('DEBUG', context, message);
    console.debug(formattedMessage, data !== null ? data : '');
  }
};

/**
 * Log an info message
 * @param {string} context - The context of the log
 * @param {string} message - The log message
 * @param {any} data - Additional data to log
 */
export const info = (context, message, data = null) => {
  if (currentLogLevel <= LOG_LEVELS.INFO) {
    const formattedMessage = formatLogMessage('INFO', context, message);
    console.info(formattedMessage, data !== null ? data : '');
  }
};

/**
 * Log a warning message
 * @param {string} context - The context of the log
 * @param {string} message - The log message
 * @param {any} data - Additional data to log
 */
export const warn = (context, message, data = null) => {
  if (currentLogLevel <= LOG_LEVELS.WARN) {
    const formattedMessage = formatLogMessage('WARN', context, message);
    console.warn(formattedMessage, data !== null ? data : '');
  }
};

/**
 * Log an error message
 * @param {string} context - The context of the log
 * @param {string} message - The log message
 * @param {Error|any} error - The error object or additional data
 */
export const error = (context, message, error = null) => {
  if (currentLogLevel <= LOG_LEVELS.ERROR) {
    const formattedMessage = formatLogMessage('ERROR', context, message);
    console.error(formattedMessage, error !== null ? error : '');
    
    // In production, we might want to send errors to a monitoring service
    if (environment.isProduction && error instanceof Error) {
      // Here you could integrate with an error monitoring service like Sentry
      // Example: Sentry.captureException(error);
    }
  }
};

/**
 * Log a group of messages
 * @param {string} context - The context of the log
 * @param {string} label - The group label
 * @param {Function} logFunction - Function that contains the logs to group
 */
export const group = (context, label, logFunction) => {
  if (currentLogLevel <= LOG_LEVELS.DEBUG) {
    const formattedLabel = formatLogMessage('GROUP', context, label);
    console.group(formattedLabel);
    logFunction();
    console.groupEnd();
  } else {
    // Just execute the function without grouping if log level is higher
    logFunction();
  }
};

/**
 * Log a performance measurement
 * @param {string} context - The context of the log
 * @param {string} label - The measurement label
 * @param {number} duration - The duration in milliseconds
 */
export const performance = (context, label, duration) => {
  if (currentLogLevel <= LOG_LEVELS.DEBUG || environment.logging.logPerformance) {
    const formattedMessage = formatLogMessage('PERF', context, `${label}: ${duration.toFixed(2)}ms`);
    console.log(formattedMessage);
  }
};

// Export the logging service
export const loggingService = {
  debug,
  info,
  warn,
  error,
  group,
  performance,
  LOG_LEVELS
};

export default loggingService;
