import React, { useState } from 'react';
import { X, Plus } from 'lucide-react';
import { nodeRegistry } from '../systems/NodeSystem';
import { 
  Target, Building, User, CheckSquare, Calendar, FileText 
} from 'lucide-react';

const iconMap = {
  Target, Building, User, CheckSquare, Calendar, FileText
};

const NodeTypeSelector = ({ isOpen, onClose, onSelectType, position }) => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  if (!isOpen) return null;

  const nodeTypes = nodeRegistry.getAllNodeTypes();
  const categories = ['all', ...new Set(nodeTypes.map(type => type.category))];
  
  const filteredTypes = selectedCategory === 'all' 
    ? nodeTypes 
    : nodeTypes.filter(type => type.category === selectedCategory);

  const handleTypeSelect = (nodeType) => {
    onSelectType(nodeType.name, position);
    onClose();
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '12px',
        padding: '24px',
        width: '90%',
        maxWidth: '600px',
        maxHeight: '80vh',
        overflow: 'auto',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <h2 style={{ 
            margin: 0, 
            fontSize: '24px', 
            fontWeight: 'bold',
            color: '#1f2937'
          }}>
            Create New Node
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '4px',
              borderRadius: '4px'
            }}
          >
            <X size={24} color="#6b7280" />
          </button>
        </div>

        {/* Category Filter */}
        <div style={{ marginBottom: '20px' }}>
          <div style={{
            display: 'flex',
            gap: '8px',
            flexWrap: 'wrap'
          }}>
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                style={{
                  padding: '6px 12px',
                  borderRadius: '20px',
                  border: '1px solid #e5e7eb',
                  background: selectedCategory === category ? '#3b82f6' : 'white',
                  color: selectedCategory === category ? 'white' : '#6b7280',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  textTransform: 'capitalize',
                  transition: 'all 0.2s ease'
                }}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Node Types Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
          gap: '16px'
        }}>
          {filteredTypes.map(nodeType => {
            const IconComponent = iconMap[nodeType.icon] || Target;
            
            return (
              <div
                key={nodeType.name}
                onClick={() => handleTypeSelect(nodeType)}
                style={{
                  border: '2px solid #e5e7eb',
                  borderRadius: '8px',
                  padding: '16px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  background: 'white'
                }}
                onMouseEnter={(e) => {
                  e.target.style.borderColor = nodeType.color;
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.borderColor = '#e5e7eb';
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = 'none';
                }}
              >
                {/* Icon and Title */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '12px'
                }}>
                  <div style={{
                    background: nodeType.color,
                    color: 'white',
                    padding: '8px',
                    borderRadius: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <IconComponent size={20} />
                  </div>
                  <div>
                    <h3 style={{
                      margin: 0,
                      fontSize: '18px',
                      fontWeight: 'bold',
                      color: '#1f2937'
                    }}>
                      {nodeType.label}
                    </h3>
                    <div style={{
                      fontSize: '12px',
                      color: '#6b7280',
                      textTransform: 'capitalize'
                    }}>
                      {nodeType.category}
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p style={{
                  margin: '0 0 12px 0',
                  fontSize: '14px',
                  color: '#6b7280',
                  lineHeight: '1.4'
                }}>
                  {nodeType.description}
                </p>

                {/* Properties Preview */}
                <div style={{
                  fontSize: '12px',
                  color: '#9ca3af'
                }}>
                  {nodeType.properties.length} properties
                </div>

                {/* Create Button */}
                <div style={{
                  marginTop: '12px',
                  display: 'flex',
                  justifyContent: 'flex-end'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '4px 8px',
                    background: nodeType.color,
                    color: 'white',
                    borderRadius: '4px',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}>
                    <Plus size={14} />
                    Create
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div style={{
          marginTop: '24px',
          padding: '16px',
          background: '#f9fafb',
          borderRadius: '8px',
          fontSize: '14px',
          color: '#6b7280'
        }}>
          <strong>Tip:</strong> You can create connections between nodes by dragging from the handles on the sides of each node.
        </div>
      </div>
    </div>
  );
};

export default NodeTypeSelector;
