.plugins-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.plugins-header {
  margin-bottom: 2rem;
  text-align: center;
}

.plugins-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.plugins-header p {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

.plugins-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.plugin-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.plugin-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.plugin-icon {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.plugin-icon i {
  font-size: 3rem;
}

.plugin-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.plugin-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.plugin-content p {
  color: #64748b;
  margin-bottom: 1.5rem;
  flex: 1;
}

.plugin-content .btn {
  align-self: flex-start;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  border-radius: 6px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .plugins-container {
    padding: 1.5rem;
  }
  
  .plugins-grid {
    grid-template-columns: 1fr;
  }
  
  .plugins-header h1 {
    font-size: 2rem;
  }
}
