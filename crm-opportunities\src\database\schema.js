// Database schema for CRM application using localStorage for browser compatibility
// Note: In a real application, you would use a proper database
// This is a simplified version using localStorage for demo purposes

class LocalStorageDB {
  constructor() {
    this.dbName = 'crm_db';
    this.initializeStorage();
  }

  initializeStorage() {
    if (!localStorage.getItem(this.dbName)) {
      localStorage.setItem(this.dbName, JSON.stringify({
        companies: [],
        contacts: [],
        opportunities: [],
        activities: [],
        nodes: [], // For universal node system
        nextId: {
          companies: 1,
          contacts: 1,
          opportunities: 1,
          activities: 1,
          nodes: 1
        }
      }));
    }
  }

  getData() {
    return JSON.parse(localStorage.getItem(this.dbName));
  }

  saveData(data) {
    localStorage.setItem(this.dbName, JSON.stringify(data));
  }

  prepare(query) {
    // Mock prepare method for compatibility
    return {
      run: (...params) => this.run(query, params),
      get: (...params) => this.get(query, params),
      all: (...params) => this.all(query, params)
    };
  }

  run(query, params = []) {
    // Simple mock implementation
    const data = this.getData();

    if (query.includes('INSERT INTO companies')) {
      const [name, industry, website, phone, email, address] = params;
      const newCompany = {
        id: data.nextId.companies++,
        name, industry, website, phone, email, address,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      data.companies.push(newCompany);
      this.saveData(data);
      return { lastInsertRowid: newCompany.id };
    }

    if (query.includes('INSERT INTO contacts')) {
      const [company_id, first_name, last_name, email, phone, position] = params;
      const newContact = {
        id: data.nextId.contacts++,
        company_id, first_name, last_name, email, phone, position,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      data.contacts.push(newContact);
      this.saveData(data);
      return { lastInsertRowid: newContact.id };
    }

    if (query.includes('INSERT INTO opportunities')) {
      const [title, description, company_id, contact_id, value, stage, probability, expected_close_date, position_x, position_y] = params;
      const newOpportunity = {
        id: data.nextId.opportunities++,
        title, description, company_id, contact_id, value, stage, probability, expected_close_date, position_x, position_y,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      data.opportunities.push(newOpportunity);
      this.saveData(data);
      return { lastInsertRowid: newOpportunity.id };
    }

    if (query.includes('INSERT INTO activities')) {
      const [opportunity_id, type, description, date] = params;
      const newActivity = {
        id: data.nextId.activities++,
        opportunity_id, type, description, date: date || new Date().toISOString(),
        created_at: new Date().toISOString()
      };
      data.activities.push(newActivity);
      this.saveData(data);
      return { lastInsertRowid: newActivity.id };
    }

    if (query.includes('UPDATE opportunities') && query.includes('position_x')) {
      const [x, y, id] = params;
      const data = this.getData();
      const opportunity = data.opportunities.find(o => o.id === id);
      if (opportunity) {
        opportunity.position_x = x;
        opportunity.position_y = y;
        opportunity.updated_at = new Date().toISOString();
        this.saveData(data);
      }
      return { changes: 1 };
    }

    if (query.includes('UPDATE opportunities')) {
      const [title, description, company_id, contact_id, value, stage, probability, expected_close_date, position_x, position_y, id] = params;
      const data = this.getData();
      const opportunity = data.opportunities.find(o => o.id === id);
      if (opportunity) {
        Object.assign(opportunity, {
          title, description, company_id, contact_id, value, stage, probability, expected_close_date, position_x, position_y,
          updated_at: new Date().toISOString()
        });
        this.saveData(data);
      }
      return { changes: 1 };
    }

    if (query.includes('DELETE FROM opportunities')) {
      const [id] = params;
      const data = this.getData();
      data.opportunities = data.opportunities.filter(o => o.id !== id);
      this.saveData(data);
      return { changes: 1 };
    }

    return { changes: 0, lastInsertRowid: 0 };
  }

  get(query, params = []) {
    const data = this.getData();

    if (query.includes('SELECT COUNT(*) as count FROM companies')) {
      return { count: data.companies.length };
    }

    return null;
  }

  all(query, params = []) {
    const data = this.getData();

    if (query.includes('FROM opportunities o') && query.includes('LEFT JOIN companies c')) {
      return data.opportunities.map(opp => {
        const company = data.companies.find(c => c.id === opp.company_id);
        const contact = data.contacts.find(c => c.id === opp.contact_id);
        return {
          ...opp,
          company_name: company?.name || null,
          contact_name: contact ? `${contact.first_name} ${contact.last_name}` : null
        };
      });
    }

    if (query.includes('FROM companies') && query.includes('ORDER BY name')) {
      return data.companies.sort((a, b) => a.name.localeCompare(b.name));
    }

    if (query.includes('FROM contacts c') && query.includes('LEFT JOIN companies co')) {
      return data.contacts.map(contact => {
        const company = data.companies.find(c => c.id === contact.company_id);
        return {
          ...contact,
          company_name: company?.name || null
        };
      }).sort((a, b) => `${a.last_name} ${a.first_name}`.localeCompare(`${b.last_name} ${b.first_name}`));
    }

    if (query.includes('FROM contacts WHERE company_id')) {
      const [companyId] = params;
      return data.contacts.filter(c => c.company_id === companyId)
        .sort((a, b) => `${a.last_name} ${a.first_name}`.localeCompare(`${b.last_name} ${b.first_name}`));
    }

    if (query.includes('FROM activities WHERE opportunity_id')) {
      const [opportunityId] = params;
      return data.activities.filter(a => a.opportunity_id === opportunityId)
        .sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    return [];
  }

  exec(query) {
    // Mock exec for table creation - no-op since we use localStorage
    console.log('Table creation query (no-op in localStorage):', query);
  }

  pragma(statement) {
    // Mock pragma - no-op
    console.log('Pragma statement (no-op in localStorage):', statement);
  }
}

const db = new LocalStorageDB();

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Create tables (no-op for localStorage, but kept for compatibility)
const createTables = () => {
  console.log('Database tables created successfully (localStorage mode)');
};

// Insert sample data
const insertSampleData = () => {
  const data = db.getData();

  // Sample companies
  const companies = [
    { name: 'TechCorp Inc', industry: 'Technology', website: 'https://techcorp.com', phone: '******-0101', email: '<EMAIL>', address: '123 Tech Street, Silicon Valley, CA' },
    { name: 'Global Solutions', industry: 'Consulting', website: 'https://globalsolutions.com', phone: '******-0102', email: '<EMAIL>', address: '456 Business Ave, New York, NY' },
    { name: 'Innovation Labs', industry: 'Research', website: 'https://innovationlabs.com', phone: '******-0103', email: '<EMAIL>', address: '789 Research Blvd, Boston, MA' }
  ];

  companies.forEach(company => {
    const newCompany = {
      id: data.nextId.companies++,
      ...company,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    data.companies.push(newCompany);
  });

  // Sample contacts
  const contacts = [
    { company_id: 1, first_name: 'John', last_name: 'Smith', email: '<EMAIL>', phone: '******-0201', position: 'CTO' },
    { company_id: 1, first_name: 'Sarah', last_name: 'Johnson', email: '<EMAIL>', phone: '******-0202', position: 'VP Sales' },
    { company_id: 2, first_name: 'Mike', last_name: 'Brown', email: '<EMAIL>', phone: '******-0203', position: 'CEO' },
    { company_id: 3, first_name: 'Lisa', last_name: 'Davis', email: '<EMAIL>', phone: '******-0204', position: 'Director' }
  ];

  contacts.forEach(contact => {
    const newContact = {
      id: data.nextId.contacts++,
      ...contact,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    data.contacts.push(newContact);
  });

  // Sample opportunities
  const opportunities = [
    { title: 'Enterprise Software License', description: 'Annual software licensing deal', company_id: 1, contact_id: 1, value: 50000, stage: 'proposal', probability: 75, expected_close_date: '2025-07-15', position_x: 100, position_y: 100 },
    { title: 'Consulting Services', description: 'Digital transformation project', company_id: 2, contact_id: 3, value: 120000, stage: 'negotiation', probability: 60, expected_close_date: '2025-08-01', position_x: 300, position_y: 150 },
    { title: 'Research Partnership', description: 'Joint research and development', company_id: 3, contact_id: 4, value: 80000, stage: 'lead', probability: 25, expected_close_date: '2025-09-30', position_x: 500, position_y: 200 },
    { title: 'Cloud Migration', description: 'Infrastructure modernization', company_id: 1, contact_id: 2, value: 200000, stage: 'qualified', probability: 40, expected_close_date: '2025-08-15', position_x: 200, position_y: 300 }
  ];

  opportunities.forEach(opportunity => {
    const newOpportunity = {
      id: data.nextId.opportunities++,
      ...opportunity,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    data.opportunities.push(newOpportunity);
  });

  db.saveData(data);
  console.log('Sample data inserted successfully');
};

// Initialize database
const initializeDatabase = () => {
  try {
    createTables();

    // Check if data already exists
    const count = db.prepare('SELECT COUNT(*) as count FROM companies').get();
    if (count.count === 0) {
      insertSampleData();
    }

    return db;
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};

export { db, initializeDatabase };
