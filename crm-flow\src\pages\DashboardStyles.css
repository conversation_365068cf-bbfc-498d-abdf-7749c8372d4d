/* Modern Dashboard Styles */

/* Dashboard container */
.dashboard-container {
  padding: 1.5rem;
  height: 100%;
  overflow-y: auto;
  color: #e0e0e0;
}

/* Dashboard header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  margin: 0;
}

.dashboard-actions {
  display: flex;
  gap: 0.5rem;
}

/* Stats section */
.stats-section {
  margin-bottom: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

/* Stats card */
.stats-card {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.8), rgba(15, 23, 42, 0.8));
  border-radius: 10px;
  padding: 1.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);
  pointer-events: none;
}

.stats-card-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
}

.stats-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.stats-card-info {
  flex: 1;
}

.stats-card-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  color: #fff;
}

.stats-card-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* Card styles */
.dashboard-card {
  background-color: rgba(40, 40, 40, 0.95);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-card-header {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.8), rgba(15, 23, 42, 0.8));
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dashboard-card-body {
  padding: 0;
  flex: 1;
  overflow-y: auto;
}

/* List styles */
.dashboard-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.dashboard-list-item {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: background-color 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.dashboard-list-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.dashboard-list-item:last-child {
  border-bottom: none;
}

.dashboard-list-content {
  flex: 1;
}

.dashboard-list-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #fff;
}

.dashboard-list-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-list-meta {
  text-align: right;
  min-width: 100px;
}

.dashboard-list-value {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #fff;
}

.dashboard-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Empty state */
.empty-state {
  padding: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.empty-state-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state-text {
  font-size: 0.875rem;
  margin-bottom: 0;
}

/* Action buttons */
.dashboard-btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.dashboard-btn i {
  margin-right: 0.5rem;
}

.dashboard-btn-primary {
  background-color: #2563eb;
  color: #fff;
}

.dashboard-btn-primary:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
}

.dashboard-btn-warning {
  background-color: #f59e0b;
  color: #fff;
}

.dashboard-btn-warning:hover {
  background-color: #d97706;
  transform: translateY(-1px);
}

.dashboard-btn-outline {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.dashboard-btn-outline:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  color: #fff;
}

.dashboard-btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Responsive adjustments */

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .dashboard-card-header {
    padding: 0.75rem 1rem;
  }

  .dashboard-list-item {
    padding: 0.75rem 1rem;
    flex-direction: column;
  }

  .dashboard-list-meta {
    text-align: left;
    margin-top: 0.5rem;
    min-width: auto;
  }
}