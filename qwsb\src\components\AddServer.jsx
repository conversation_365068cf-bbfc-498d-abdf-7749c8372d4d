import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Title,
  Paper,
  TextInput,
  Button,
  Group,
  Text,
  NumberInput,
  LoadingOverlay,
  Alert,
  Box,
  Tabs,
  Progress
} from '@mantine/core';
import { useForm } from '@mantine/form';
import {
  IconArrowLeft,
  IconPlus,
  IconAlertCircle,
  IconServer,
  IconWorldWww,
  IconDownload
} from '@tabler/icons-react';

function AddServer({ addServer, loading, importServersFromUrl, refreshProgress, totalServersToRefresh }) {
  const navigate = useNavigate();
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState('single');
  const [importSuccess, setImportSuccess] = useState(null);

  const singleServerForm = useForm({
    initialValues: {
      address: '',
      port: 27500, // Default QuakeWorld port
    },
    validate: {
      address: (value) => (value.trim().length === 0 ? 'Address is required' : null),
      port: (value) => (value < 1 || value > 65535 ? 'Port must be between 1 and 65535' : null),
    },
  });

  const importForm = useForm({
    initialValues: {
      url: '',
    },
    validate: {
      url: (value) => {
        if (value.trim().length === 0) return 'URL is required';
        try {
          new URL(value); // Check if it's a valid URL
          return null;
        } catch (e) {
          return 'Invalid URL format';
        }
      },
    },
  });

  const handleSingleServerSubmit = async (values) => {
    try {
      setError(null);
      setSuccess(false);

      const serverInfo = await addServer(values.address, values.port);
      setSuccess(true);

      // Navigate to the server details page after a short delay
      setTimeout(() => {
        navigate(`/server/${values.address}/${values.port}`);
      }, 1500);
    } catch (err) {
      setError(`Failed to add server: ${err.message || err}`);
    }
  };

  const handleImportSubmit = async (values) => {
    try {
      setError(null);
      setImportSuccess(null);

      const successCount = await importServersFromUrl(values.url);
      setImportSuccess(`Successfully imported ${successCount} servers`);

      // Clear the form
      importForm.reset();
    } catch (err) {
      setError(`Failed to import servers: ${err.message || err}`);
    }
  };

  return (
    <Container size="md" py="md">
      <Box pos="relative">
        <LoadingOverlay visible={loading} overlayBlur={2} />

        <Group mb="md">
          <Button leftSection={<IconArrowLeft size={16} />} onClick={() => navigate('/')}>
            Back to Server List
          </Button>
        </Group>

        <Title order={2} mb="md">Add Server</Title>

        {error && (
          <Alert
            icon={<IconAlertCircle size={16} />}
            title="Error"
            color="red"
            mb="md"
          >
            {error}
          </Alert>
        )}

        <Tabs value={activeTab} onChange={setActiveTab} mb="md">
          <Tabs.List>
            <Tabs.Tab value="single" leftSection={<IconServer size={16} />}>
              Single Server
            </Tabs.Tab>
            <Tabs.Tab value="import" leftSection={<IconWorldWww size={16} />}>
              Import from URL
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="single" pt="md">
            <Paper p="md" withBorder>
              {success && (
                <Alert
                  title="Success"
                  color="green"
                  mb="md"
                >
                  Server added successfully! Redirecting to server details...
                </Alert>
              )}

              <form onSubmit={singleServerForm.onSubmit(handleSingleServerSubmit)}>
                <TextInput
                  label="Server Address"
                  placeholder="Enter IP address or hostname"
                  required
                  mb="md"
                  {...singleServerForm.getInputProps('address')}
                />

                <NumberInput
                  label="Port"
                  placeholder="Enter port number"
                  required
                  min={1}
                  max={65535}
                  mb="md"
                  {...singleServerForm.getInputProps('port')}
                />

                <Group justify="flex-end" mt="md">
                  <Button
                    type="submit"
                    leftSection={<IconPlus size={16} />}
                    loading={loading}
                  >
                    Add Server
                  </Button>
                </Group>
              </form>
            </Paper>

            <Paper p="md" withBorder mt="md">
              <Title order={3} mb="md">Common QuakeWorld Servers</Title>
              <Text size="sm" mb="md">
                Here are some popular QuakeWorld servers you can try:
              </Text>

              <Group>
                <Button
                  variant="outline"
                  onClick={() => {
                    singleServerForm.setValues({ address: 'qw.foppa.dk', port: 27501 });
                    singleServerForm.submitForm();
                  }}
                >
                  qw.foppa.dk:27501
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    singleServerForm.setValues({ address: 'qw.foppa.dk', port: 27500 });
                    singleServerForm.submitForm();
                  }}
                >
                  qw.foppa.dk:27500
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    singleServerForm.setValues({ address: 'qw.foppa.dk', port: 27503 });
                    singleServerForm.submitForm();
                  }}
                >
                  qw.foppa.dk:27503
                </Button>
              </Group>
            </Paper>
          </Tabs.Panel>

          <Tabs.Panel value="import" pt="md">
            <Paper p="md" withBorder>
              {importSuccess && (
                <Alert
                  title="Success"
                  color="green"
                  mb="md"
                >
                  {importSuccess}
                </Alert>
              )}

              <form onSubmit={importForm.onSubmit(handleImportSubmit)}>
                <TextInput
                  label="Server List URL"
                  placeholder="Enter URL to a server list (e.g., https://www.quakeservers.net/lists/europe/eu-sv.txt)"
                  required
                  mb="md"
                  {...importForm.getInputProps('url')}
                />

                <Text size="sm" color="dimmed" mb="md">
                  The URL should point to a text file with one server per line in the format: address:port
                </Text>

                {loading && totalServersToRefresh > 0 && (
                  <Box mb="md">
                    <Group position="apart" mb={5}>
                      <Text size="sm">Importing servers: {refreshProgress}%</Text>
                      <Text size="sm">
                        {Math.floor(refreshProgress * totalServersToRefresh / 100)} / {totalServersToRefresh} servers
                      </Text>
                    </Group>
                    <Progress value={refreshProgress} size="md" radius="sm" striped animated />
                  </Box>
                )}

                <Group justify="flex-end" mt="md">
                  <Button
                    type="submit"
                    leftSection={<IconDownload size={16} />}
                    loading={loading}
                  >
                    Import Servers
                  </Button>
                </Group>
              </form>
            </Paper>

            <Paper p="md" withBorder mt="md">
              <Title order={3} mb="md">Example Server Lists</Title>
              <Text size="sm" mb="md">
                Here are some server lists you can import:
              </Text>

              <Group>
                <Button
                  variant="outline"
                  onClick={() => {
                    importForm.setValues({ url: 'https://www.quakeservers.net/lists/europe/eu-sv.txt' });
                    importForm.submitForm();
                  }}
                >
                  European Servers
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    importForm.setValues({ url: 'https://www.quakeservers.net/lists/global/global-sv.txt' });
                    importForm.submitForm();
                  }}
                >
                  Global Servers
                </Button>
              </Group>
            </Paper>
          </Tabs.Panel>
        </Tabs>
      </Box>
    </Container>
  );
}

export default AddServer;
