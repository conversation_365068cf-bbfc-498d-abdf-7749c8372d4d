import { useState } from 'react';
import {
  Container,
  Title,
  Paper,
  Text,
  Group,
  Button,
  Table,
  ActionIcon,
  TextInput,
  Box,
  LoadingOverlay,
  Progress
} from '@mantine/core';
import {
  IconRefresh,
  IconTrash,
  IconInfoCircle,
  IconSearch,
  IconArrowLeft
} from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';
import { invoke } from "@tauri-apps/api/core";

function Favorites({
  favorites,
  refreshServer,
  refreshFavorites,
  removeFavorite,
  loading,
  refreshProgress,
  totalServersToRefresh
}) {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [localLoading, setLocalLoading] = useState(false);

  // Filter favorites based on search query
  const filteredFavorites = favorites.filter(server => {
    const name = server.name || '';
    const address = server.address || '';
    const notes = server.notes || '';

    const searchLower = searchQuery.toLowerCase();
    return (
      name.toLowerCase().includes(searchLower) ||
      address.toLowerCase().includes(searchLower) ||
      notes.toLowerCase().includes(searchLower)
    );
  });

  // Function to query a favorite server
  const queryFavoriteServer = async (address, port) => {
    try {
      setLocalLoading(true);
      await invoke("query_server", { address, port });
      navigate(`/server/${address}/${port}`);
    } catch (error) {
      console.error("Failed to query favorite server:", error);
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <Container size="xl" py="md">
      <Box pos="relative">
        <LoadingOverlay visible={loading || localLoading} overlayBlur={2} />

        <Group mb="md">
          <Button leftSection={<IconArrowLeft size={16} />} onClick={() => navigate('/')}>
            Back to Server List
          </Button>
          <Button
            leftSection={<IconRefresh size={16} />}
            onClick={refreshFavorites}
            loading={loading && totalServersToRefresh > 0}
            disabled={loading}
          >
            Refresh All Favorites
          </Button>
        </Group>

        <Title order={2} mb="md">Favorite Servers</Title>

        {loading && totalServersToRefresh > 0 && (
          <Box mb="md">
            <Group position="apart" mb={5}>
              <Text size="sm">Refreshing servers: {refreshProgress}%</Text>
              <Text size="sm">{Math.floor(refreshProgress * totalServersToRefresh / 100)} / {totalServersToRefresh} servers</Text>
            </Group>
            <Progress value={refreshProgress} size="md" radius="sm" striped animated />
          </Box>
        )}

        <TextInput
          placeholder="Search favorites..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.currentTarget.value)}
          leftSection={<IconSearch size={16} />}
          mb="md"
        />

        {filteredFavorites.length === 0 ? (
          <Paper p="md" withBorder>
            <Text ta="center">
              {favorites.length === 0
                ? "No favorite servers added yet."
                : "No servers match your search criteria."}
            </Text>
          </Paper>
        ) : (
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Server Name</Table.Th>
                <Table.Th>Address</Table.Th>
                <Table.Th>Notes</Table.Th>
                <Table.Th>Added</Table.Th>
                <Table.Th style={{ textAlign: 'right' }}>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {filteredFavorites.map((server) => (
                <Table.Tr key={`${server.address}:${server.port}`}>
                  <Table.Td>
                    <Text fw={500}>{server.name || 'Unnamed Server'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{server.address}:{server.port}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{server.notes || '-'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">
                      {new Date(server.added_at * 1000).toLocaleDateString()}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs" justify="flex-end">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        onClick={() => queryFavoriteServer(server.address, server.port)}
                      >
                        <IconInfoCircle size={16} />
                      </ActionIcon>
                      <ActionIcon
                        variant="subtle"
                        color="green"
                        onClick={() => refreshServer(server.address, server.port)}
                      >
                        <IconRefresh size={16} />
                      </ActionIcon>
                      <ActionIcon
                        variant="subtle"
                        color="red"
                        onClick={() => removeFavorite(server.address, server.port)}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        )}
      </Box>
    </Container>
  );
}

export default Favorites;
