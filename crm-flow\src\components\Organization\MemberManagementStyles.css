/* Member Management Styles */
.member-management .card {
  border: 1px solid #dee2e6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.member-management .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.member-list {
  display: flex;
  flex-direction: column;
  margin-top: 5px;
}

.member-card {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 12px 15px;
  transition: all 0.2s ease;
  width: 100%;
  position: relative;
  border-bottom: 1px solid #eee;
}

.member-card:hover {
  background-color: #f8f9fa;
}

.member-avatar {
  margin-right: 15px;
  flex-shrink: 0;
}

.member-avatar img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 50%;
  border: 1px solid #dee2e6;
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1e3a8a;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
}

.member-info {
  flex: 1;
  min-width: 0;
  /* Ensures text truncation works */
}

.member-name {
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  color: #212529;
}

.member-email {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-role {
  font-size: 0.75rem;
}

.member-actions {
  margin-left: auto;
}

.member-actions button {
  padding: 4px 8px;
  font-size: 0.75rem;
}

/* Last member card has no border */
.member-card:last-child {
  border-bottom: none;
}

/* Style the badge counter */
.member-management .card-header .badge {
  background-color: #6c757d;
  font-weight: 500;
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
}

@media (max-width: 576px) {
  .member-card {
    flex-wrap: wrap;
  }

  .member-avatar {
    margin-right: 10px;
  }

  .member-info {
    flex: 1;
    min-width: 0;
  }

  .member-actions {
    width: 100%;
    margin-left: 0;
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
}