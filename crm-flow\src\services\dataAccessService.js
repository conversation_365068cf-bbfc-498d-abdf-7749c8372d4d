import {
  collection,
  query,
  where,
  getDocs,
  getDoc,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';
import { isOrganizationOwner, isOrganizationMember } from './organizationService';
import { cacheService } from './cacheService';

/**
 * Get data for a specific user or organization
 * @param {string} collectionName - The Firestore collection name
 * @param {string} userId - The current user ID
 * @param {string|null} organizationId - The organization ID (if applicable)
 * @param {Object} queryOptions - Additional query options
 * @param {boolean} bypassCache - Whether to bypass the cache
 * @returns {Promise<Array>} - Array of documents
 */
export const getUserData = async (collectionName, userId, organizationId = null, queryOptions = {}, bypassCache = false) => {
  try {
    // Create filters array for cache key generation
    const filters = [];

    if (organizationId) {
      filters.push({ field: 'organizationId', operator: '==', value: organizationId });
    } else {
      filters.push({ field: 'createdBy', operator: '==', value: userId });
    }

    // Add additional filters from queryOptions
    if (queryOptions.filters && Array.isArray(queryOptions.filters)) {
      queryOptions.filters.forEach(filter => {
        if (filter._field && filter._op && filter._value !== undefined) {
          filters.push({
            field: filter._field.toString(),
            operator: filter._op.toString(),
            value: filter._value
          });
        }
      });
    }

    // Check cache first if not bypassing
    if (!bypassCache) {
      const cachedData = cacheService.getQuery(collectionName, filters);
      if (cachedData) {
        return cachedData;
      }
    }

    // If we need to fetch from Firebase
    if (process.env.NODE_ENV !== 'production') {
      console.log(`Getting ${collectionName} data for user ${userId} with organization ${organizationId}`);
    }

    let q;

    if (organizationId) {
      // Check if user is a member of the organization
      const isMember = await isOrganizationMember(userId, organizationId);

      if (!isMember) {
        console.error(`User ${userId} is not a member of organization ${organizationId}`);
        throw new Error('User is not a member of this organization');
      }

      // Get data for the entire organization
      q = query(
        collection(db, collectionName),
        where('organizationId', '==', organizationId),
        ...(queryOptions.filters || [])
      );
    } else {
      // Get data only for this specific user
      q = query(
        collection(db, collectionName),
        where('createdBy', '==', userId),
        ...(queryOptions.filters || [])
      );
    }

    const snapshot = await getDocs(q);
    const results = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Cache the results
    cacheService.setQuery(collectionName, filters, results);

    if (process.env.NODE_ENV !== 'production') {
      console.log(`Retrieved ${results.length} ${collectionName} items`);
    }
    return results;
  } catch (error) {
    console.error(`Error getting ${collectionName} data:`, error);
    return [];
  }
};

/**
 * Add a new document with organization context
 * @param {string} collectionName - The Firestore collection name
 * @param {Object} data - The document data
 * @param {string} userId - The current user ID
 * @param {string|null} organizationId - The organization ID (if applicable)
 * @returns {Promise<Object>} - The result of the operation
 */
export const addUserDocument = async (collectionName, data, userId, organizationId = null) => {
  try {
    // Prepare document data with user and organization context
    const docData = {
      ...data,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      organizationId
    };

    // Add the document to Firestore
    const docRef = await addDoc(collection(db, collectionName), docData);

    const newDoc = {
      id: docRef.id,
      ...docData,
      createdAt: new Date(), // Convert server timestamp to Date for immediate use
      updatedAt: new Date()
    };

    // Invalidate collection cache
    cacheService.invalidateCollection(collectionName);

    // Cache the new document
    cacheService.setDocument(collectionName, docRef.id, newDoc);

    return {
      success: true,
      id: docRef.id,
      data: newDoc,
      message: `${collectionName} document created successfully`
    };
  } catch (error) {
    console.error(`Error adding ${collectionName} document:`, error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Update a document with permission checks
 * @param {string} collectionName - The Firestore collection name
 * @param {string} documentId - The document ID
 * @param {Object} data - The updated data
 * @param {string} userId - The current user ID
 * @param {string|null} organizationId - The organization ID (if applicable)
 * @returns {Promise<Object>} - The result of the operation
 */
export const updateUserDocument = async (collectionName, documentId, data, userId, organizationId = null) => {
  try {
    // Get the document reference (bypass cache to ensure fresh data)
    const docRef = doc(db, collectionName, documentId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      throw new Error(`${collectionName} document not found`);
    }

    const docData = docSnap.data();

    // Check permissions
    if (docData.createdBy !== userId) {
      // If not the creator, check if user is organization owner
      if (organizationId && docData.organizationId === organizationId) {
        const isOwner = await isOrganizationOwner(userId, organizationId);
        if (!isOwner) {
          throw new Error('You do not have permission to update this document');
        }
      } else {
        throw new Error('You do not have permission to update this document');
      }
    }

    // Update the document
    const updateData = {
      ...data,
      updatedAt: serverTimestamp(),
      updatedBy: userId
    };

    await updateDoc(docRef, updateData);

    // Invalidate caches
    cacheService.invalidateDocument(collectionName, documentId);
    cacheService.invalidateCollection(collectionName);

    return {
      success: true,
      message: `${collectionName} document updated successfully`
    };
  } catch (error) {
    console.error(`Error updating ${collectionName} document:`, error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Delete a document with permission checks
 * @param {string} collectionName - The Firestore collection name
 * @param {string} documentId - The document ID
 * @param {string} userId - The current user ID
 * @param {string|null} organizationId - The organization ID (if applicable)
 * @returns {Promise<Object>} - The result of the operation
 */
export const deleteUserDocument = async (collectionName, documentId, userId, organizationId = null) => {
  try {
    // Get the document reference (bypass cache to ensure fresh data)
    const docRef = doc(db, collectionName, documentId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      throw new Error(`${collectionName} document not found`);
    }

    const docData = docSnap.data();

    // Check permissions
    if (docData.createdBy !== userId) {
      // If not the creator, check if user is organization owner
      if (organizationId && docData.organizationId === organizationId) {
        const isOwner = await isOrganizationOwner(userId, organizationId);
        if (!isOwner) {
          throw new Error('You do not have permission to delete this document');
        }
      } else {
        throw new Error('You do not have permission to delete this document');
      }
    }

    // Delete the document
    await deleteDoc(docRef);

    // Invalidate caches
    cacheService.invalidateDocument(collectionName, documentId);
    cacheService.invalidateCollection(collectionName);

    return {
      success: true,
      message: `${collectionName} document deleted successfully`
    };
  } catch (error) {
    console.error(`Error deleting ${collectionName} document:`, error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get a single document with permission checks
 * @param {string} collectionName - The Firestore collection name
 * @param {string} documentId - The document ID
 * @param {string} userId - The current user ID
 * @param {string|null} organizationId - The organization ID (if applicable)
 * @param {boolean} bypassCache - Whether to bypass the cache
 * @returns {Promise<Object|null>} - The document data or null
 */
export const getUserDocument = async (collectionName, documentId, userId, organizationId = null, bypassCache = false) => {
  try {
    // Check cache first if not bypassing
    if (!bypassCache) {
      const cachedDoc = cacheService.getDocument(collectionName, documentId);
      if (cachedDoc) {
        return cachedDoc;
      }
    }

    // Get the document reference
    const docRef = doc(db, collectionName, documentId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return null;
    }

    const docData = docSnap.data();

    // Check permissions
    if (docData.createdBy !== userId) {
      // If not the creator, check if document belongs to user's organization
      if (organizationId && docData.organizationId === organizationId) {
        const isMember = await isOrganizationMember(userId, organizationId);
        if (!isMember) {
          throw new Error('You do not have permission to access this document');
        }
      } else {
        throw new Error('You do not have permission to access this document');
      }
    }

    const result = {
      id: docSnap.id,
      ...docData
    };

    // Cache the document
    cacheService.setDocument(collectionName, documentId, result);

    return result;
  } catch (error) {
    console.error(`Error getting ${collectionName} document:`, error);
    return null;
  }
};

/**
 * Archive a document by setting archived flag
 * @param {string} collectionName - The Firestore collection name
 * @param {string} documentId - The document ID
 * @param {string} userId - The current user ID
 * @param {string|null} organizationId - The organization ID (if applicable)
 * @returns {Promise<Object>} - The result of the operation
 */
export const archiveUserDocument = async (collectionName, documentId, userId, organizationId = null) => {
  try {
    // Update with archived flag
    const result = await updateUserDocument(collectionName, documentId, {
      archived: true,
      archivedAt: new Date(),
      archivedBy: userId
    }, userId, organizationId);

    // Invalidate collection cache since archived status affects filtering
    cacheService.invalidateCollection(collectionName);

    return result;
  } catch (error) {
    console.error(`Error archiving ${collectionName} document:`, error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Restore an archived document
 * @param {string} collectionName - The Firestore collection name
 * @param {string} documentId - The document ID
 * @param {string} userId - The current user ID
 * @param {string|null} organizationId - The organization ID (if applicable)
 * @returns {Promise<Object>} - The result of the operation
 */
export const restoreUserDocument = async (collectionName, documentId, userId, organizationId = null) => {
  try {
    // Update to remove archived flag
    const result = await updateUserDocument(collectionName, documentId, {
      archived: false,
      archivedAt: null,
      restoredAt: new Date(),
      restoredBy: userId
    }, userId, organizationId);

    // Invalidate collection cache since archived status affects filtering
    cacheService.invalidateCollection(collectionName);

    return result;
  } catch (error) {
    console.error(`Error restoring ${collectionName} document:`, error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Batch update multiple documents
 * @param {string} collectionName - The Firestore collection name
 * @param {Array<Object>} documents - Array of documents with id and data to update
 * @param {string} userId - The current user ID
 * @param {string|null} organizationId - The organization ID (if applicable)
 * @returns {Promise<Object>} - The result of the operation
 */
export const batchUpdateDocuments = async (collectionName, documents, userId, organizationId = null) => {
  try {
    if (!documents || !Array.isArray(documents) || documents.length === 0) {
      return {
        success: false,
        error: 'No documents provided for batch update'
      };
    }

    // Create a batch
    const batch = writeBatch(db);
    const updatedIds = [];

    // Add each document to the batch
    for (const document of documents) {
      if (!document.id) continue;

      // Check permissions first (bypass cache to ensure fresh data)
      const existingDoc = await getUserDocument(collectionName, document.id, userId, organizationId, true);
      if (!existingDoc) {
        console.error(`Document ${document.id} not found or you don't have permission to update it`);
        continue;
      }

      // Add metadata
      const updateData = {
        ...document.data,
        updatedAt: serverTimestamp(),
        updatedBy: userId
      };

      // Remove any undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      // Add to batch
      const docRef = doc(db, collectionName, document.id);
      batch.update(docRef, updateData);
      updatedIds.push(document.id);
    }

    if (updatedIds.length === 0) {
      return {
        success: false,
        error: 'No valid documents to update'
      };
    }

    // Commit the batch
    await batch.commit();

    // Invalidate collection cache
    cacheService.invalidateCollection(collectionName);

    // Invalidate individual document caches
    updatedIds.forEach(id => {
      cacheService.invalidateDocument(collectionName, id);
    });

    return {
      success: true,
      message: `Batch updated ${updatedIds.length} documents in ${collectionName}`,
      updatedIds
    };
  } catch (error) {
    console.error(`Error batch updating ${collectionName} documents:`, error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Clear all caches
 * @returns {void}
 */
export const clearAllCaches = () => {
  cacheService.clearAll();
};