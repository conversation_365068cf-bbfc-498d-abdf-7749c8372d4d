import { AppShell, Group, Button, Title, Container, Menu, ActionIcon, Box, Flex } from '@mantine/core';
import { IconRefresh, IconPlus, IconSettings, IconStar, IconWorld, IconChevronDown, IconX } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';

function AppHeader({ refreshAllServers, refreshMasterServers, loading, cancelRefresh }) {
  const navigate = useNavigate();

  return (
    <AppShell.Header p={0} style={{ height: '50px', zIndex: 1000 }}>
      <Box
        style={{
          height: '100%',
          backgroundColor: '#1a1b1e',
          borderBottom: '1px solid #2c2e33',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <Flex
          justify="space-between"
          align="center"
          style={{
            width: '100%',
            height: '100%',
            padding: '0 10px'
          }}
        >
          <Title
            order={4}
            onClick={() => navigate('/')}
            style={{
              cursor: 'pointer',
              fontSize: '16px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: '200px'
            }}
          >
            QuakeWorld Server Browser
          </Title>

          <Flex gap={8} align="center">
            {loading ? (
              <Button
                leftSection={<IconX size={14} />}
                color="red"
                size="xs"
                onClick={cancelRefresh}
                style={{ height: '26px', padding: '0 10px' }}
              >
                Cancel
              </Button>
            ) : (
              <Menu shadow="md" width={180} position="bottom-end" withinPortal>
                <Menu.Target>
                  <Button
                    leftSection={<IconRefresh size={14} />}
                    rightSection={<IconChevronDown size={14} />}
                    loading={loading}
                    variant="filled"
                    size="xs"
                    color="blue"
                    style={{ height: '26px', padding: '0 10px' }}
                  >
                    Refresh
                  </Button>
                </Menu.Target>

                <Menu.Dropdown>
                  <Menu.Item
                    leftSection={<IconRefresh size={14} />}
                    onClick={refreshAllServers}
                  >
                    Refresh Current Servers
                  </Menu.Item>
                  <Menu.Item
                    leftSection={<IconWorld size={14} />}
                    onClick={refreshMasterServers}
                  >
                    Refresh from Master Servers
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            )}

            <ActionIcon
              variant="light"
              color="blue"
              onClick={() => navigate('/add')}
              title="Add Server"
              size="sm"
            >
              <IconPlus size={14} />
            </ActionIcon>

            <ActionIcon
              variant="light"
              color="blue"
              onClick={() => navigate('/favorites')}
              title="Favorites"
              size="sm"
            >
              <IconStar size={14} />
            </ActionIcon>

            <ActionIcon
              variant="light"
              color="blue"
              onClick={() => navigate('/settings')}
              title="Settings"
              size="sm"
            >
              <IconSettings size={14} />
            </ActionIcon>
          </Flex>
        </Flex>
      </Box>
    </AppShell.Header>
  );
}

export default AppHeader;
