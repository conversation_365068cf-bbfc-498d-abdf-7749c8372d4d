import { useState, useEffect } from 'react';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';
import { parseInputText, isJSON } from '../../utils/csvParser';
import 'bootstrap/dist/css/bootstrap.min.css';
import './ImporterStyles.css';

const OpportunityImporter = ({ onImportComplete }) => {
  const { currentUser, organization } = useAuth();
  const [inputText, setInputText] = useState('');
  const [parsedData, setParsedData] = useState([]);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importResults, setImportResults] = useState({ success: 0, failed: 0, total: 0 });
  const [fieldMappings, setFieldMappings] = useState({
    name: 'name',
    contact_info: 'phone',
    location: 'address',
    issue: 'description'
  });

  // Detect if input is JSON format
  const [isJsonFormat, setIsJsonFormat] = useState(false);
  const [errors, setErrors] = useState([]);

  // Parse the input text when it changes
  useEffect(() => {
    if (inputText.trim()) {
      try {
        // Check if input is JSON format
        const isJsonInput = isJSON(inputText);
        setIsJsonFormat(isJsonInput);

        // Parse the input
        const data = parseInputText(inputText);
        setParsedData(data);

        // If it's JSON, update field mappings to match JSON property names
        if (isJsonInput && data.length > 0) {
          const firstItem = data[0];
          const newMappings = {
            name: Object.keys(firstItem).find(key => key === 'name') || 'name',
            contact_info: Object.keys(firstItem).find(key => key === 'contact_info') || 'contact_info',
            location: Object.keys(firstItem).find(key => key === 'location') || 'location',
            issue: Object.keys(firstItem).find(key => key === 'issue') || 'issue'
          };
          setFieldMappings(newMappings);
        }

        setErrors([]);
      } catch (error) {
        console.error('Error parsing input:', error);
        setErrors(['Failed to parse input. Please check the format.']);
        setParsedData([]);
      }
    } else {
      setParsedData([]);
      setErrors([]);
    }
  }, [inputText]);

  // Handle text input change
  const handleInputChange = (e) => {
    setInputText(e.target.value);
    setIsPreviewMode(false);
  };

  // Handle field mapping change
  const handleMappingChange = (sourceField, targetField) => {
    setFieldMappings({
      ...fieldMappings,
      [sourceField]: targetField
    });
  };

  // Preview the parsed data
  const handlePreview = () => {
    if (parsedData.length > 0) {
      setIsPreviewMode(true);
    } else {
      setErrors(['No data to preview. Please enter valid data.']);
    }
  };

  // Import the opportunities
  const handleImport = async () => {
    if (parsedData.length === 0) {
      setErrors(['No data to import. Please enter valid data.']);
      return;
    }

    if (!currentUser || !organization) {
      setErrors(['You must be logged in and have an active organization to import data.']);
      return;
    }

    setIsImporting(true);
    setImportResults({ success: 0, failed: 0, total: parsedData.length });
    const results = { success: 0, failed: 0, total: parsedData.length };
    const importErrors = [];

    for (const item of parsedData) {
      try {
        // Map the fields according to the field mappings
        const opportunityData = {
          name: item[fieldMappings.name] || 'New Opportunity',
          value: 0, // Default value
          currency: 'USD', // Default currency
          status: 'New',
          closeDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
          description: item[fieldMappings.issue] || '',
          phone: item[fieldMappings.contact_info] || '',
          email: item[fieldMappings.contact_info]?.includes('@') ? item[fieldMappings.contact_info] : '',
          address: item[fieldMappings.location] || '',
          createdBy: currentUser.uid,
          createdAt: serverTimestamp(),
          organizationId: organization.id,
          archived: false
        };

        // Add the opportunity to Firestore
        await addDoc(collection(db, 'opportunities'), opportunityData);
        results.success++;
      } catch (error) {
        console.error('Error importing opportunity:', error);
        importErrors.push(`Failed to import ${item[fieldMappings.name] || 'unnamed opportunity'}: ${error.message}`);
        results.failed++;
      }
    }

    setImportResults(results);
    setErrors(importErrors);
    setIsImporting(false);

    if (onImportComplete) {
      onImportComplete(results);
    }
  };

  // Reset the importer
  const handleReset = () => {
    setInputText('');
    setParsedData([]);
    setIsPreviewMode(false);
    setImportResults({ success: 0, failed: 0, total: 0 });
    setErrors([]);
  };

  return (
    <div className="opportunity-importer">
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Import Opportunities</h5>
        </div>
        <div className="card-body">
          {!isPreviewMode ? (
            <>
              <div className="mb-3">
                <label htmlFor="importText" className="form-label">Paste your data (JSON, CSV, table format, or markdown table):</label>
                <textarea
                  id="importText"
                  className="form-control"
                  rows="10"
                  value={inputText}
                  onChange={handleInputChange}
                  placeholder="Paste your data here..."
                ></textarea>
                {inputText.trim() && parsedData.length > 0 && (
                  <div className="mt-2">
                    <span className="badge bg-info">
                      {isJsonFormat ? 'JSON Format Detected' : 'Table/CSV Format Detected'}
                    </span>
                    <span className="ms-2 text-muted small">
                      {parsedData.length} records found
                    </span>
                  </div>
                )}
              </div>
              <div className="d-flex justify-content-between">
                <button
                  className="btn btn-primary"
                  onClick={handlePreview}
                  disabled={parsedData.length === 0}
                >
                  Preview Data
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={handleReset}
                  disabled={!inputText}
                >
                  Reset
                </button>
              </div>
            </>
          ) : (
            <>
              <h6 className="mb-3">Preview ({parsedData.length} records)</h6>

              <div className="mb-4">
                <h6>Field Mappings</h6>
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label">Name Field</label>
                    <select
                      className="form-select"
                      value={fieldMappings.name}
                      onChange={(e) => handleMappingChange('name', e.target.value)}
                    >
                      {Object.keys(parsedData[0] || {}).map(field => (
                        <option key={field} value={field}>{field}</option>
                      ))}
                    </select>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Contact Info Field</label>
                    <select
                      className="form-select"
                      value={fieldMappings.contact_info}
                      onChange={(e) => handleMappingChange('contact_info', e.target.value)}
                    >
                      {Object.keys(parsedData[0] || {}).map(field => (
                        <option key={field} value={field}>{field}</option>
                      ))}
                    </select>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Location Field</label>
                    <select
                      className="form-select"
                      value={fieldMappings.location}
                      onChange={(e) => handleMappingChange('location', e.target.value)}
                    >
                      {Object.keys(parsedData[0] || {}).map(field => (
                        <option key={field} value={field}>{field}</option>
                      ))}
                    </select>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Issue/Description Field</label>
                    <select
                      className="form-select"
                      value={fieldMappings.issue}
                      onChange={(e) => handleMappingChange('issue', e.target.value)}
                    >
                      {Object.keys(parsedData[0] || {}).map(field => (
                        <option key={field} value={field}>{field}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              <div className="table-responsive mb-3">
                <table className="table table-sm table-bordered">
                  <thead>
                    <tr>
                      <th>#</th>
                      <th>Name</th>
                      <th>Contact</th>
                      <th>Location</th>
                      <th>Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {parsedData.slice(0, 10).map((item, index) => (
                      <tr key={index}>
                        <td>{index + 1}</td>
                        <td>{item[fieldMappings.name]}</td>
                        <td>{item[fieldMappings.contact_info]}</td>
                        <td>{item[fieldMappings.location]}</td>
                        <td>{item[fieldMappings.issue]}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {parsedData.length > 10 && (
                  <div className="text-center text-muted small">
                    Showing 10 of {parsedData.length} records
                  </div>
                )}
              </div>

              <div className="d-flex justify-content-between">
                <div>
                  <button
                    className="btn btn-success me-2"
                    onClick={handleImport}
                    disabled={isImporting}
                  >
                    {isImporting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Importing...
                      </>
                    ) : (
                      `Import ${parsedData.length} Opportunities`
                    )}
                  </button>
                  <button
                    className="btn btn-outline-secondary"
                    onClick={() => setIsPreviewMode(false)}
                    disabled={isImporting}
                  >
                    Back to Edit
                  </button>
                </div>
                <button
                  className="btn btn-secondary"
                  onClick={handleReset}
                  disabled={isImporting}
                >
                  Reset
                </button>
              </div>

              {importResults.total > 0 && (
                <div className="alert alert-info mt-3">
                  <h6>Import Results</h6>
                  <p className="mb-0">
                    Successfully imported {importResults.success} of {importResults.total} opportunities.
                    {importResults.failed > 0 && ` Failed to import ${importResults.failed} opportunities.`}
                  </p>
                </div>
              )}
            </>
          )}

          {errors.length > 0 && (
            <div className="alert alert-danger mt-3">
              <h6>Errors</h6>
              <ul className="mb-0">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OpportunityImporter;
