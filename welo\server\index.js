import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import db, { serverDb } from './database.js';
import {
  queryServer,
  pingServer,
  queryAllServers,
  updateServerInDatabase,
  validateServerAddress
} from './quakeworld-api.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files from the React app build
app.use(express.static(path.join(__dirname, '../dist')));

// API Routes

// Get all servers with latest status
app.get('/api/servers', (req, res) => {
  try {
    const servers = serverDb.getLatestServerStatuses.all();
    res.json(servers);
  } catch (error) {
    console.error('Error fetching servers:', error);
    res.status(500).json({ error: 'Failed to fetch servers' });
  }
});

// Add a new server
app.post('/api/servers', async (req, res) => {
  const { name, address, port } = req.body;

  if (!name || !address || !port) {
    return res.status(400).json({ error: 'Name, address, and port are required' });
  }

  // Validate address and port
  const validation = validateServerAddress(address, port);
  if (!validation.valid) {
    return res.status(400).json({ error: validation.error });
  }

  try {
    const result = serverDb.addServer.run(name, address, parseInt(port));
    const newServer = serverDb.getServerById.get(result.lastInsertRowid);

    // Query the server immediately to get initial status
    const queryResult = await queryServer(address, parseInt(port));
    await updateServerInDatabase(newServer.id, queryResult);

    res.status(201).json({
      message: 'Server added successfully',
      server: newServer,
      initialStatus: queryResult
    });
  } catch (error) {
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      res.status(409).json({ error: 'Server already exists' });
    } else {
      console.error('Error adding server:', error);
      res.status(500).json({ error: 'Failed to add server' });
    }
  }
});

// Update a server
app.patch('/api/servers/:id', (req, res) => {
  const { id } = req.params;
  const { name } = req.body;

  if (!name) {
    return res.status(400).json({ error: 'Name is required' });
  }

  try {
    const updateServer = db.prepare(`
      UPDATE servers SET name = ? WHERE id = ?
    `);
    const result = updateServer.run(name, id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Server not found' });
    }

    const updatedServer = serverDb.getServerById.get(id);
    res.json({ message: 'Server updated successfully', server: updatedServer });
  } catch (error) {
    console.error('Error updating server:', error);
    res.status(500).json({ error: 'Failed to update server' });
  }
});

// Delete a server
app.delete('/api/servers/:id', (req, res) => {
  const { id } = req.params;

  try {
    const result = serverDb.deleteServer.run(id);
    if (result.changes === 0) {
      return res.status(404).json({ error: 'Server not found' });
    }
    res.json({ message: 'Server deleted successfully' });
  } catch (error) {
    console.error('Error deleting server:', error);
    res.status(500).json({ error: 'Failed to delete server' });
  }
});

// Query a specific server
app.post('/api/servers/:id/query', async (req, res) => {
  const { id } = req.params;

  try {
    const server = serverDb.getServerById.get(id);
    if (!server) {
      return res.status(404).json({ error: 'Server not found' });
    }

    const result = await queryServer(server.address, server.port);
    await updateServerInDatabase(server.id, result);

    res.json(result);
  } catch (error) {
    console.error('Error querying server:', error);
    res.status(500).json({ error: 'Failed to query server' });
  }
});

// Query all servers
app.post('/api/servers/query-all', async (req, res) => {
  try {
    const results = await queryAllServers();
    res.json(results);
  } catch (error) {
    console.error('Error querying all servers:', error);
    res.status(500).json({ error: 'Failed to query servers' });
  }
});

// Get server history
app.get('/api/servers/:id/history', (req, res) => {
  const { id } = req.params;
  const limit = parseInt(req.query.limit) || 50;

  try {
    const history = serverDb.getServerHistory.all(id, limit);
    res.json(history);
  } catch (error) {
    console.error('Error fetching server history:', error);
    res.status(500).json({ error: 'Failed to fetch server history' });
  }
});

// Test server connection without saving
app.post('/api/test-server', async (req, res) => {
  const { address, port } = req.body;

  if (!address || !port) {
    return res.status(400).json({ error: 'Address and port are required' });
  }

  const validation = validateServerAddress(address, port);
  if (!validation.valid) {
    return res.status(400).json({ error: validation.error });
  }

  try {
    const result = await queryServer(address, parseInt(port));
    res.json(result);
  } catch (error) {
    console.error('Error testing server:', error);
    res.status(500).json({ error: 'Failed to test server' });
  }
});

// Catch all handler: send back React's index.html file for client-side routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`QuakeWorld Server Browser API running on port ${PORT}`);
  console.log(`Frontend will be served from http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  process.exit(0);
});
