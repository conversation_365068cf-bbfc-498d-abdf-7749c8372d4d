import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Sidebar from '../Navigation/Sidebar';
import './MainLayout.css';

// Get the app's background color from CSS variables or set a default
const getAppBackgroundColor = () => {
  // You can replace this with your app's actual background color
  return '#f8f9fa';
};

// Helper function to check if device is mobile
const isMobileDevice = () => window.innerWidth <= 768;

const MainLayout = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const location = useLocation();

  // Listen for sidebar collapse state changes
  useEffect(() => {
    const handleSidebarToggle = (e) => {
      if (e.detail) {
        setSidebarCollapsed(e.detail.collapsed);
      }
    };

    window.addEventListener('sidebar-toggle', handleSidebarToggle);

    return () => {
      window.removeEventListener('sidebar-toggle', handleSidebarToggle);
    };
  }, []);

  // State to track if device is mobile
  const [isMobile, setIsMobile] = useState(isMobileDevice());

  // Update isMobile state on window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(isMobileDevice());
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Add a class to handle completely hidden sidebar on mobile
  const sidebarClass = isMobile && sidebarCollapsed ? 'sidebar-hidden' : '';

  // Check if we're on the flow page
  const isFlowPage = location.pathname === '/flow';

  return (
    <div className={`layout-container ${isFlowPage ? 'flow-view' : ''}`} style={{ backgroundColor: getAppBackgroundColor() }}>
      <Sidebar collapsed={sidebarCollapsed} setCollapsed={setSidebarCollapsed} />
      <div
        className={`main-content ${sidebarCollapsed ? 'expanded' : ''} ${sidebarClass}`}
        style={{ backgroundColor: getAppBackgroundColor() }}>
        {isMobile && sidebarCollapsed && (
          <button
            className="mobile-menu-toggle"
            onClick={() => setSidebarCollapsed(false)}
            aria-label="Open menu"
          >
            <i className="bi bi-list"></i>
          </button>
        )}
        {children}
      </div>
    </div>
  );
};

export default MainLayout;
