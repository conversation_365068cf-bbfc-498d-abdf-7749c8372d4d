import { useState, useEffect } from 'react';
import { collection, addDoc, doc, updateDoc } from 'firebase/firestore';
import { addUserDocument } from '../../services/dataAccessService';
import { db } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';
import 'bootstrap/dist/css/bootstrap.min.css';

const CompanyForm = ({ onClose, onSuccess, companyData = null, isEditing = false }) => {
  const { currentUser, organization } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    industry: '',
    size: 'Small',
    website: '',
    address: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Initialize form with company data if editing
  useEffect(() => {
    if (isEditing && companyData) {
      setFormData({
        id: companyData.id,
        name: companyData.name || '',
        industry: companyData.industry || '',
        size: companyData.size || 'Small',
        website: companyData.website || '',
        address: companyData.address || '',
        description: companyData.description || ''
      });
    }
  }, [isEditing, companyData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validate form
      if (!formData.name || !formData.industry) {
        throw new Error('Please fill in all required fields');
      }

      // Validate website format if provided
      if (formData.website && !formData.website.match(/^(https?:\/\/)?(www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\/.*)?$/)) {
        throw new Error('Please enter a valid website URL');
      }

      // Prepare company data
      const companyData = {
        ...formData,
        updatedAt: new Date()
      };

      // Add organization ID if available
      if (organization?.id) {
        companyData.organizationId = organization.id;
      }

      console.log('Company data with organization context:', companyData);

      let docId;

      if (isEditing && companyData.id) {
        // Update existing company
        const companyId = companyData.id;
        // Remove id from the data object before updating
        const { id, ...dataToUpdate } = companyData;
        const companyRef = doc(db, 'companies', companyId);
        await updateDoc(companyRef, dataToUpdate);
        docId = companyId;
      } else {
        // Create new company using the dataAccessService
        console.log('Creating new company with data:', companyData);

        // Use the addUserDocument function to add the company with proper context
        const result = await addUserDocument('companies', companyData, currentUser.uid, organization?.id);

        if (result.success) {
          docId = result.id;
          console.log('Company created with ID:', docId);
        } else {
          throw new Error(result.error || 'Failed to create company');
        }
      }

      // Show success message
      setSuccess(true);

      if (!isEditing) {
        // Only reset form for new companies
        setFormData({
          name: '',
          industry: '',
          size: 'Small',
          website: '',
          address: '',
          description: ''
        });
      }

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(docId, companyData);
      }

      // Close modal after 1.5 seconds if onClose is provided
      if (onClose) {
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('Error adding company:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="card shadow border-0">
      <div className="card-header bg-info text-white">
        <h5 className="mb-0">{isEditing ? 'Edit Company' : 'Add New Company'}</h5>
      </div>
      <div className="card-body">
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success" role="alert">
            Company {isEditing ? 'updated' : 'created'} successfully!
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label htmlFor="name" className="form-label">Company Name *</label>
            <input
              type="text"
              className="form-control"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="industry" className="form-label">Industry *</label>
              <select
                className="form-select"
                id="industry"
                name="industry"
                value={formData.industry}
                onChange={handleChange}
                required
              >
                <option value="">Select an industry</option>
                <option value="Technology">Technology</option>
                <option value="Financial Services">Financial Services</option>
                <option value="Healthcare">Healthcare</option>
                <option value="Manufacturing">Manufacturing</option>
                <option value="Retail">Retail</option>
                <option value="Education">Education</option>
                <option value="Professional Services">Professional Services</option>
                <option value="Media & Entertainment">Media & Entertainment</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <div className="col-md-6">
              <label htmlFor="size" className="form-label">Company Size</label>
              <select
                className="form-select"
                id="size"
                name="size"
                value={formData.size}
                onChange={handleChange}
              >
                <option value="Small">Small (1-50 employees)</option>
                <option value="Mid-Market">Mid-Market (51-500 employees)</option>
                <option value="Enterprise">Enterprise (500+ employees)</option>
              </select>
            </div>
          </div>

          <div className="mb-3">
            <label htmlFor="website" className="form-label">Website</label>
            <input
              type="text"
              className="form-control"
              id="website"
              name="website"
              placeholder="https://example.com"
              value={formData.website}
              onChange={handleChange}
            />
          </div>

          <div className="mb-3">
            <label htmlFor="address" className="form-label">Address</label>
            <input
              type="text"
              className="form-control"
              id="address"
              name="address"
              value={formData.address}
              onChange={handleChange}
            />
          </div>

          <div className="mb-3">
            <label htmlFor="description" className="form-label">Description</label>
            <textarea
              className="form-control"
              id="description"
              name="description"
              rows="3"
              value={formData.description}
              onChange={handleChange}
            ></textarea>
          </div>

          <div className="d-flex justify-content-end gap-2 mt-4">
            {onClose && (
              <button
                type="button"
                className="btn btn-outline-secondary"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              className="btn btn-info"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Saving...
                </>
              ) : isEditing ? 'Update Company' : 'Save Company'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CompanyForm;
