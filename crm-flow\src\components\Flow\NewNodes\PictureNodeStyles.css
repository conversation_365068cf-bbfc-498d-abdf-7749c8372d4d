.node-picture {
  width: 280px;
  max-width: 280px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.node-picture.node-archived {
  opacity: 0.7;
  background-color: #f8f9fa;
}

.picture-node-body {
  max-height: 300px;
  overflow-y: auto;
  position: relative;
  background-color: white;
  padding: 10px;
}

.drop-zone {
  border: 2px dashed #ccc;
  border-radius: 5px;
  padding: 10px;
  transition: all 0.3s ease;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #f8f9fa;
}

.node-picture.dragging .drop-zone {
  border-color: #f472b6;
  background-color: rgba(244, 114, 182, 0.1);
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 5px;
}

.file-list {
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border-radius: 5px;
  margin-bottom: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
}

.file-item:hover {
  background-color: rgba(244, 114, 182, 0.05);
}

.file-icon {
  margin-right: 10px;
  font-size: 1.2rem;
  color: #f472b6;
  width: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-info {
  flex: 1;
  min-width: 0;
  cursor: pointer;
}

.file-name {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.file-meta {
  display: flex;
  font-size: 0.75rem;
  color: #6c757d;
}

.file-size {
  margin-right: 10px;
}

.file-delete {
  padding: 2px 5px;
  font-size: 0.8rem;
  line-height: 1;
  border-radius: 3px;
  border-color: #f472b6;
  color: #f472b6;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.file-delete:hover {
  opacity: 1;
  background-color: rgba(244, 114, 182, 0.1);
}

.file-delete i {
  font-size: 0.8rem;
}

/* Node header styling */
.node-picture .node-header {
  background-color: #f472b6;
  color: white;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.node-picture .node-title {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.node-picture .node-badge {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
}

/* Add button styling */
.node-picture .btn-outline-primary {
  color: #f472b6;
  border-color: #f472b6;
}

.node-picture .btn-outline-primary:hover {
  background-color: #f472b6;
  color: white;
}

/* Scrollbar styling */
.picture-node-body::-webkit-scrollbar {
  width: 6px;
}

.picture-node-body::-webkit-scrollbar-track {
  background: #f8f9fa;
}

.picture-node-body::-webkit-scrollbar-thumb {
  background: #f472b6;
  opacity: 0.7;
  border-radius: 3px;
}

.picture-node-body::-webkit-scrollbar-thumb:hover {
  background: #f472b6;
  opacity: 1;
}