import React, { useEffect, useState } from 'react';
import './Notification.css';

const Notification = ({ message, duration = 5000, type = 'error' }) => {
  const [visible, setVisible] = useState(true);
  
  useEffect(() => {
    // Set a timeout to hide the notification after the specified duration
    const timer = setTimeout(() => {
      setVisible(false);
    }, duration);
    
    // Clean up the timer when the component unmounts
    return () => clearTimeout(timer);
  }, [duration]);
  
  if (!visible) return null;
  
  return (
    <div className={`notification notification-${type} ${visible ? 'show' : 'hide'}`}>
      <div className="notification-content">
        {type === 'error' && <i className="bi bi-exclamation-triangle-fill me-2"></i>}
        {type === 'warning' && <i className="bi bi-exclamation-circle-fill me-2"></i>}
        {type === 'info' && <i className="bi bi-info-circle-fill me-2"></i>}
        {type === 'success' && <i className="bi bi-check-circle-fill me-2"></i>}
        <span>{message}</span>
      </div>
    </div>
  );
};

export default Notification;
