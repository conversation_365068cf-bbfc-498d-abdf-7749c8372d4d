/* CRM Application Styles */
.App {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Opportunity Node Styles */
.opportunity-node {
  transition: all 0.2s ease;
}

.opportunity-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.opportunity-node.selected {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

/* React Flow Overrides */
.react-flow__node-opportunity {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

.react-flow__handle {
  width: 8px !important;
  height: 8px !important;
  border: 2px solid white !important;
}

.react-flow__controls {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.react-flow__controls-button {
  background: white !important;
  border: none !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.react-flow__controls-button:hover {
  background: #f9fafb !important;
}

.react-flow__minimap {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
}

/* Form Styles */
input, select, textarea {
  font-family: inherit;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

button {
  font-family: inherit;
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
