body {
  margin: 0;
  padding: 0;
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 400;
  background-color: #1a1b1e;
  color: #c1c2c5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: #339af0;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

.server-row {
  cursor: pointer;
  transition: background-color 0.2s;
  height: 40px;
}

.server-row:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Compact styles */
.mantine-AppShell-main {
  padding: 0 !important;
  margin-top: 50px !important;
}

.mantine-Container-root {
  padding: 10px !important;
}

.mantine-Paper-root {
  padding: 10px !important;
}

.mantine-Group-root {
  gap: 8px !important;
}

.mantine-Button-root {
  height: 32px !important;
  font-size: 13px !important;
  padding: 0 12px !important;
}

.mantine-TextInput-root,
.mantine-NumberInput-root,
.mantine-Select-root {
  margin-bottom: 8px !important;
}

.mantine-Table-root th,
.mantine-Table-root td {
  padding: 6px 10px !important;
  font-size: 13px !important;
}

.mantine-Title-root {
  margin-bottom: 10px !important;
  font-size: 18px !important;
}

.mantine-Title-root.mantine-Title-order3 {
  font-size: 16px !important;
}

.mantine-Text-root {
  font-size: 13px !important;
}

.mantine-Badge-root {
  height: 18px !important;
  font-size: 11px !important;
}

/* Fix for header positioning */
.mantine-AppShell-header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
}

/* Fix for dropdown menus */
.mantine-Menu-dropdown {
  z-index: 1001 !important;
}

/* Ensure content doesn't overlap with header */
.mantine-AppShell-main {
  position: relative !important;
  z-index: 1 !important;
}