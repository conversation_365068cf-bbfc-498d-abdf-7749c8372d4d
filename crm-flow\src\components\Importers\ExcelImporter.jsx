import { useState, useEffect } from 'react';
import { collection, addDoc, serverTimestamp, query, where, getDocs, doc, setDoc, updateDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';
import { parseExcelFile, getExcelSheets, getExcelPreview } from '../../utils/excelParser';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import './ImporterStyles.css';

const ExcelImporter = () => {
  const { t } = useTranslation();
  const { currentUser, organization } = useAuth();
  const navigate = useNavigate();
  const [file, setFile] = useState(null);
  const [sheets, setSheets] = useState([]);
  const [selectedSheet, setSelectedSheet] = useState(0);
  const [headerRow, setHeaderRow] = useState(1);
  const [previewData, setPreviewData] = useState([]);
  const [fieldMappings, setFieldMappings] = useState({});
  const [availableFields, setAvailableFields] = useState([]);
  const [targetFields] = useState([
    { id: 'name', label: 'Name', required: true },
    { id: 'value', label: 'Value', type: 'number' },
    { id: 'currency', label: 'Currency' },
    { id: 'status', label: 'Status' },
    { id: 'company', label: 'Company' },
    { id: 'contact', label: 'Contact' },
    { id: 'phone', label: 'Phone' },
    { id: 'email', label: 'Email' },
    { id: 'address', label: 'Address' },
    { id: 'description', label: 'Description' },
    { id: 'closeDate', label: 'Close Date', type: 'date' }
  ]);
  const [importing, setImporting] = useState(false);
  const [importResults, setImportResults] = useState(null);
  const [errors, setErrors] = useState([]);
  const [createRelationships, setCreateRelationships] = useState(true);
  const [arrangeInFlowView, setArrangeInFlowView] = useState(true);
  const [relationshipResults, setRelationshipResults] = useState({
    contacts: { created: 0, linked: 0, failed: 0 },
    companies: { created: 0, linked: 0, failed: 0 },
    edges: { created: 0, failed: 0 }
  });
  const [createdNodeIds, setCreatedNodeIds] = useState({
    opportunities: [],
    contacts: [],
    companies: []
  });

  // Handle file selection
  const handleFileChange = async (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;

    try {
      setFile(selectedFile);

      // Get sheets from the Excel file
      const sheetList = await getExcelSheets(selectedFile);
      setSheets(sheetList);
      setSelectedSheet(0);

      // Get preview data
      await loadPreviewData(selectedFile, 0, headerRow);
    } catch (error) {
      console.error('Error processing Excel file:', error);
      setErrors([`Error processing Excel file: ${error.message}`]);
    }
  };

  // Load preview data when sheet or header row changes
  const loadPreviewData = async (fileObj, sheetIdx, headerRowNum) => {
    try {
      const preview = await getExcelPreview(fileObj || file, {
        sheetIndex: sheetIdx !== undefined ? sheetIdx : selectedSheet,
        headerRow: headerRowNum !== undefined ? headerRowNum : headerRow,
        maxRows: 5
      });

      setPreviewData(preview);

      // Extract available fields from the first row
      if (preview.length > 0) {
        const fields = Object.keys(preview[0]).map(field => ({
          id: field,
          label: field
        }));
        setAvailableFields(fields);

        // Set default mappings
        const defaultMappings = {};
        fields.forEach(field => {
          // Try to find matching target fields
          const normalizedField = field.id.toLowerCase();
          const matchingTarget = targetFields.find(target =>
            normalizedField.includes(target.id.toLowerCase()) ||
            target.id.toLowerCase().includes(normalizedField)
          );

          if (matchingTarget) {
            defaultMappings[field.id] = matchingTarget.id;
          }
        });

        setFieldMappings(defaultMappings);
      }
    } catch (error) {
      console.error('Error loading preview data:', error);
      setErrors([`Error loading preview data: ${error.message}`]);
    }
  };

  // Handle sheet selection
  const handleSheetChange = (e) => {
    const sheetIndex = parseInt(e.target.value);
    setSelectedSheet(sheetIndex);
    loadPreviewData(file, sheetIndex, headerRow);
  };

  // Handle header row change
  const handleHeaderRowChange = (e) => {
    const row = parseInt(e.target.value);
    setHeaderRow(row);
    loadPreviewData(file, selectedSheet, row);
  };

  // Handle field mapping change
  const handleMappingChange = (sourceField, targetField) => {
    setFieldMappings(prev => ({
      ...prev,
      [sourceField]: targetField
    }));
  };

  // Import data
  const handleImport = async () => {
    if (!organization?.id) {
      setErrors(['Organization not found. Please try again.']);
      return;
    }

    // Validate required fields
    const requiredTargetFields = targetFields.filter(f => f.required).map(f => f.id);
    const mappedRequiredFields = requiredTargetFields.filter(field =>
      Object.values(fieldMappings).includes(field)
    );

    if (mappedRequiredFields.length < requiredTargetFields.length) {
      const missingFields = requiredTargetFields.filter(field =>
        !mappedRequiredFields.includes(field)
      );
      setErrors([`Missing required field mappings: ${missingFields.join(', ')}`]);
      return;
    }

    setImporting(true);
    setErrors([]);

    try {
      // Parse the full Excel file
      const data = await parseExcelFile(file, {
        sheetIndex: selectedSheet,
        headerRow
      });

      // Create a reverse mapping (target -> source)
      const reverseMapping = {};
      Object.keys(fieldMappings).forEach(source => {
        if (fieldMappings[source]) {
          reverseMapping[fieldMappings[source]] = source;
        }
      });

      // Transform data based on mappings
      const transformedData = data.map(item => {
        const transformed = {};

        // Map fields according to the mapping
        targetFields.forEach(targetField => {
          const sourceField = reverseMapping[targetField.id];
          if (sourceField && item[sourceField] !== undefined) {
            // Convert types if needed
            if (targetField.type === 'number') {
              transformed[targetField.id] = parseFloat(item[sourceField]) || 0;
            } else if (targetField.type === 'date') {
              // Handle Excel date format or string date
              if (item[sourceField] instanceof Date) {
                transformed[targetField.id] = item[sourceField];
              } else if (typeof item[sourceField] === 'number') {
                // Excel stores dates as days since 1/1/1900
                const excelEpoch = new Date(1899, 11, 30);
                const date = new Date(excelEpoch.getTime() + (item[sourceField] * 24 * 60 * 60 * 1000));
                transformed[targetField.id] = date;
              } else {
                transformed[targetField.id] = new Date(item[sourceField]);
              }
            } else {
              transformed[targetField.id] = item[sourceField];
            }
          }
        });

        return transformed;
      });

      // Filter out items without required fields
      const validItems = transformedData.filter(item =>
        requiredTargetFields.every(field => item[field] !== undefined && item[field] !== '')
      );

      // Import to Firestore
      const opportunitiesCollection = collection(db, 'opportunities');
      const importedItems = [];
      const failedItems = [];

      // Initialize relationship results
      const relationships = {
        contacts: { created: 0, linked: 0, failed: 0 },
        companies: { created: 0, linked: 0, failed: 0 },
        edges: { created: 0, failed: 0 }
      };

      // Track created node IDs for arrangement
      const createdNodes = {
        opportunities: [],
        contacts: [],
        companies: []
      };

      for (const item of validItems) {
        try {
          // Add default fields
          const opportunity = {
            ...item,
            organizationId: organization.id,
            createdBy: currentUser.uid,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            archived: false,
            // Set defaults for required fields if not present
            status: item.status || 'New',
            value: item.value !== undefined ? item.value : 0,
            currency: item.currency || 'USD',
            closeDate: item.closeDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
          };

          const docRef = await addDoc(opportunitiesCollection, opportunity);
          const opportunityId = docRef.id;
          const importedItem = { id: opportunityId, ...opportunity };
          importedItems.push(importedItem);

          // Track opportunity ID for arrangement
          createdNodes.opportunities.push({
            id: opportunityId,
            type: 'opportunity',
            name: opportunity.name
          });

          // Create relationships if enabled
          if (createRelationships) {
            // Handle contact relationship
            if (item.contact) {
              try {
                const contactResult = await createOrLinkContact(item.contact, organization.id);
                if (contactResult) {
                  const { contactId, isNew } = contactResult;

                  // Create edge between opportunity and contact
                  await createEdge(opportunityId, contactId, 'opportunity', 'contact', organization.id);
                  relationships.edges.created++;

                  // Track contact ID for arrangement if it's new
                  if (isNew) {
                    createdNodes.contacts.push({
                      id: contactId,
                      type: 'contact',
                      name: item.contact,
                      relatedTo: opportunityId
                    });
                  }
                }
              } catch (error) {
                console.error('Error creating contact relationship:', error);
                relationships.edges.failed++;
              }
            }

            // Handle company relationship
            if (item.company) {
              try {
                const companyResult = await createOrLinkCompany(item.company, organization.id);
                if (companyResult) {
                  const { companyId, isNew } = companyResult;

                  // Create edge between opportunity and company
                  await createEdge(opportunityId, companyId, 'opportunity', 'company', organization.id);
                  relationships.edges.created++;

                  // Track company ID for arrangement if it's new
                  if (isNew) {
                    createdNodes.companies.push({
                      id: companyId,
                      type: 'company',
                      name: item.company,
                      relatedTo: opportunityId
                    });
                  }
                }
              } catch (error) {
                console.error('Error creating company relationship:', error);
                relationships.edges.failed++;
              }
            }
          }
        } catch (error) {
          console.error('Error importing item:', error, item);
          failedItems.push({ item, error: error.message });
        }
      }

      // Set relationship results
      setRelationshipResults(relationships);

      // Save created node IDs for arrangement
      setCreatedNodeIds(createdNodes);

      // Set import results
      setImportResults({
        total: validItems.length,
        imported: importedItems.length,
        failed: failedItems.length,
        items: importedItems,
        failedItems
      });

      // Arrange nodes in flow view if enabled
      if (arrangeInFlowView && (createdNodes.opportunities.length > 0 ||
        createdNodes.contacts.length > 0 || createdNodes.companies.length > 0)) {
        await arrangeNodesInFlowView(createdNodes, organization.id);
      }

    } catch (error) {
      console.error('Error importing data:', error);
      setErrors([`Error importing data: ${error.message}`]);
    } finally {
      setImporting(false);
    }
  };

  // Create or link a contact
  const createOrLinkContact = async (contactName, organizationId) => {
    if (!contactName) return null;

    try {
      // First, check if contact already exists
      const contactsCollection = collection(db, 'contacts');
      const q = query(
        contactsCollection,
        where('name', '==', contactName),
        where('organizationId', '==', organizationId)
      );

      const querySnapshot = await getDocs(q);

      // If contact exists, use it
      if (!querySnapshot.empty) {
        const contactDoc = querySnapshot.docs[0];
        setRelationshipResults(prev => ({
          ...prev,
          contacts: {
            ...prev.contacts,
            linked: prev.contacts.linked + 1
          }
        }));
        return { contactId: contactDoc.id, isNew: false };
      }

      // Otherwise, create a new contact
      const newContact = {
        name: contactName,
        organizationId,
        createdBy: currentUser.uid,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        archived: false
      };

      const docRef = await addDoc(contactsCollection, newContact);

      setRelationshipResults(prev => ({
        ...prev,
        contacts: {
          ...prev.contacts,
          created: prev.contacts.created + 1
        }
      }));

      return { contactId: docRef.id, isNew: true };
    } catch (error) {
      console.error('Error creating/linking contact:', error);
      setRelationshipResults(prev => ({
        ...prev,
        contacts: {
          ...prev.contacts,
          failed: prev.contacts.failed + 1
        }
      }));
      return null;
    }
  };

  // Create or link a company
  const createOrLinkCompany = async (companyName, organizationId) => {
    if (!companyName) return null;

    try {
      // First, check if company already exists
      const companiesCollection = collection(db, 'companies');
      const q = query(
        companiesCollection,
        where('name', '==', companyName),
        where('organizationId', '==', organizationId)
      );

      const querySnapshot = await getDocs(q);

      // If company exists, use it
      if (!querySnapshot.empty) {
        const companyDoc = querySnapshot.docs[0];
        setRelationshipResults(prev => ({
          ...prev,
          companies: {
            ...prev.companies,
            linked: prev.companies.linked + 1
          }
        }));
        return { companyId: companyDoc.id, isNew: false };
      }

      // Otherwise, create a new company
      const newCompany = {
        name: companyName,
        organizationId,
        createdBy: currentUser.uid,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        archived: false
      };

      const docRef = await addDoc(companiesCollection, newCompany);

      setRelationshipResults(prev => ({
        ...prev,
        companies: {
          ...prev.companies,
          created: prev.companies.created + 1
        }
      }));

      return { companyId: docRef.id, isNew: true };
    } catch (error) {
      console.error('Error creating/linking company:', error);
      setRelationshipResults(prev => ({
        ...prev,
        companies: {
          ...prev.companies,
          failed: prev.companies.failed + 1
        }
      }));
      return null;
    }
  };

  // Create an edge between two nodes
  const createEdge = async (sourceId, targetId, sourceType, targetType, organizationId) => {
    try {
      const edgeId = `${sourceType}-${sourceId}_${targetType}-${targetId}`;
      const edgeData = {
        id: edgeId,
        source: `${sourceType}-${sourceId}`,
        target: `${targetType}-${targetId}`,
        organizationId,
        createdBy: currentUser.uid,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      // Use setDoc with merge to handle potential duplicates
      await setDoc(doc(db, 'edges', edgeId), edgeData, { merge: true });

      return edgeId;
    } catch (error) {
      console.error('Error creating edge:', error);
      throw error;
    }
  };

  // Arrange nodes in flow view
  const arrangeNodesInFlowView = async (nodes, organizationId) => {
    try {
      const { opportunities, contacts, companies } = nodes;

      if (opportunities.length === 0) return;

      // Calculate positions for opportunities
      const startX = 100;
      const startY = 100;
      const opportunitySpacingY = 200;
      const contactOffsetX = 300;
      const companyOffsetX = -300;

      // Create a map to track positions
      const nodePositions = {};

      // Position opportunities in a vertical column
      opportunities.forEach((opportunity, index) => {
        const position = {
          x: startX,
          y: startY + (index * opportunitySpacingY)
        };

        nodePositions[`opportunity-${opportunity.id}`] = position;
      });

      // Position contacts to the right of their related opportunities
      contacts.forEach(contact => {
        const relatedOpportunityId = contact.relatedTo;
        const relatedOpportunityPosition = nodePositions[`opportunity-${relatedOpportunityId}`];

        if (relatedOpportunityPosition) {
          const position = {
            x: relatedOpportunityPosition.x + contactOffsetX,
            y: relatedOpportunityPosition.y
          };

          nodePositions[`contact-${contact.id}`] = position;
        }
      });

      // Position companies to the left of their related opportunities
      companies.forEach(company => {
        const relatedOpportunityId = company.relatedTo;
        const relatedOpportunityPosition = nodePositions[`opportunity-${relatedOpportunityId}`];

        if (relatedOpportunityPosition) {
          const position = {
            x: relatedOpportunityPosition.x + companyOffsetX,
            y: relatedOpportunityPosition.y
          };

          nodePositions[`company-${company.id}`] = position;
        }
      });

      // Update positions in Firestore
      const nodePositionsCollection = collection(db, 'nodePositions');

      for (const nodeId in nodePositions) {
        try {
          const position = nodePositions[nodeId];

          // Create a unique ID for the node position document
          const positionDocId = `${nodeId}-position`;

          await setDoc(doc(db, 'nodePositions', positionDocId), {
            id: nodeId,
            position,
            organizationId,
            createdBy: currentUser.uid,
            updatedAt: new Date()
          }, { merge: true });

        } catch (error) {
          console.error(`Error updating position for ${nodeId}:`, error);
        }
      }

      // Navigate to flow view after arrangement is complete
      setTimeout(() => {
        navigate('/flow');
      }, 1000);

    } catch (error) {
      console.error('Error arranging nodes in flow view:', error);
    }
  };

  return (
    <div className="importer-container">
      <div className="card">
        <div className="card-body">
          {!importResults ? (
            <>
              <h4 className="card-title mb-4">Import Opportunities from Excel</h4>

              {/* File Selection */}
              <div className="mb-4">
                <label htmlFor="excelFile" className="form-label">Select Excel File</label>
                <input
                  type="file"
                  className="form-control"
                  id="excelFile"
                  accept=".xlsx,.xls"
                  onChange={handleFileChange}
                />
              </div>

              {file && (
                <>
                  {/* Sheet Selection */}
                  <div className="row mb-4">
                    <div className="col-md-6">
                      <label htmlFor="sheetSelect" className="form-label">Select Sheet</label>
                      <select
                        id="sheetSelect"
                        className="form-select"
                        value={selectedSheet}
                        onChange={handleSheetChange}
                      >
                        {sheets.map((sheet, index) => (
                          <option key={index} value={index}>{sheet}</option>
                        ))}
                      </select>
                    </div>

                    <div className="col-md-6">
                      <label htmlFor="headerRow" className="form-label">Header Row</label>
                      <input
                        type="number"
                        className="form-control"
                        id="headerRow"
                        min="1"
                        value={headerRow}
                        onChange={handleHeaderRowChange}
                      />
                    </div>
                  </div>

                  {/* Preview Data */}
                  {previewData.length > 0 && (
                    <div className="mb-4">
                      <h5>Data Preview</h5>
                      <div className="table-responsive">
                        <table className="table table-sm table-bordered">
                          <thead>
                            <tr>
                              {Object.keys(previewData[0]).map((header, index) => (
                                <th key={index}>{header}</th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {previewData.map((row, rowIndex) => (
                              <tr key={rowIndex}>
                                {Object.values(row).map((cell, cellIndex) => (
                                  <td key={cellIndex}>{cell?.toString() || ''}</td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* Field Mapping */}
                  {availableFields.length > 0 && (
                    <div className="mb-4">
                      <h5>Field Mapping</h5>
                      <p className="text-muted small">Map Excel columns to opportunity fields</p>

                      <div className="table-responsive">
                        <table className="table table-sm">
                          <thead>
                            <tr>
                              <th>Excel Column</th>
                              <th>Opportunity Field</th>
                            </tr>
                          </thead>
                          <tbody>
                            {availableFields.map((field, index) => (
                              <tr key={index}>
                                <td>{field.label}</td>
                                <td>
                                  <select
                                    className="form-select form-select-sm"
                                    value={fieldMappings[field.id] || ''}
                                    onChange={(e) => handleMappingChange(field.id, e.target.value)}
                                  >
                                    <option value="">-- Ignore --</option>
                                    {targetFields.map((target, idx) => (
                                      <option
                                        key={idx}
                                        value={target.id}
                                        disabled={target.required && Object.values(fieldMappings).includes(target.id) && fieldMappings[field.id] !== target.id}
                                      >
                                        {target.label}{target.required ? ' *' : ''}
                                      </option>
                                    ))}
                                  </select>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                      <p className="text-muted small">* Required fields</p>
                    </div>
                  )}

                  {/* Error Messages */}
                  {errors.length > 0 && (
                    <div className="alert alert-danger">
                      <ul className="mb-0">
                        {errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Relationship Options */}
                  <div className="mb-4">
                    <h5>Flow View Options</h5>
                    <div className="form-check mb-2">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        id="createRelationships"
                        checked={createRelationships}
                        onChange={(e) => setCreateRelationships(e.target.checked)}
                      />
                      <label className="form-check-label" htmlFor="createRelationships">
                        Create relationships in Flow View
                      </label>
                      <div className="text-muted small">
                        When enabled, contacts and companies will be created and linked to opportunities in the Flow View.
                        This will create nodes and connections automatically.
                      </div>
                    </div>

                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        id="arrangeInFlowView"
                        checked={arrangeInFlowView}
                        onChange={(e) => setArrangeInFlowView(e.target.checked)}
                        disabled={!createRelationships}
                      />
                      <label className="form-check-label" htmlFor="arrangeInFlowView">
                        Arrange nodes in Flow View
                      </label>
                      <div className="text-muted small">
                        When enabled, imported opportunities and their related contacts/companies will be
                        automatically arranged in the Flow View. Opportunities will be placed in a vertical column,
                        with contacts to the right and companies to the left.
                      </div>
                    </div>
                  </div>

                  {/* Import Button */}
                  <div className="d-flex justify-content-end">
                    <button
                      className="btn btn-primary"
                      onClick={handleImport}
                      disabled={importing || !file || previewData.length === 0}
                    >
                      {importing ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Importing...
                        </>
                      ) : 'Import Opportunities'}
                    </button>
                  </div>
                </>
              )}
            </>
          ) : (
            // Import Results
            <div className="import-results">
              <h4 className="card-title mb-4">Import Results</h4>

              <div className="alert alert-success">
                <h5>Import Complete</h5>
                <p>
                  Successfully imported {importResults.imported} of {importResults.total} opportunities.
                  {importResults.failed > 0 && ` ${importResults.failed} items failed.`}
                </p>

                {createRelationships && (
                  <div className="mt-2">
                    <h6>Relationships Created:</h6>
                    <ul className="mb-0">
                      <li>
                        <strong>Contacts:</strong> {relationshipResults.contacts.created} created,
                        {relationshipResults.contacts.linked} linked to existing
                        {relationshipResults.contacts.failed > 0 && `, ${relationshipResults.contacts.failed} failed`}
                      </li>
                      <li>
                        <strong>Companies:</strong> {relationshipResults.companies.created} created,
                        {relationshipResults.companies.linked} linked to existing
                        {relationshipResults.companies.failed > 0 && `, ${relationshipResults.companies.failed} failed`}
                      </li>
                      <li>
                        <strong>Connections:</strong> {relationshipResults.edges.created} created
                        {relationshipResults.edges.failed > 0 && `, ${relationshipResults.edges.failed} failed`}
                      </li>
                    </ul>
                    <div className="mt-2 small text-muted">
                      All relationships are visible in the Flow View.
                    </div>

                    {arrangeInFlowView && (
                      <div className="mt-3">
                        <h6>Flow View Arrangement:</h6>
                        <p className="mb-0">
                          {createdNodeIds.opportunities.length > 0 ? (
                            <>
                              {createdNodeIds.opportunities.length} opportunities have been arranged in the Flow View
                              {(createdNodeIds.contacts.length > 0 || createdNodeIds.companies.length > 0) &&
                                ` along with ${createdNodeIds.contacts.length} contacts and ${createdNodeIds.companies.length} companies`}.
                              <br />
                              <a href="/flow" className="btn btn-sm btn-outline-primary mt-2">
                                <i className="bi bi-diagram-3 me-1"></i>
                                View in Flow View
                              </a>
                            </>
                          ) : (
                            'No nodes were arranged in the Flow View.'
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {importResults.items.length > 0 && (
                <div className="mb-4">
                  <h5>Imported Opportunities</h5>
                  <div className="table-responsive">
                    <table className="table table-sm table-striped">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Value</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {importResults.items.slice(0, 10).map((item, index) => (
                          <tr key={index}>
                            <td>{item.name}</td>
                            <td>{item.value} {item.currency}</td>
                            <td>{item.status}</td>
                          </tr>
                        ))}
                        {importResults.items.length > 10 && (
                          <tr>
                            <td colSpan="3" className="text-center">
                              ...and {importResults.items.length - 10} more
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {importResults.failedItems.length > 0 && (
                <div className="mb-4">
                  <h5>Failed Items</h5>
                  <div className="table-responsive">
                    <table className="table table-sm table-danger">
                      <thead>
                        <tr>
                          <th>Item</th>
                          <th>Error</th>
                        </tr>
                      </thead>
                      <tbody>
                        {importResults.failedItems.map((failure, index) => (
                          <tr key={index}>
                            <td>{JSON.stringify(failure.item)}</td>
                            <td>{failure.error}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              <div className="d-flex justify-content-end">
                <button
                  className="btn btn-primary"
                  onClick={() => {
                    setFile(null);
                    setPreviewData([]);
                    setImportResults(null);
                    setErrors([]);
                  }}
                >
                  Import Another File
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExcelImporter;
