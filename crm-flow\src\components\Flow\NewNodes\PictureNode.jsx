import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>le, Position } from '@xyflow/react';
import { useAuth } from '../../../contexts/AuthContext';
import { uploadFile, getNodeFiles, deleteFile } from '../../../services/localStorageService';
import NodeMenu from './NodeMenu';
import './PictureNodeStyles.css';

const PictureNode = ({ id, data, selected }) => {
  const { currentUser, organization } = useAuth();
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(null);
  const fileInputRef = useRef(null);

  // Format date for display
  const formatDate = (date) => {
    if (!date) return 'Unknown';
    return typeof date === 'object' && date.toDate
      ? date.toDate().toLocaleDateString()
      : new Date(date).toLocaleDateString();
  };

  // Load files on component mount
  useEffect(() => {
    const loadFiles = async () => {
      setLoading(true);
      try {
        const nodeFiles = await getNodeFiles(id);
        setFiles(nodeFiles);
      } catch (error) {
        console.error('Error loading files:', error);
        setError('Failed to load files');
      } finally {
        setLoading(false);
      }
    };

    loadFiles();
  }, [id]);

  // Handle file upload
  const handleFileUpload = async (uploadedFiles) => {
    if (!currentUser || !organization) return;

    setLoading(true);
    setError('');
    setUploadProgress(0);

    try {
      const fileArray = Array.from(uploadedFiles);
      const uploadedFileData = [];

      // Process each file
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i];

        // Check file type
        const fileExt = file.name.split('.').pop().toLowerCase();
        const allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'pdf'];

        if (!allowedTypes.includes(fileExt)) {
          setError(`File type not allowed: ${fileExt}. Only images and PDFs are allowed.`);
          continue;
        }

        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setError('File too large. Maximum size is 10MB.');
          continue;
        }

        // Upload file
        const result = await uploadFile(file, currentUser.uid, organization.id, id);

        if (result.success) {
          uploadedFileData.push(result.fileData);
          setUploadProgress(Math.round(((i + 1) / fileArray.length) * 100));
        } else {
          setError(`Failed to upload ${file.name}: ${result.error}`);
        }
      }

      // Update files list
      setFiles(prevFiles => [...prevFiles, ...uploadedFileData]);

    } catch (error) {
      console.error('Error uploading files:', error);
      setError('Failed to upload files');
    } finally {
      setLoading(false);
      setUploadProgress(null);
    }
  };

  // Handle file deletion
  const handleDeleteFile = async (docId, storagePath) => {
    if (!confirm('Are you sure you want to delete this file?')) return;

    setLoading(true);
    setError('');

    try {
      const result = await deleteFile(docId, storagePath);

      if (result.success) {
        // Remove file from list
        setFiles(prevFiles => prevFiles.filter(file => file.docId !== docId));
      } else {
        setError(`Failed to delete file: ${result.error}`);
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      setError('Failed to delete file');
    } finally {
      setLoading(false);
    }
  };

  // Handle drag events
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileUpload(e.dataTransfer.files);
    }
  }, [handleFileUpload]);

  // Handle file input change
  const handleFileInputChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileUpload(e.target.files);
    }
  };

  // Handle browse button click
  const handleBrowseClick = () => {
    fileInputRef.current.click();
  };

  // Handle opening file in new tab
  const handleOpenFile = (dataUrl, fileName) => {
    // For data URLs, we need to create a new window and set the content
    const newWindow = window.open('', '_blank');
    if (!newWindow) {
      alert('Please allow popups for this site to view files');
      return;
    }

    // Create HTML content based on file type
    const isImage = dataUrl.startsWith('data:image/');
    const isPdf = dataUrl.startsWith('data:application/pdf');

    let htmlContent = '';

    if (isImage) {
      htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${fileName}</title>
          <style>
            body { margin: 0; display: flex; justify-content: center; align-items: center; height: 100vh; background-color: #1e1e1e; }
            img { max-width: 95%; max-height: 95%; object-fit: contain; }
          </style>
        </head>
        <body>
          <img src="${dataUrl}" alt="${fileName}" />
        </body>
        </html>
      `;
    } else if (isPdf) {
      htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${fileName}</title>
          <style>
            body { margin: 0; height: 100vh; }
            embed { width: 100%; height: 100%; }
          </style>
        </head>
        <body>
          <embed src="${dataUrl}" type="application/pdf" width="100%" height="100%" />
        </body>
        </html>
      `;
    } else {
      // For other file types, offer download
      htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${fileName}</title>
          <style>
            body { font-family: Arial, sans-serif; display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100vh; background-color: #f5f5f5; }
            .container { text-align: center; padding: 20px; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h2 { margin-top: 0; color: #333; }
            p { color: #666; }
            a { display: inline-block; margin-top: 20px; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; }
            a:hover { background-color: #45a049; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>File: ${fileName}</h2>
            <p>This file type cannot be previewed directly in the browser.</p>
            <a href="${dataUrl}" download="${fileName}">Download File</a>
          </div>
        </body>
        </html>
      `;
    }

    newWindow.document.write(htmlContent);
    newWindow.document.close();
  };

  // Handle node actions
  const handleEdit = () => {
    // Not implemented for picture nodes
  };

  const handleDelete = () => {
    if (data.onDelete) data.onDelete(id);
  };

  const handleArchive = () => {
    if (data.onArchive) data.onArchive(id);
  };

  const handleRestore = () => {
    if (data.onRestore) data.onRestore(id);
  };

  // Get file icon based on type
  const getFileIcon = (fileType) => {
    if (fileType.includes('pdf')) return 'bi-file-earmark-pdf';
    if (fileType.includes('image')) return 'bi-file-earmark-image';
    return 'bi-file-earmark';
  };

  return (
    <div className={`node-card node-picture ${data.archived ? 'node-archived' : ''} ${isDragging ? 'dragging' : ''}`}>
      <NodeMenu
        onEdit={handleEdit}
        onDelete={handleDelete}
        onArchive={handleArchive}
        isArchived={data.archived}
        onRestore={handleRestore}
        nodeType="picture"
        hideEdit={true}
      />
      <div className="node-header">
        <h6 className="node-title">Picture Collection</h6>
        <span className="node-badge">
          {files.length} {files.length === 1 ? 'file' : 'files'}
        </span>
      </div>
      <div
        className="node-body picture-node-body"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {loading && (
          <div className="upload-overlay">
            {uploadProgress !== null ? (
              <div className="progress" style={{ height: '20px' }}>
                <div
                  className="progress-bar"
                  role="progressbar"
                  style={{ width: `${uploadProgress}%` }}
                  aria-valuenow={uploadProgress}
                  aria-valuemin="0"
                  aria-valuemax="100"
                >
                  {uploadProgress}%
                </div>
              </div>
            ) : (
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="alert alert-danger py-1 mb-2">
            <small>{error}</small>
          </div>
        )}

        <div className="drop-zone">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInputChange}
            style={{ display: 'none' }}
            multiple
            accept="image/*,application/pdf"
          />

          {files.length === 0 ? (
            <div className="text-center p-3">
              <i className="bi bi-cloud-upload fs-3 mb-2"></i>
              <p className="mb-1">Drag & drop files here</p>
              <p className="text-muted small">or</p>
              <button
                className="btn btn-sm btn-outline-primary"
                onClick={handleBrowseClick}
              >
                Browse Files
              </button>
            </div>
          ) : (
            <>
              <div className="file-list">
                {files.map((file) => (
                  <div key={file.docId} className="file-item">
                    <div className="file-icon">
                      <i className={`bi ${getFileIcon(file.fileType)}`}></i>
                    </div>
                    <div className="file-info" onClick={() => handleOpenFile(file.dataUrl, file.originalName)}>
                      <div className="file-name">{file.originalName}</div>
                      <div className="file-meta">
                        <span className="file-size">{Math.round(file.fileSize / 1024)} KB</span>
                        <span className="file-date">{formatDate(file.uploadedAt)}</span>
                      </div>
                    </div>
                    <button
                      className="btn btn-sm btn-outline-danger file-delete"
                      onClick={() => handleDeleteFile(file.id, file.docId)}
                    >
                      <i className="bi bi-trash"></i>
                    </button>
                  </div>
                ))}
              </div>
              <div className="text-center mt-2">
                <button
                  className="btn btn-sm btn-outline-primary"
                  onClick={handleBrowseClick}
                >
                  <i className="bi bi-plus-lg me-1"></i>
                  Add More Files
                </button>
              </div>
            </>
          )}
        </div>
      </div>
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: '#555' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: '#555' }}
      />
    </div>
  );
};

export default React.memo(PictureNode);
