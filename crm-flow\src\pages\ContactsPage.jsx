import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy, deleteDoc, doc, updateDoc, writeBatch, where } from 'firebase/firestore';
import { db } from '../services/firebase';
import { useAuth } from '../contexts/AuthContext';
import { getUserData } from '../services/dataAccessService';
import { useNavigate } from 'react-router-dom';
import Modal from '../components/UI/Modal';
import ContactForm from '../components/Forms/ContactForm';
import 'bootstrap/dist/css/bootstrap.min.css';

const ContactsPage = () => {
  const { currentUser, organization } = useAuth();
  const navigate = useNavigate();
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentContact, setCurrentContact] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusMessage, setStatusMessage] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);

  // Fetch contacts
  const fetchContacts = async () => {
    try {
      setLoading(true);

      // Get organization ID if available
      const orgId = organization?.id || null;

      // Use the getUserData function to get contacts
      const contactsList = await getUserData('contacts', currentUser.uid, orgId);

      // Sort contacts by name, or firstName + lastName if name is not available
      const sortedContacts = contactsList.sort((a, b) => {
        const aName = a.name || (a.firstName && a.lastName ? `${a.firstName} ${a.lastName}` : (a.firstName || a.lastName || ''));
        const bName = b.name || (b.firstName && b.lastName ? `${b.firstName} ${b.lastName}` : (b.firstName || b.lastName || ''));
        return aName.localeCompare(bName);
      });

      console.log('Fetched contacts:', sortedContacts);
      setContacts(sortedContacts);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching contacts:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchContacts();
    }
  }, [currentUser, organization]);

  // Handle sending contact to flow view
  const handleSendToFlow = async (contact) => {
    try {
      const contactRef = doc(db, 'contacts', contact.id);

      // Update data to include organization context
      const updateData = {
        archived: false, // Make sure it's not archived
        updatedAt: new Date()
      };

      // If part of an organization, ensure the organizationId is set
      if (organization?.id) {
        updateData.organizationId = organization.id;
      }

      // Update the document
      await updateDoc(contactRef, updateData);

      setStatusMessage(`Contact "${contact.name}" sent to flow view successfully!`);

      // Refresh contacts list
      fetchContacts();

      // Clear status message after 3 seconds
      setTimeout(() => {
        setStatusMessage('');
      }, 3000);
    } catch (error) {
      console.error('Error sending contact to flow:', error);
      setStatusMessage(`Error: ${error.message}`);
    }
  };

  // Handle contact deletion
  const handleDeleteContact = async (id) => {
    if (window.confirm('Are you sure you want to delete this contact?')) {
      try {
        await deleteDoc(doc(db, 'contacts', id));
        // Refresh the contacts list
        fetchContacts();
      } catch (error) {
        console.error('Error deleting contact:', error);
      }
    }
  };

  // Handle contact edit
  const handleEditContact = (contact) => {
    setCurrentContact(contact);
    setShowEditModal(true);
  };

  // Handle deleting all contacts
  const handleDeleteAllContacts = async () => {
    if (!organization?.id) return;

    try {
      setIsDeleting(true);

      // Create a query to get all contacts for this organization
      const contactsQuery = query(
        collection(db, 'contacts'),
        where('organizationId', '==', organization.id)
      );

      const snapshot = await getDocs(contactsQuery);

      if (snapshot.empty) {
        setIsDeleting(false);
        setShowDeleteConfirmModal(false);
        return;
      }

      // Use a batch to delete all contacts
      const batch = writeBatch(db);

      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      // Commit the batch
      await batch.commit();

      // Also delete related edges in the flow view
      const edgesQuery = query(
        collection(db, 'edges'),
        where('organizationId', '==', organization.id)
      );

      const edgesSnapshot = await getDocs(edgesQuery);

      if (!edgesSnapshot.empty) {
        const edgesBatch = writeBatch(db);

        edgesSnapshot.docs.forEach(doc => {
          const edgeData = doc.data();
          // Only delete edges that are connected to contacts
          if (edgeData.source.startsWith('contact-') || edgeData.target.startsWith('contact-')) {
            edgesBatch.delete(doc.ref);
          }
        });

        await edgesBatch.commit();
      }

      // Refresh the contacts list
      fetchContacts();

      setStatusMessage('All contacts have been deleted successfully.');

      // Clear status message after 5 seconds
      setTimeout(() => {
        setStatusMessage('');
      }, 5000);

      setIsDeleting(false);
      setShowDeleteConfirmModal(false);
    } catch (error) {
      console.error('Error deleting all contacts:', error);
      setStatusMessage(`Error deleting contacts: ${error.message}`);
      setIsDeleting(false);
      setShowDeleteConfirmModal(false);
    }
  };

  // This function has been replaced by the new handleSendToFlow function above

  // Filter contacts based on search term
  const filteredContacts = contacts.filter(contact => {
    const searchTermLower = searchTerm.toLowerCase();
    return (
      (contact.name && contact.name.toLowerCase().includes(searchTermLower)) ||
      (contact.email && contact.email.toLowerCase().includes(searchTermLower)) ||
      (contact.company && contact.company.toLowerCase().includes(searchTermLower)) ||
      // If we have firstName and lastName instead of name
      (contact.firstName && contact.firstName.toLowerCase().includes(searchTermLower)) ||
      (contact.lastName && contact.lastName.toLowerCase().includes(searchTermLower))
    );
  });

  return (
    <div style={{ height: '100%', width: '100%', overflow: 'auto', padding: '20px' }}>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h2>Contacts</h2>
          <button
            className="btn btn-success"
            onClick={() => setShowAddModal(true)}
          >
            <i className="bi bi-person-plus me-2"></i>
            Add Contact
          </button>
        </div>

        {/* Status Message */}
        {statusMessage && (
          <div className="alert alert-info alert-dismissible fade show mb-3" role="alert">
            {statusMessage}
            <button type="button" className="btn-close" onClick={() => setStatusMessage('')} aria-label="Close"></button>
          </div>
        )}

        {/* Search Bar */}
        <div className="row mb-4">
          <div className="col-md-6">
            <div className="input-group">
              <span className="input-group-text">
                <i className="bi bi-search"></i>
              </span>
              <input
                type="text"
                className="form-control"
                placeholder="Search contacts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="btn btn-outline-secondary"
                  type="button"
                  onClick={() => setSearchTerm('')}
                >
                  <i className="bi bi-x"></i>
                </button>
              )}
            </div>
          </div>
          <div className="col-md-6 d-flex justify-content-end">
            {filteredContacts.length > 0 && (
              <button
                className="btn btn-outline-danger"
                onClick={() => setShowDeleteConfirmModal(true)}
              >
                <i className="bi bi-trash me-2"></i>
                Delete All Contacts
              </button>
            )}
          </div>
        </div>

        {loading ? (
          <div className="d-flex justify-content-center mt-5">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : (
          <>
            {filteredContacts.length === 0 ? (
              <div className="alert alert-info">
                {searchTerm ? 'No contacts match your search.' : 'No contacts found. Add your first contact!'}
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-sm table-hover">
                  <thead className="table-light">
                    <tr>
                      <th>Name</th>
                      <th style={{ width: '120px' }}>Title</th>
                      <th style={{ width: '120px' }}>Company</th>
                      <th style={{ width: '180px' }}>Email</th>
                      <th style={{ width: '120px' }}>Phone</th>
                      <th style={{ width: '140px' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredContacts.map(contact => (
                      <tr key={contact.id}>
                        <td>{contact.name || (contact.firstName && contact.lastName ? `${contact.firstName} ${contact.lastName}` : (contact.firstName || contact.lastName || 'Unnamed Contact'))}</td>
                        <td><small>{contact.title || '-'}</small></td>
                        <td><small>{contact.company || '-'}</small></td>
                        <td>
                          <small><a href={`mailto:${contact.email}`}>{contact.email}</a></small>
                        </td>
                        <td><small>{contact.phone || '-'}</small></td>
                        <td>
                          <div className="btn-group btn-group-sm">
                            <button
                              className="btn btn-sm btn-outline-primary"
                              title="Edit"
                              onClick={() => handleEditContact(contact)}
                            >
                              <i className="bi bi-pencil"></i>
                            </button>
                            <button
                              className="btn btn-sm btn-outline-success"
                              title="Send to Flow"
                              onClick={() => handleSendToFlow(contact)}
                            >
                              <i className="bi bi-diagram-3"></i>
                            </button>
                            <button
                              className="btn btn-sm btn-outline-danger"
                              title="Delete"
                              onClick={() => handleDeleteContact(contact.id)}
                            >
                              <i className="bi bi-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </>
        )}
      </div>

      {/* Add Contact Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="lg"
      >
        <ContactForm
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            fetchContacts();
          }}
        />
      </Modal>

      {/* Edit Contact Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="lg"
      >
        {currentContact && (
          <ContactForm
            onClose={() => setShowEditModal(false)}
            onSuccess={() => {
              setShowEditModal(false);
              fetchContacts();
            }}
            contactData={currentContact}
            isEditing={true}
          />
        )}
      </Modal>

      {/* Delete All Confirmation Modal */}
      <Modal
        show={showDeleteConfirmModal}
        onClose={() => setShowDeleteConfirmModal(false)}
        size="md"
      >
        <div className="modal-header">
          <h5 className="modal-title">Confirm Delete All</h5>
          <button
            type="button"
            className="btn-close"
            onClick={() => setShowDeleteConfirmModal(false)}
          ></button>
        </div>
        <div className="modal-body">
          <div className="alert alert-danger">
            <i className="bi bi-exclamation-triangle-fill me-2"></i>
            Warning: This action cannot be undone!
          </div>
          <p>
            Are you sure you want to delete all contacts?
            This will also remove any connections in the Flow View.
          </p>
          <p>
            <strong>Total contacts to delete: {filteredContacts.length}</strong>
          </p>
        </div>
        <div className="modal-footer">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => setShowDeleteConfirmModal(false)}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-danger"
            onClick={handleDeleteAllContacts}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Deleting...
              </>
            ) : (
              'Delete All'
            )}
          </button>
        </div>
      </Modal>
    </div>
  );
};

export default ContactsPage;
