.custom-modal {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 1.5rem;
}

.modal-title {
  font-weight: 600;
  color: #2563eb;
  font-size: 1.25rem;
}

.modal-body {
  background-color: #fff;
}

.btn-close {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  padding: 0.5rem;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  opacity: 1;
}

/* Form styling within modals */
.custom-modal .form-label {
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.custom-modal .form-control,
.custom-modal .form-select {
  border-radius: 8px;
  padding: 0.625rem 0.75rem;
  border-color: rgba(0, 0, 0, 0.15);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.custom-modal .form-control:focus,
.custom-modal .form-select:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
}

.custom-modal .btn {
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.custom-modal .btn-primary {
  background-color: #2563eb;
  border-color: #2563eb;
}

.custom-modal .btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
}

/* Animation for modal */
.modal.fade.show {
  animation: modalFadeIn 0.3s ease forwards;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-dialog {
  animation: modalSlideIn 0.3s ease forwards;
}

@keyframes modalSlideIn {
  from {
    transform: translate(0, -30px);
    opacity: 0;
  }
  to {
    transform: translate(0, 0);
    opacity: 1;
  }
}
