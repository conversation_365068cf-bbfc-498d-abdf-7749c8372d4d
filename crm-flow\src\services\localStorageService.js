import { v4 as uuidv4 } from 'uuid';
import { db } from './firebase';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  query,
  where,
  getDocs
} from 'firebase/firestore';

// IndexedDB setup
const DB_NAME = 'crm-files-db';
const DB_VERSION = 1;
const FILES_STORE = 'files';

// Collection name for file metadata
const FILES_COLLECTION = 'pictureNodeFiles';

/**
 * Initialize the IndexedDB database
 * @returns {Promise<IDBDatabase>} - The database instance
 */
const initDB = () => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('Error opening IndexedDB:', event.target.error);
      reject(event.target.error);
    };

    request.onsuccess = (event) => {
      resolve(event.target.result);
    };

    request.onupgradeneeded = (event) => {
      const db = event.target.result;

      // Create object store for files
      if (!db.objectStoreNames.contains(FILES_STORE)) {
        const store = db.createObjectStore(FILES_STORE, { keyPath: 'id' });
        store.createIndex('nodeId', 'nodeId', { unique: false });
      }
    };
  });
};

/**
 * Store a file in IndexedDB
 * @param {Object} fileData - The file data to store
 * @returns {Promise<string>} - The file ID
 */
const storeFile = async (fileData) => {
  try {
    const db = await initDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([FILES_STORE], 'readwrite');
      const store = transaction.objectStore(FILES_STORE);

      const request = store.add(fileData);

      request.onsuccess = () => {
        resolve(fileData.id);
      };

      request.onerror = (event) => {
        console.error('Error storing file in IndexedDB:', event.target.error);
        reject(event.target.error);
      };
    });
  } catch (error) {
    console.error('Error in storeFile:', error);
    throw error;
  }
};

/**
 * Get a file from IndexedDB by ID
 * @param {string} fileId - The file ID
 * @returns {Promise<Object>} - The file data
 */
const getFile = async (fileId) => {
  try {
    const db = await initDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([FILES_STORE], 'readonly');
      const store = transaction.objectStore(FILES_STORE);

      const request = store.get(fileId);

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = (event) => {
        console.error('Error getting file from IndexedDB:', event.target.error);
        reject(event.target.error);
      };
    });
  } catch (error) {
    console.error('Error in getFile:', error);
    throw error;
  }
};

/**
 * Get all files for a node
 * @param {string} nodeId - The node ID
 * @returns {Promise<Array>} - Array of file data
 */
const getFilesForNode = async (nodeId) => {
  try {
    const db = await initDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([FILES_STORE], 'readonly');
      const store = transaction.objectStore(FILES_STORE);
      const index = store.index('nodeId');

      const request = index.getAll(nodeId);

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = (event) => {
        console.error('Error getting node files from IndexedDB:', event.target.error);
        reject(event.target.error);
      };
    });
  } catch (error) {
    console.error('Error in getFilesForNode:', error);
    throw error;
  }
};

/**
 * Delete a file from IndexedDB
 * @param {string} fileId - The file ID
 * @returns {Promise<boolean>} - Success status
 */
const deleteFileFromIndexedDB = async (fileId) => {
  try {
    const db = await initDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([FILES_STORE], 'readwrite');
      const store = transaction.objectStore(FILES_STORE);

      const request = store.delete(fileId);

      request.onsuccess = () => {
        resolve(true);
      };

      request.onerror = (event) => {
        console.error('Error deleting file from IndexedDB:', event.target.error);
        reject(event.target.error);
      };
    });
  } catch (error) {
    console.error('Error in deleteFileFromIndexedDB:', error);
    throw error;
  }
};

/**
 * Upload a file to local storage and save metadata to Firestore
 * @param {File} file - The file to upload
 * @param {string} userId - The user ID
 * @param {string} organizationId - The organization ID
 * @param {string} nodeId - The node ID this file belongs to
 * @returns {Promise<Object>} - The result of the operation
 */
export const uploadFile = async (file, userId, organizationId, nodeId) => {
  try {
    // Generate a unique ID for the file
    const fileId = uuidv4();

    // Read the file as data URL
    const fileDataUrl = await readFileAsDataURL(file);

    // Create file metadata
    const fileData = {
      id: fileId,
      originalName: file.name,
      fileType: file.type,
      fileSize: file.size,
      fileExt: file.name.split('.').pop().toLowerCase(),
      nodeId: nodeId,
      organizationId: organizationId,
      uploadedBy: userId,
      uploadedAt: new Date(),
      dataUrl: fileDataUrl
    };

    // Store file in IndexedDB
    await storeFile(fileData);

    // Store metadata in Firestore (without the actual file data)
    const metadataForFirestore = {
      ...fileData,
      dataUrl: null // Don't store the actual file data in Firestore
    };

    const docRef = await addDoc(collection(db, FILES_COLLECTION), metadataForFirestore);

    return {
      success: true,
      fileId: fileId,
      docId: docRef.id,
      fileData: {
        ...fileData,
        docId: docRef.id
      }
    };
  } catch (error) {
    console.error('Error uploading file:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get all files for a node (exported function)
 * @param {string} nodeId - The node ID
 * @returns {Promise<Array>} - Array of file metadata
 */
export const getNodeFiles = async (nodeId) => {
  try {
    // Get files from IndexedDB using the internal function
    const files = await getFilesForNode(nodeId);
    return files;
  } catch (error) {
    console.error('Error getting node files:', error);
    return [];
  }
};

/**
 * Delete a file from local storage and update its metadata
 * @param {string} fileId - The file ID
 * @param {string} docId - The Firestore document ID
 * @returns {Promise<Object>} - The result of the operation
 */
export const deleteFile = async (fileId, docId) => {
  try {
    // Delete file from IndexedDB
    await deleteFileFromIndexedDB(fileId);

    // Delete metadata from Firestore
    if (docId) {
      const fileDocRef = doc(db, FILES_COLLECTION, docId);
      await deleteDoc(fileDocRef);
    }

    return {
      success: true,
      message: 'File deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting file:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Read a file as data URL
 * @param {File} file - The file to read
 * @returns {Promise<string>} - The file as data URL
 */
const readFileAsDataURL = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      resolve(reader.result);
    };

    reader.onerror = (error) => {
      console.error('Error reading file:', error);
      reject(error);
    };

    reader.readAsDataURL(file);
  });
};
