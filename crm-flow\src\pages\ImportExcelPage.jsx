import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import MainLayout from '../components/Layout/MainLayout';
import ExcelImporter from '../components/Importers/ExcelImporter';
import 'bootstrap/dist/css/bootstrap.min.css';

const ImportExcelPage = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  
  return (
    <MainLayout>
      <div className="container py-4">
        <div className="row">
          <div className="col-12">
            <h1 className="mb-4">{t('plugins.importExcel.title')}</h1>
            
            <div className="alert alert-info mb-4">
              <h5><i className="bi bi-info-circle me-2"></i>Import Instructions</h5>
              <p>
                You can import opportunities from Excel files (.xlsx, .xls). 
                The importer will allow you to map Excel columns to opportunity fields.
              </p>
              <p>
                Required fields: Name (all other fields are optional)
              </p>
              <div className="mt-3">
                <h6>Steps to Import:</h6>
                <ol className="mb-0">
                  <li>Select your Excel file</li>
                  <li>Choose the sheet containing your data</li>
                  <li>Verify the header row number (usually 1)</li>
                  <li>Map Excel columns to opportunity fields</li>
                  <li>Click "Import Opportunities" to begin the import</li>
                </ol>
              </div>
            </div>
            
            <ExcelImporter />
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default ImportExcelPage;
