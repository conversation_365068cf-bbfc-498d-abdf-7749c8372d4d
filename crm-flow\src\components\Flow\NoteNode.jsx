import React, { useState, useRef, useEffect } from 'react';
import { Handle, Position } from '@xyflow/react';
import NodeMenu from './NewNodes/NodeMenu';
import './NoteNodeStyles.css';

const NoteNode = ({ id, data, selected, isConnectable }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [noteText, setNoteText] = useState(data.content || '');
  const textareaRef = useRef(null);

  // Initialize with data content
  useEffect(() => {
    setNoteText(data.content || '');
  }, [data.content]);

  // Focus textarea when editing starts
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isEditing]);

  // Handle double click to start editing
  const handleDoubleClick = (e) => {
    e.stopPropagation();
    setIsEditing(true);
  };

  // Handle text change
  const handleTextChange = (e) => {
    setNoteText(e.target.value);
  };

  // Handle save
  const handleSave = () => {
    if (data.onContentChange) {
      data.onContentChange(id, noteText);
    }
    setIsEditing(false);
  };

  // Handle blur (save on blur)
  const handleBlur = () => {
    handleSave();
  };

  // Handle key down (save on Ctrl+Enter or Escape)
  const handleKeyDown = (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setNoteText(data.content || '');
      setIsEditing(false);
    }
  };

  // Handle node actions
  const handleDelete = () => {
    if (data.onDelete) data.onDelete(id);
  };

  const handleArchive = () => {
    if (data.onArchive) data.onArchive(id);
  };

  const handleRestore = () => {
    if (data.onRestore) data.onRestore(id);
  };

  return (
    <div
      className={`note-node ${selected ? 'selected' : ''} ${data.archived ? 'node-archived' : ''}`}
      onDoubleClick={handleDoubleClick}
    >
      <NodeMenu
        onEdit={() => setIsEditing(true)}
        onDelete={handleDelete}
        onArchive={handleArchive}
        isArchived={data.archived}
        onRestore={handleRestore}
        nodeType="note"
      />
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        className="note-handle"
      />

      <div className="note-header">
        <i className="bi bi-sticky"></i>
        <span className="note-title">Note</span>
        {!isEditing && (
          <button
            className="note-edit-button"
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
            title="Edit note"
          >
            <i className="bi bi-pencil-fill"></i>
          </button>
        )}
      </div>

      <div className="note-content">
        {isEditing ? (
          <textarea
            ref={textareaRef}
            value={noteText}
            onChange={handleTextChange}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className="note-textarea"
            placeholder="Enter your note here..."
            autoFocus
          />
        ) : (
          <div className="note-text">
            {noteText ? (
              noteText.split('\n').map((line, i) => (
                <React.Fragment key={i}>
                  {line}
                  {i < noteText.split('\n').length - 1 && <br />}
                </React.Fragment>
              ))
            ) : (
              <span className="note-placeholder">Double-click to add a note</span>
            )}
          </div>
        )}
      </div>

      <Handle
        type="source"
        position={Position.Right}
        isConnectable={isConnectable}
        className="note-handle"
      />
    </div>
  );
};

export default NoteNode;
