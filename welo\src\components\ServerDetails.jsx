import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tabs,
  Tab,
  Box,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  List,
  ListItem,
  ListItemText
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  Close as CloseIcon,
  Circle as CircleIcon
} from '@mui/icons-material'
import { queryServer, getServerHistory } from '../services/api'

const ServerDetails = ({ server, onClose, onServerUpdated }) => {
  const [serverData, setServerData] = useState(null)
  const [history, setHistory] = useState([])
  const [loading, setLoading] = useState(true)
  const [querying, setQuerying] = useState(false)
  const [activeTab, setActiveTab] = useState('info')

  useEffect(() => {
    loadServerDetails()
  }, [server.id])

  const loadServerDetails = async () => {
    try {
      setLoading(true)
      const [historyData] = await Promise.all([
        getServerHistory(server.id, 20)
      ])
      setHistory(historyData)
    } catch (error) {
      console.error('Failed to load server details:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleQuery = async () => {
    try {
      setQuerying(true)
      const result = await queryServer(server.id)
      setServerData(result)
      onServerUpdated()
      loadServerDetails() // Refresh history
    } catch (error) {
      console.error('Failed to query server:', error)
    } finally {
      setQuerying(false)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString()
  }

  const renderPlayerList = () => {
    if (!serverData?.players || serverData.players.length === 0) {
      return (
        <Typography variant="body1" color="text.secondary" textAlign="center" py={4}>
          No players online
        </Typography>
      )
    }

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Players ({serverData.players.length})
        </Typography>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell align="center">Frags</TableCell>
                <TableCell align="center">Time</TableCell>
                <TableCell align="center">Ping</TableCell>
                <TableCell>Team</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {serverData.players.map((player, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {player.name}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">{player.frags}</TableCell>
                  <TableCell align="center">{Math.floor(player.time / 60)}m</TableCell>
                  <TableCell align="center">{player.ping}ms</TableCell>
                  <TableCell>{player.team || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    )
  }

  const renderServerInfo = () => {
    const latestHistory = history[0]
    const info = latestHistory ? JSON.parse(latestHistory.server_info || '{}') : {}

    const infoItems = [
      { label: 'Status', value: server.is_online ? 'Online' : 'Offline', color: server.is_online ? 'success' : 'error' },
      { label: 'Address', value: `${server.address}:${server.port}` },
      { label: 'Ping', value: server.ping ? `${server.ping}ms` : '-' },
      { label: 'Players', value: server.player_count !== null ? `${server.player_count}/${server.max_players || '?'}` : '-' },
      { label: 'Map', value: server.map_name || '-' },
      { label: 'Hostname', value: info.hostname || '-' },
      { label: 'Game', value: info.gamedir || '-' },
      { label: 'Version', value: info.version || '-' },
    ]

    return (
      <Grid container spacing={2}>
        {infoItems.map((item, index) => (
          <Grid item xs={12} sm={6} key={index}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="caption" color="text.secondary" display="block">
                {item.label}
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {item.color ? (
                  <Chip
                    label={item.value}
                    color={item.color}
                    size="small"
                    icon={<CircleIcon />}
                  />
                ) : (
                  item.value
                )}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
    )
  }

  const renderHistory = () => {
    if (history.length === 0) {
      return (
        <Typography variant="body1" color="text.secondary" textAlign="center" py={4}>
          No history available
        </Typography>
      )
    }

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Recent History
        </Typography>
        <List>
          {history.map((entry) => (
            <ListItem key={entry.id} divider>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    <CircleIcon
                      sx={{
                        color: entry.is_online ? '#4CAF50' : '#f44336',
                        fontSize: 16
                      }}
                    />
                    <Typography variant="body2">
                      {formatDate(entry.checked_date)}
                    </Typography>
                  </Box>
                }
                secondary={
                  <Box display="flex" gap={1} flexWrap="wrap" mt={0.5}>
                    {entry.ping && (
                      <Chip label={`${entry.ping}ms`} size="small" variant="outlined" />
                    )}
                    {entry.player_count !== null && (
                      <Chip label={`${entry.player_count} players`} size="small" variant="outlined" />
                    )}
                    {entry.map_name && (
                      <Chip label={entry.map_name} size="small" variant="outlined" />
                    )}
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      </Box>
    )
  }

  return (
    <Dialog
      open={true}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '500px' }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">{server.name}</Typography>
          <Chip
            icon={<CircleIcon />}
            label={server.is_online ? 'Online' : 'Offline'}
            color={server.is_online ? 'success' : 'error'}
            size="small"
          />
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 2 }}>
          <Tab label="Server Info" value="info" />
          <Tab label="Players" value="players" />
          <Tab label="History" value="history" />
        </Tabs>

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
            <Typography variant="body1" sx={{ ml: 2 }}>Loading...</Typography>
          </Box>
        ) : (
          <>
            {activeTab === 'info' && renderServerInfo()}
            {activeTab === 'players' && renderPlayerList()}
            {activeTab === 'history' && renderHistory()}
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button
          startIcon={querying ? <CircularProgress size={16} /> : <RefreshIcon />}
          onClick={handleQuery}
          disabled={querying}
        >
          {querying ? 'Querying...' : 'Refresh'}
        </Button>
        <Button startIcon={<CloseIcon />} onClick={onClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ServerDetails
