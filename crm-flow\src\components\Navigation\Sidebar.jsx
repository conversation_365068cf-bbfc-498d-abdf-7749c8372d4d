import { useNavigate, NavLink } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';

import 'bootstrap/dist/css/bootstrap.min.css';
import './SidebarStyles.css';

const Sidebar = ({ collapsed, setCollapsed }) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const toggleSidebar = () => {
    const newCollapsedState = !collapsed;
    setCollapsed(newCollapsedState);
  };

  return (
    <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header">
        <div className="logo-container">
          {!collapsed && (
            <div className="app-logo">
              <i className="bi bi-diagram-3-fill"></i>
              <span className="app-name">CRM Flow</span>
            </div>
          )}
        </div>
        <button className="toggle-btn" onClick={toggleSidebar} title={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}>
          <i className={`bi ${collapsed ? 'bi-chevron-right' : 'bi-chevron-left'}`}></i>
        </button>
      </div>

      <div className="sidebar-menu">
        <ul className="nav-list">
          <li className="nav-item">
            <NavLink
              className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              to="/dashboard"
              title={t('navigation.dashboard')}
            >
              <i className="bi bi-speedometer2"></i>
              {!collapsed && <span>{t('navigation.dashboard')}</span>}
            </NavLink>
          </li>


          <li className="nav-item">
            <NavLink
              className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              to="/opportunities"
              title={t('navigation.opportunities')}
            >
              <i className="bi bi-graph-up"></i>
              {!collapsed && <span>{t('navigation.opportunities')}</span>}
            </NavLink>
          </li>

          <li className="nav-item">
            <NavLink
              className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              to="/flow"
              title={t('navigation.flow')}
            >
              <i className="bi bi-diagram-3"></i>
              {!collapsed && <span>{t('navigation.flow')}</span>}
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink
              className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              to="/organization"
              title={t('navigation.organizations')}
            >
              <i className="bi bi-people"></i>
              {!collapsed && <span>{t('navigation.organizations')}</span>}
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink
              className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              to="/contacts"
              title={t('navigation.contacts')}
            >
              <i className="bi bi-person"></i>
              {!collapsed && <span>{t('navigation.contacts')}</span>}
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink
              className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              to="/plugins"
              title={t('navigation.plugins')}
            >
              <i className="bi bi-puzzle"></i>
              {!collapsed && <span>{t('navigation.plugins')}</span>}
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink
              className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              to="/user-settings"
              title={t('settings.title')}
            >
              <i className="bi bi-person-gear"></i>
              {!collapsed && <span>{t('settings.title')}</span>}
            </NavLink>
          </li>
        </ul>
      </div>

      <div className="sidebar-user">
        {currentUser && (
          <div className="user-info">
            {currentUser.photoURL ? (
              <img
                src={currentUser.photoURL}
                alt="User Avatar"
                className="user-avatar"
                onClick={() => navigate('/user-settings')}
                style={{ cursor: 'pointer' }}
                title="Edit profile"
              />
            ) : (
              <div
                className="user-avatar-placeholder"
                onClick={() => navigate('/user-settings')}
                style={{ cursor: 'pointer' }}
                title="Edit profile"
              >
                {(currentUser.displayName || currentUser.email || '?').charAt(0).toUpperCase()}
              </div>
            )}
            {!collapsed && (
              <div className="user-details">
                <div className="user-name">{currentUser.displayName || currentUser.email.split('@')[0]}</div>
                <div className="user-email">{currentUser.email}</div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
