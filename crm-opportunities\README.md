# Enhanced CRM Dashboard

A cutting-edge CRM application built with React, featuring a modular node system, Google OAuth authentication, and advanced XY Flow visualization. This application provides a comprehensive platform for managing sales opportunities, companies, contacts, tasks, meetings, and notes through an interactive visual interface with edge-drop node creation capabilities.

## 🚀 New Features

### 🔐 **Google OAuth Authentication** ✅ Ready!
- **Pre-configured**: Google OAuth Client ID already set up
- **Real Authentication**: Sign in with your Google account
- **Demo Mode**: Instant access without Google account
- **Persistent Sessions**: Login state saved between visits
- **User Profile**: Display with avatar and logout functionality

### 🎯 **Modular Node System**
- **Property-Based Architecture**: All nodes constructed from arrays of properties
- **Universal Node Component**: Single component renders all node types
- **Streamlined Node Creation**: Easy system to add new node types
- **Six Built-in Node Types**: Opportunity, Company, Contact, Task, Meeting, Note

### 🔗 **Edge-Drop Node Creation**
- **Interactive Edge Creation**: Drag from node handles and drop to create new connected nodes
- **Modal Node Selection**: Choose node type via popup modal when dropping edges
- **Automatic Connections**: New nodes automatically connect to source nodes

### 📊 **Node Creation Dashboard**
- **Quick Create Interface**: Rapid node creation with visual type selection
- **Node Type Management**: View and manage all available node types
- **Property Inspection**: Detailed view of node properties and configurations
- **Category Organization**: Nodes organized by categories (sales, contacts, productivity, etc.)

### 🎨 **Enhanced User Experience**
- **No Hover Animations**: Clean, static interface as requested
- **Double-Click Creation**: Double-click empty space to create nodes
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Visual Instructions**: Built-in help panel with usage tips

## 🏗 **Architecture**

### **Node System Architecture**
```
BaseNode Class
├── Properties Array (key-value pairs)
├── Position Management
├── Type Definition
└── Flow Node Conversion

NodeTypeRegistry
├── Property Definitions
├── Node Type Configurations
├── Category Management
└── Creation Factory

UniversalNode Component
├── Dynamic Rendering
├── Type-Specific Styling
├── Property Display Logic
└── Icon Management
```

### **Available Node Types**

| Type | Category | Description | Key Properties |
|------|----------|-------------|----------------|
| **Opportunity** | Sales | Sales pipeline tracking | Title, Value, Stage, Probability, Close Date |
| **Company** | Contacts | Organization management | Name, Industry, Website, Size |
| **Contact** | Contacts | Individual contacts | First/Last Name, Email, Position, LinkedIn |
| **Task** | Productivity | Action items | Title, Status, Priority, Due Date |
| **Meeting** | Activities | Meetings and calls | Title, Date, Duration, Type, Location |
| **Note** | General | Text notes and memos | Title, Content, Tags |

## 🛠 **Technology Stack**

- **Frontend**: React 19 with JSX
- **Visualization**: XY Flow (React Flow) v12+ for interactive diagrams
- **Authentication**: Google OAuth with Google Identity Services
- **Icons**: Lucide React for consistent iconography
- **Styling**: CSS-in-JS with responsive design
- **Data Storage**: localStorage with SQLite simulation
- **Build Tool**: Vite for fast development and building
- **State Management**: React hooks with localStorage persistence

## 🚀 **Getting Started**

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager
- Google OAuth Client ID (optional, demo mode available)

### Installation

1. **Navigate to project directory**
   ```bash
   cd crm-opportunities
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Authentication Ready!**
   - Google OAuth is pre-configured and ready to use
   - Click "Sign in with Google" for real authentication
   - Or use "Demo Mode" button for instant access

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open application**
   Navigate to `http://localhost:5173/`

## 📖 **Usage Guide**

### **Authentication**
1. Click "Sign in with Google" (or use demo mode)
2. Authenticate with your Google account
3. Access the enhanced CRM dashboard

### **Creating Nodes**
- **Double-click**: Double-click empty space to open node selector
- **Dashboard**: Click "Dashboard" button for advanced creation interface
- **Edge-drop**: Drag from node handle and drop on empty space
- **Quick create**: Use + buttons in dashboard for instant creation

### **Managing Connections**
- **Connect nodes**: Drag from source handle to target handle
- **Edge-drop creation**: Drag from handle and drop to create connected node
- **Visual feedback**: Connection lines show relationships

### **Node Interaction**
- **Select**: Click any node to view details
- **Move**: Drag nodes to reposition (positions auto-save)
- **Edit**: Use edit buttons in detail panels
- **Delete**: Remove nodes via detail panels

### **Dashboard Features**
- **Quick Create Tab**: Rapid node creation by category
- **Manage Types Tab**: View all node type configurations
- **Property Inspector**: Detailed property definitions
- **Category Filtering**: Organize by node categories

## 🎯 **Advanced Features**

### **Adding New Node Types**
```javascript
// Register a new node type
nodeRegistry.registerNodeType('custom', {
  label: 'Custom Node',
  description: 'Your custom node type',
  icon: 'Star',
  color: '#8b5cf6',
  category: 'custom',
  properties: [
    new PropertyDefinition('name', 'Name', 'text', '', { required: true }),
    new PropertyDefinition('value', 'Value', 'number', 0)
  ]
});
```

### **Property Types**
- `text`: Text input
- `number`: Numeric input
- `date`: Date picker
- `select`: Dropdown selection
- `boolean`: Checkbox
- `currency`: Currency input
- `percentage`: Percentage input

### **Data Persistence**
- **Opportunities**: Stored in localStorage database simulation
- **Other Nodes**: Stored in localStorage as JSON
- **Edges**: Stored in localStorage
- **User Sessions**: Persistent login state

## 🔧 **Development**

### **Project Structure**
```
src/
├── components/           # React components
│   ├── CRMDashboard.jsx     # Main dashboard
│   ├── EnhancedFlow.jsx     # Enhanced flow component
│   ├── UniversalNode.jsx    # Universal node renderer
│   ├── NodeTypeSelector.jsx # Node type selection modal
│   ├── NodeCreationDashboard.jsx # Creation dashboard
│   └── GoogleAuth.jsx       # Authentication component
├── systems/              # Core systems
│   └── NodeSystem.js        # Node system architecture
├── database/             # Data layer
│   └── schema.js            # localStorage database
├── services/             # Business logic
│   └── database.js          # CRUD operations
└── App.jsx              # Main application
```

### **Key Design Principles**
1. **Modularity**: All nodes follow same property-based pattern
2. **Extensibility**: Easy to add new node types
3. **Consistency**: Universal rendering with type-specific styling
4. **Performance**: Efficient state management and rendering
5. **User Experience**: Intuitive interactions and visual feedback

## 🚀 **Future Enhancements**

- **Real Database Integration**: PostgreSQL/MongoDB backend
- **Team Collaboration**: Multi-user support and permissions
- **Advanced Analytics**: Reporting and dashboard analytics
- **Mobile App**: React Native mobile application
- **API Integration**: External service connections
- **Workflow Automation**: Automated task and process management
- **Custom Fields**: User-defined property types
- **Import/Export**: Data exchange capabilities

## 📝 **License**

This project is for demonstration purposes. Feel free to use and modify as needed.

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Test thoroughly
5. Submit a pull request

## 🔧 **Troubleshooting**

### Common Issues

**React Infinite Loop Errors**
- Fixed: useCallback implementation prevents infinite re-renders
- Error boundary catches and handles unexpected errors gracefully

**Google OAuth Issues**
- Use "Demo Mode" button for instant access without configuration
- Follow `GOOGLE_OAUTH_SETUP.md` for production OAuth setup
- Check browser console for specific OAuth errors

**Performance Issues**
- All components optimized with proper React hooks
- localStorage operations are efficient for demo data sizes
- XY Flow handles large node datasets efficiently

**Browser Compatibility**
- Modern browsers (Chrome, Firefox, Safari, Edge)
- JavaScript enabled required
- localStorage support required

### Getting Help

1. Check browser console for error messages
2. Try "Demo Mode" if authentication issues occur
3. Refresh the page if components seem unresponsive
4. Clear localStorage to reset all data: `localStorage.clear()`

## 📞 **Support**

For questions or issues, please refer to the documentation or create an issue in the project repository.
