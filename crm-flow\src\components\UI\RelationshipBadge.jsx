import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';

/**
 * A badge component that displays a relationship between entities
 *
 * @param {Object} props - Component props
 * @param {string} props.type - The type of relationship (e.g., "Connected to", "Owned by")
 * @param {string} props.entityType - The type of the related entity (e.g., "contact", "company")
 * @param {string} props.entityId - The ID of the related entity
 * @param {string} props.entityName - The name of the related entity to display
 * @param {boolean} props.hideBreadcrumbs - Whether to hide the breadcrumbs styling
 * @returns {JSX.Element} - The relationship badge component
 */
const RelationshipBadge = ({ type, entityType, entityId, entityName, hideBreadcrumbs = false }) => {
  // Determine badge color based on entity type
  const getBadgeColor = () => {
    switch (entityType) {
      case 'opportunity':
        return 'bg-primary';
      case 'contact':
        return 'bg-info';
      case 'company':
        return 'bg-success';
      case 'task':
        return 'bg-warning';
      default:
        return 'bg-secondary';
    }
  };

  // Determine the URL for the entity
  const getEntityUrl = () => {
    switch (entityType) {
      case 'opportunity':
        return `/opportunities/${entityId}`;
      case 'contact':
        return `/contacts/${entityId}`;
      case 'company':
        return `/companies/${entityId}`;
      case 'task':
        return `/tasks/${entityId}`;
      default:
        return '#';
    }
  };

  // Determine the icon for the entity type
  const getEntityIcon = () => {
    switch (entityType) {
      case 'opportunity':
        return 'bi-graph-up';
      case 'contact':
        return 'bi-person';
      case 'company':
        return 'bi-building';
      case 'task':
        return 'bi-check2-square';
      default:
        return 'bi-link';
    }
  };

  // If hideBreadcrumbs is true, just show the badge without the breadcrumb container
  if (hideBreadcrumbs) {
    return (
      <div className="d-inline-block me-2 mb-2">
        <Link
          to={getEntityUrl()}
          className={`badge ${getBadgeColor()} text-decoration-none d-inline-flex align-items-center`}
        >
          <i className={`bi ${getEntityIcon()} me-1`}></i>
          {entityName || 'Unknown'}
        </Link>
      </div>
    );
  }

  // Otherwise, show the full breadcrumb container
  return (
    <div className="relationship-badge d-inline-block me-2 mb-2">
      <div className="breadcrumb-container">
        <div className="relationship-type">
          <small className="text-muted">{type}</small>
        </div>
        <div className="relationship-arrow">
          <i className="bi bi-chevron-right text-muted"></i>
        </div>
        <div className="relationship-entity">
          <Link
            to={getEntityUrl()}
            className={`badge ${getBadgeColor()} text-decoration-none d-inline-flex align-items-center`}
          >
            <i className={`bi ${getEntityIcon()} me-1`}></i>
            {entityName || 'Unknown'}
          </Link>
        </div>
      </div>
    </div>
  );
};

RelationshipBadge.propTypes = {
  type: PropTypes.string.isRequired,
  entityType: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  entityName: PropTypes.string,
  hideBreadcrumbs: PropTypes.bool
};

export default RelationshipBadge;
