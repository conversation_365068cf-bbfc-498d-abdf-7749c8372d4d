import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy, deleteDoc, doc, updateDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import { useAuth } from '../contexts/AuthContext';
import { getUserData } from '../services/dataAccessService';
import { useNavigate } from 'react-router-dom';
import Modal from '../components/UI/Modal';
import CompanyForm from '../components/Forms/CompanyForm';
import 'bootstrap/dist/css/bootstrap.min.css';

const CompaniesPage = () => {
  const { currentUser, organization } = useAuth();
  const navigate = useNavigate();
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentCompany, setCurrentCompany] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusMessage, setStatusMessage] = useState('');

  // Fetch companies
  const fetchCompanies = async () => {
    try {
      setLoading(true);

      // Get organization ID if available
      const orgId = organization?.id || null;

      // Use the getUserData function to get companies
      const companiesList = await getUserData('companies', currentUser.uid, orgId);

      // Sort companies by name
      const sortedCompanies = companiesList.sort((a, b) => {
        return (a.name || '').localeCompare(b.name || '');
      });

      console.log('Fetched companies:', sortedCompanies);
      setCompanies(sortedCompanies);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching companies:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchCompanies();
    }
  }, [currentUser, organization]);

  // Handle sending company to flow view
  const handleSendToFlow = async (company) => {
    try {
      const companyRef = doc(db, 'companies', company.id);

      // Update data to include organization context
      const updateData = {
        archived: false, // Make sure it's not archived
        updatedAt: new Date()
      };

      // If part of an organization, ensure the organizationId is set
      if (organization?.id) {
        updateData.organizationId = organization.id;
      }

      // Update the document
      await updateDoc(companyRef, updateData);

      setStatusMessage(`Company "${company.name}" sent to flow view successfully!`);

      // Refresh companies list
      fetchCompanies();

      // Clear status message after 3 seconds
      setTimeout(() => {
        setStatusMessage('');
      }, 3000);
    } catch (error) {
      console.error('Error sending company to flow:', error);
      setStatusMessage(`Error: ${error.message}`);
    }
  };

  // Handle company deletion
  const handleDeleteCompany = async (id) => {
    if (window.confirm('Are you sure you want to delete this company?')) {
      try {
        await deleteDoc(doc(db, 'companies', id));
        // Refresh the companies list
        fetchCompanies();
      } catch (error) {
        console.error('Error deleting company:', error);
      }
    }
  };

  // Handle company edit
  const handleEditCompany = (company) => {
    setCurrentCompany(company);
    setShowEditModal(true);
  };

  // This function has been replaced by the new handleSendToFlow function above

  // Filter companies based on search term
  const filteredCompanies = companies.filter(company =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (company.industry && company.industry.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (company.website && company.website.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div style={{ height: '100%', width: '100%', overflow: 'auto', padding: '20px' }}>
      <div className="container-fluid">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h2>Companies</h2>
          <button
            className="btn btn-info"
            onClick={() => setShowAddModal(true)}
          >
            <i className="bi bi-building-add me-2"></i>
            Add Company
          </button>
        </div>

        {/* Status Message */}
        {statusMessage && (
          <div className="alert alert-info alert-dismissible fade show mb-3" role="alert">
            {statusMessage}
            <button type="button" className="btn-close" onClick={() => setStatusMessage('')} aria-label="Close"></button>
          </div>
        )}

        {/* Search Bar */}
        <div className="row mb-4">
          <div className="col-md-6">
            <div className="input-group">
              <span className="input-group-text">
                <i className="bi bi-search"></i>
              </span>
              <input
                type="text"
                className="form-control"
                placeholder="Search companies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="btn btn-outline-secondary"
                  type="button"
                  onClick={() => setSearchTerm('')}
                >
                  <i className="bi bi-x"></i>
                </button>
              )}
            </div>
          </div>
        </div>

        {loading ? (
          <div className="d-flex justify-content-center mt-5">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : (
          <>
            {filteredCompanies.length === 0 ? (
              <div className="alert alert-info">
                {searchTerm ? 'No companies match your search.' : 'No companies found. Add your first company!'}
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-sm table-hover">
                  <thead className="table-light">
                    <tr>
                      <th>Name</th>
                      <th style={{ width: '120px' }}>Industry</th>
                      <th style={{ width: '80px' }}>Size</th>
                      <th style={{ width: '180px' }}>Website</th>
                      <th style={{ width: '140px' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCompanies.map(company => (
                      <tr key={company.id}>
                        <td>{company.name}</td>
                        <td><small>{company.industry || '-'}</small></td>
                        <td><small>{company.size || '-'}</small></td>
                        <td>
                          <small>
                            {company.website ? (
                              <a href={company.website.startsWith('http') ? company.website : `https://${company.website}`} target="_blank" rel="noopener noreferrer">
                                {company.website}
                              </a>
                            ) : (
                              '-'
                            )}
                          </small>
                        </td>
                        <td>
                          <div className="btn-group btn-group-sm">
                            <button
                              className="btn btn-sm btn-outline-primary"
                              title="Edit"
                              onClick={() => handleEditCompany(company)}
                            >
                              <i className="bi bi-pencil"></i>
                            </button>
                            <button
                              className="btn btn-sm btn-outline-success"
                              title="Send to Flow"
                              onClick={() => handleSendToFlow(company)}
                            >
                              <i className="bi bi-diagram-3"></i>
                            </button>
                            <button
                              className="btn btn-sm btn-outline-danger"
                              title="Delete"
                              onClick={() => handleDeleteCompany(company.id)}
                            >
                              <i className="bi bi-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </>
        )}
      </div>

      {/* Add Company Modal */}
      <Modal
        show={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="lg"
      >
        <CompanyForm
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            fetchCompanies();
          }}
        />
      </Modal>

      {/* Edit Company Modal */}
      <Modal
        show={showEditModal}
        onClose={() => setShowEditModal(false)}
        size="lg"
      >
        {currentCompany && (
          <CompanyForm
            onClose={() => setShowEditModal(false)}
            onSuccess={() => {
              setShowEditModal(false);
              fetchCompanies();
            }}
            companyData={currentCompany}
            isEditing={true}
          />
        )}
      </Modal>
    </div>
  );
};

export default CompaniesPage;
