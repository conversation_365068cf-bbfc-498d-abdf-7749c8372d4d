/**
 * Production Server Starter
 *
 * This script starts a simple Express server to serve the production build.
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const os = require('os');
const app = express();
const port = process.env.PORT || 5173; // Use the same port as Vite dev server

// Check if dist directory exists (Vite uses 'dist' instead of 'build')
if (!fs.existsSync(path.join(__dirname, 'dist'))) {
  console.error('\x1b[31m%s\x1b[0m', 'Error: The "dist" directory does not exist.');
  console.log('\x1b[33m%s\x1b[0m', 'Please run "npm run build:prod" first to create a production build.');
  process.exit(1);
}

// Serve static files from the Vite build
app.use(express.static(path.join(__dirname, 'dist')));

// Handle all other requests by serving the app's index.html (for client-side routing)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Start the server
app.listen(port, '0.0.0.0', () => {
  console.log(`
╔════════════════════════════════════════════════════════════╗
║                                                            ║
║   CRM Flow Server is running!                              ║
║                                                            ║
║   - Local:            http://localhost:${port}                 ║
║   - On Your Network:  http://${getLocalIp()}:${port}      ║
║                                                            ║
║   To access from the internet, follow the instructions     ║
║   in the SELF_HOSTING_GUIDE.md file.                       ║
║                                                            ║
╚════════════════════════════════════════════════════════════╝
`);
});

// Helper function to get local IP address
function getLocalIp() {
  const nets = os.networkInterfaces();

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // Skip over non-IPv4 and internal (loopback) addresses
      if (net.family === 'IPv4' && !net.internal) {
        return net.address;
      }
    }
  }
  return '127.0.0.1';
}
